# CineSend Theme

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

This uses [Tailwind CSS](https://tailwindcss.com/docs/installation).

## Local

To run locally:

`yarn dev`

## Build and deploy to GitHub Pages

This project lives in sample / demo form on [Github](https://bitcine.github.io/cinesend-theme).

It builds the sample page as used in development purposes.

To build and deploy to GitHub Pages, run:

`yarn gh-deploy`

## Build and deploy to NPM

This project exists as an [NPM package](https://www.npmjs.com/package/@bitcine/cinesend-theme).

To build and deploy to NPM, run:

`yarn npm-build`

`yarn publish`

Specify a new major, minor, or patch version.

## Using the NPM package

To use this package in other products, run:

`yarn add @bitcine/cinesend-theme`

And add it to a React component:

```
import React from 'react'
import { Button } from '@bitcine/cinesend-theme'

const App = () =>
    <Button
        type="error"
        size="large">
        Cancel subscription
    </Button>

export default App
```

## Typescript typings support

This library exposes type declarations via an `index.d.ts` file in the root of the folder for an exported component. To add types to a component that doesn't already have them, create a new `index.d.ts` file. Add the expected type declarations for the props of the component being exported. Be mindful of the types file when updating existing components with new prop parameters. 

```
import React from 'react'

interface Props {
    // The prop key expects a string - trying to assign a different value will result in an error
    name: string
    // The prop length makes use of the `?` operator - also known as an optional. This is short hand for typing it as `number | undefined`
    length?: number
    // The prop onClick takes a callback which you also need to declare the return type of
    onClick: () => void
    // The prop data expects an array that is populated with objects. The objects inside the array have a string for a key but the value is unknown
    data: [Record<string, unknown>]

}

declare const <COMPONENT NAME GOES HERE>: React.FunctionComponent<Props>

export default <COMPONENT NAME GOES HERE>

```

Once the `index.d.ts` file has been created, add the following line to the `index.d.ts` file found in `src/components` to expose the types at build time for external use. 

`/// <reference path="<PATH TO NEW index.d.ts FILE" />` 
