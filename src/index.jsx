import React from 'react';
import { createRoot } from 'react-dom/client';
import { HashRouter } from 'react-router-dom';
import './components/index.css';

// Import the entry point and render it's default export
import('./demo/index.jsx').then(({ default: Environment }) => {
  const container = document.getElementById("root")
  const root = createRoot(container);
  root.render(
    <React.StrictMode>
      <HashRouter>
        <Environment />
      </HashRouter>
    </React.StrictMode>
  );
});