import React, { useState } from 'react'
import Showcase from '../showcase'
import { Select } from "../../components"
import { Star } from "../../components/icons";

const SelectExamples = () => {
  const options = [
    { value: 'chocolate', label: 'Chocolate' },
    { value: 'strawberry', label: 'Strawberry' },
    { value: 'vanilla', label: 'Vanilla' },
    { value: 'banana', label: 'Disabled Banana', disabled: true }
  ]

  const [selected, setSelected] = useState({
    0: "",
    1: "",
    2: "",
    3: "",
    4: "",
    5: "",
    6: options[0],
    7: "",
    8: "",
    9: "",
    10: [options[0], options[1]],
    11: "",
    12: "",
    13: "",
    14: [options[0], options[1]],
    15: [options[0], options[1]],
    16: "",
    17: ""
  })

  return (
    <Showcase
      name="Select"
      examples={
        <div className='space-y-4'>
          {[
            { id: 'select-1', placeholder: 'Placeholder' },
            { id: 'select-2', label: 'Success Full Width', placeholder: 'Placeholder', success: true, width: 'w-full' },
            { id: 'select-3', label: 'Error', placeholder: 'Placeholder', error: true },
            { id: 'select-4', label: 'Disabled', placeholder: 'Placeholder', disabled: true },
            { id: 'select-5', label: 'Clearable', placeholder: 'Placeholder', isClearable: true },
            { id: 'select-6', label: 'Icon', placeholder: 'Placeholder', icon: Star },
            { id: 'select-7', label: 'Default Value', placeholder: 'Placeholder' },
            { id: 'select-8', label: 'Multi Select', placeholder: 'Placeholder', isMulti: true },
            { id: 'select-9', label: 'Clearable Multi Select', placeholder: 'Placeholder', isClearable: true, isMulti: true },
            { id: 'select-10', label: 'Icon Multi Select', placeholder: 'Placeholder', icon: Star, isMulti: true },
            { id: 'select-11', label: 'Default Value Multi Select', placeholder: 'Placeholder', isMulti: true, width: 'w-full' },
            { id: 'select-12', label: 'With Text and Top Icon Right', placeholder: 'Placeholder', description: "Description", message: "Neutral Message", topIcon: Star, topIconPosition: "right", bottomIcon: Star, bottomIconPosition: "left", isClearable: true },
            { id: 'select-13', label: 'Multi With Text', placeholder: 'Placeholder', description: "Description", message: "Neutral Message", topIcon: Star, topIconPosition: "left", bottomIcon: Star, bottomIconPosition: "right", isClearable: true, isMulti: true, width: 'w-full' },
            { id: 'select-14', label: 'Creatable', placeholder: 'Placeholder', description: "Description", message: "Neutral Message", isClearable: true, isCreatable: true, width: 'w-full' },
            { id: 'select-15', label: 'Sortable', placeholder: 'Placeholder', description: "Description", message: "Neutral Message", topIcon: Star, topIconPosition: "left", bottomIcon: Star, bottomIconPosition: "right", isClearable: true, isMulti: true, isSortable: true, width: 'w-full' },
            { id: 'select-16', label: 'Sortable Creatable', placeholder: 'Placeholder', description: "Description", message: "Neutral Message", topIcon: Star, topIconPosition: "left", bottomIcon: Star, bottomIconPosition: "right", isClearable: true, isMulti: true, isCreatable: true, isSortable: true, width: 'w-full' },
            { id: 'select-17', label: 'Select with padding', className: 'py-5' },
            { id: 'select-18', label: 'Sortable and Creatable with no dropdown', isSortable: true, isCreatable: true, isMulti: true, hideDropdown: true, placeholder: "Enter a new option" },
          ].map((input, i) =>
            <div key={i}>
              <Select
                options={options}
                value={selected[i]}
                onChange={(select) => setSelected({ ...selected, [i]: select})}
                onCreateOption={label => {
                  setSelected({
                    ...selected,
                    [i]: [...selected[i], { value: label, label }]
                  })
                }}
                {...input}
              />
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<Select
  id={"unique-id"}                                // required
  options={options}                               // required, list of { value: '', label: '' }
  value={options[x]}                              // default null, single value or multiple when multi select
  placeholder={text}                              // default no placeholder
  onChange={(input) => setInput(input)}           // default null, accepts variable for new value
  onCreateOption={(input) => createOption(input)} // default null, only relevant for createable
  label={"My label"}                              // default no label
  description={"Custom Description"}              // default no description
  message={"Custom Message"}                      // default no message
  disabled={true}                                 // default false, or true
  success={true}                                  // default false, or true
  error={true}                                    // default false, or true
  icon={Star}                                     // optional icon
  topIcon={Star}                                  // optional label icon
  bottomIcon={Star}                               // optional message icon
  topIconPosition={"label"}                       // default right, or left
  bottomIconPosition={"left"}                     // default left, or right
  isClearable={false}                             // default false, or true
  isMulti={false}                                 // default false, or true
  isCreatable={false}                             // default false, or true
  isSortable={false}                              // default false, or true
  className={'py-5'}                              // default none
/>`

export default SelectExamples
