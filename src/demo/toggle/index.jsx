import React, {useState} from 'react'
import Showcase from '../showcase'
import { Toggle } from '../../components'

const ToggleExamples = () => {
  const [selected, setSelected] = useState({ 0: true, 1: true, 2: true, 3: true })

  return (
    <Showcase
      name="Toggle"
      examples={
        <div className='space-y-2'>
          {[
            { id: "row-1" },
            { id: "row-2", label: "Checked with label" },
            { id: "row-3", label: "Checked with small font", size: "small" },
            { id: "row-4", label: "Checked with large font", size: "large" },
            { id: "row-5", label: "Disabled checked", disabled: true },
            { id: "row-6" },
            { id: "row-7", label: "Unchecked with label" },
            { id: "row-8", label: "Unchecked with small font", size: "small" },
            { id: "row-9", label: "Disabled unchecked", disabled: true },
          ].map((toggle, i) =>
            <div key={i}>
              <Toggle
                checked={selected[i]}
                onChange={() => setSelected({ ...selected, [i]: !selected[i] })}
                {...toggle}
              />
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<Toggle
  checked={checked}                       // required
  onChange={() => setChecked(!checked)}   // required
  id={"unique-id"}                        // required
  disabled={true}                         // default is false
  label={"My label"}                      // default is no label
  size={"small"}                          // default is medium (24px), or small (16px)
/>`

export default ToggleExamples
