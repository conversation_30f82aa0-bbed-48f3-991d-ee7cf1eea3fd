import React, { useState } from 'react'
import Showcase from "../showcase";
import { DatePicker } from "../../components"
import { Star } from "../../components/icons"
import dayjs from 'dayjs'

const DatePickerExample = () => {
  const newDate = new Date()

  const [date, setDate] = useState({
    0: dayjs("2024-05-16T04:00:00.000Z").toDate(),
    1: newDate,
    2: newDate,
    3: newDate,
    4: newDate,
    5: newDate,
    6: newDate,
    7: null,
    8: null,
    9: newDate,
    10: newDate,
    11: newDate,
    12: newDate,
    13: newDate,
    14: newDate,
  });

  return (
    <Showcase
      name="DatePicker"
      examples={
        <div className='space y-2'>
          {[
            { id: "id" },
            { label: "Date Picker with label" },
            { label: "Disabled Date Picker", disabled: true },
            { label: "Date Picker not clearable", isClearable: false },
            { label: "Date Picker with no time select", showTimeSelect: false },
            { label: "Date Picker with custom width", width: 'w-[700px]', showTimeSelect: true },
            { label: "Date Picker with custom width", width: 'w-[200px]' },
            { label: "Date Picker with no set date" },
            { label: "Disabled Date Picker with no set date", disabled: true },
            { label: "Date Picker with Icon", icon: Star },
            { label: "Date Picker with Material Icon", icon: "schedule" },
            { label: "Date Picker with Message", message: { text: "This is a very long message that should extend beyond the input box", className: "text-gray-600 text-sm" } },
            {
              label: "Date Picker with min and max date",
              showTimeSelect: false,
              minDate: dayjs().subtract(3, 'day'),
              maxDate: dayjs().add(3, 'day')
            },
            {
              label: "Time select only",
              showTimeSelectOnly: true,
              minTime: dayjs().hour(9).minute(0),
              maxTime: dayjs().hour(17).minute(0)
            },
            {
              label: "Year Picker and Set Today",
              showTimeSelect: false,
              showJumpToToday: true,
              showJumpToYear: true,
            }
          ].map((input, i) =>
            <div key={i} className={'pt-2'}>
              <DatePicker
                date={date[i]}
                onChange={(newDate) => setDate({ ...date, [i]: newDate })}
                {...input}
              />
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<DatePicker 
    date={date}                               // required, state variable for date
    onChange={(newDate) => setDate(newDate)}  // required, on date change function
    label={"Label for DatePicker"}            // optional
    timeInterval={1}                          // default 5
    disabled={true}                           // default false
    isClearable={false}                       // default is true
    showTimeSelect={false}                    // default is true
    showTimeSelectOnly={false}                // default is false
    width={'w-[350px]'}                       // default is 300px
    icon={Icon}                               // optional icon
    minDate={startDate}                       // optional, disables all previous dates, like: dayjs().subtract(5, "day")
    maxDate={maxDate}                         // optional, disables all future dates, like: dayjs().add(5, "day")
    minTime={minTime}                         // optional, like: dayjs().hour(9).minute(0)
    maxTime={maxTime}                         // optional, like: dayjs().hour(17).minute(0)
    dateFormat={"MMMM d, yyyy"}               // default is "MMMM d, yyyy h:mm aa"
    buttonText={"Set Date"}                   // default is "Click to Set"
    className={'h-max'}                       // default is empty
    message={                                 // default none
      text: "The message",                    // default none
      className: "text-gray-600"              // default 
    }
  />`

export default DatePickerExample;
