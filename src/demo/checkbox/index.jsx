import React, { useState } from 'react'
import Showcase from '../showcase'
import { Checkbox } from '../../components'

const CheckboxExamples = () => {
  const [selected, setSelected] = useState({ 1: true, 5: true, 7: true })
  return (
    <Showcase 
      name="Checkbox"
      examples={
        <div className='space-y-2'>
          {[
            { label: 'Standard' },
            { label: 'Checked' },
            { label: 'Small', size: "small" },
            { label: 'Large', size: "large" },
            { label: 'Indeterminate', indeterminate: true },
            { label: 'Disabled', disabled: true },
            { label: 'Checked Disabled', disabled: true },
            { label: 'Small Disabled', size: "small", disabled: true },
            { label: 'Checked Indeterminate Disabled', indeterminate: true, disabled: true },
          ].map((checkbox, i) => 
            <div key={i}>
              <Checkbox
                index={i}
                size={checkbox.size}
                checked={selected[i]} 
                indeterminate={checkbox.indeterminate}
                label={checkbox.label}
                disabled={checkbox.disabled}
                onChange={() => setSelected({ ...selected, [i]: !selected[i] })}/>
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<Checkbox
  checked={true}
  size={"small"} // small or default medium
  disabled={false}
  indeterminate={false}
  label={"Label"}
  onChange={() => console.log("Handle on change.")}/>`

export default CheckboxExamples