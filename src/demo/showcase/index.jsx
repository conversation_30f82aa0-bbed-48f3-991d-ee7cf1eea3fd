import React from 'react'

const Showcase = ({ name, examples, code }) => 
  <div className='flex flex-col p-4'>
    <div className='text-2xl font-bold p-4 flex items-center justify-between'>
      <h3>{`<${name}/>`}</h3>
    </div>
    <div className='border-t border-gray-400 p-4 space-y-8'>
      <h5>Showcase</h5>
      {examples}
      <h5 className='font-subheader text-2xl'>Usage</h5>
      <div className='bg-black p-4 overflow-x-scroll'>
        <pre className='text-white text-sm'>{code}</pre>
      </div>
    </div>
  </div>

export default Showcase