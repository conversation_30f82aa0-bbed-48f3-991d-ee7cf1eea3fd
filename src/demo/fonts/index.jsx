import React from 'react'

const Fonts = () =>
  <div>
    <h4 className='my-4 border-t border-gray-300 pt-4'>Available Fonts</h4>
    There are three fonts primarily used in CineSend Theme:
    <div className='mt-4 py-4 border-t border-gray-300 mx-6'>
      1. <b>Clash Display</b> defaults in <b>{"<h1/>, <h2/>, <h3/>, <h4/>, <h5/>, an d <h6/>"}</b> tags and is targeted with <b>font-header</b>.
      <div className='text-center my-4'>
        <h1>{"<h1>This is Clash Display</h1>"}</h1>
        <h2>{"<h2>This is Clash Display</h2>"}</h2>
        <h3>{"<h3>This is Clash Display</h3>"}</h3>
        <h4>{"<h4>This is Clash Display</h4>"}</h4>
      </div>
    </div>
    <div className='mt-4 py-4 border-t border-gray-300 mx-10'>
      2. <b>Clash Grotesk</b> can <span className='underline'>only</span> be targeted with <b>font-subheader</b>.
      <div className='text-center my-4'>
        <h4 className='font-subheader'>{"<h4 className='font-subheader'>This is Clash Grotesk, overriding <h4/> default</h4>"}</h4>
        <div className='font-subheader'>{"<div className='font-subheader'>This is Clash Grotesk in a regular div</div>"}</div>
      </div>
    </div>
    <div className='mt-4 py-4 border-t border-gray-300 mx-10'>
      3. <b>Inter</b> is used by default in all text within <b>{"<body/>"}</b> and can be targeted with <b>font-content</b>.
      <div className='text-center my-4'>
        <div>{"<div>This is a regular div using Inter by default</div>"}</div>
      </div>
    </div>
  </div>

export default Fonts