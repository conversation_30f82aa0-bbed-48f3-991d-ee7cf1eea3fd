import React from 'react'
import { useNavigate } from 'react-router-dom'
import Showcase from '../showcase'
import { Button, Status } from '../../components'
import { Star } from '../../components/icons'

const StatusExamples = ({ history }) => {
  const navigate = useNavigate()
  return (
    <Showcase
      name="Status"
      examples={
        <div className='space-y-2'>
          {[
            { label: "Status wrapper with no issues" },
            { label: "Pending", pending: true },
            { label: "Pending with message", pending: true, pendingMessage: "Fetching your data!" },
            { label: "Error", error: true, errorMessage: "An error occurred fetching the data. Contact customer support." },
            { label: "Empty", empty: true, emptyMessage: { title: "No favourites!", icon: Star, message: "Add some favourites to your list and they'll show up here!" } },
            { label: "With Call to Actions", error: true, errorMessage: "Something happened! Click the call to action below.",
              buttons:[<Button onClick={() => navigate('/')}>Go Home</Button>, <Button tertiary onClick={() => window.location.reload()}>Try Again</Button>]}
          ].map((status, index) =>
            <div key={index} className='border border-gray-300 rounded p-4'>
              {status.label}:
              <Status {...status}>
                <div className='bg-primary-100 dark:bg-primary-700 dark:text-gray-200 p-4 rounded'>Children would render here.</div>
              </Status>
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<Status
  pending={true}                // false or true
  error={false}                 // false or true
  errorMessage={"An error!"}    // string to show when error is true
  empty={false}                 // empty indicator, use emptyMessage object to assist               
  emptyMessage={{
    title: "No results!",
    message: "Refine your search and try again."
    icon: "movies"
  }}
  height={"h-full"}             // override default height (uses tailwind class)  
>
  <div>Inner content that was not ready to be rendered yet.</div>
</Status>`

export default StatusExamples
