import React from "react"
import Showcase from "../showcase"
import { UploadTab } from "../../components"
import useCineSendImporter from "../hooks/use_cinesend_importer"

const DEV_API_URL = "https://local-api.cinesend.com.test/api/uploads"

const Upload = () => {
  const {
    projectList,
    breadcrumbList,
    status,
    fileList,
    onProjectClick,
    onOpenFolderClick,
    onAllFilesClick,
    onImportFilesClick,
    getRootFolderFileList,
  } = useCineSendImporter()

  return (
    <Showcase
      name="Upload Tab"
      examples={
        <div className="flex space-x-8 w-[1500px]">
          <UploadTab
            cinesend={{
              onImportFilesClick,
              onOpenFolderClick,
              onAllFilesClick,
              onProjectClick,
              getRootFolderFileList,
              status,
              projectList,
              fileList,
              breadcrumbList,
            }}
            multiple={true}
            extensions={["mp4", "vtt", "mp3", "txt"]}
            aspera
            upload={{
              apiURL: DEV_API_URL,
              specifications: {
                video_id: "6233552093cdc676751ceaf5",
              },
              onFileSelected: () => {
                console.log("selected")
              },
              onComplete: (fileToUpload, URL, res) => {
                console.log("completed ", res)
              },
              onCancel: () => {
                console.log("cancelled")
              },
              accept: {
                "video/*": [".mov", ".mp4", ".mpg", ".m4v"],
              },
            }}
          />
          <UploadTab
            aspera
            icon={"movie"}
            upload={{
              apiURL: DEV_API_URL,
              specifications: {
                video_id: "6233552093cdc676751ceaf5",
              },
              onRemove: () => {
                console.log("file removed")
              },
            }}
            file={{
              name: "test_mov.mov",
              size: 7009235,
            }}
            completionPending={true}
          />
        </div>
      }
      code={code}
    />
  )
}

const code = `<UploadTab
  upload={{
    publicURL: "https://cinesend-public-assets-dev.s3.amazonaws.com/cinesend-theme-upload-zone"
  }}
  onFileSelected={(file, callback) => {
    // This is used if signed URLs are required.
    // Fetch on an API endpoint to generate a signed URL, then callback with that response.
    // const response = { destinationURL: "https://s3.some-signed-url.com?signature=12345" }
    // callback(response)
  }}
  onCancel={() => {
    // function to be called when cancelling an upload
  }}
  onRemove={() => {
    // function to be called when removing an uploaded file
  }}
  cinesend={{                                     // Include this to enable the CineSend importer
    getRootFolderFileList={() => console.log('')} // optional, gets the root level filesList, normally used for setting the projectList
    onImportFilesClick: (ids) => console.log(''), // onClick function for importing or uploading the selected files
    onOpenFolderClick: () => console.log(''),     // onClick function for Api endpoint for retrieving files from a folder
    onAllFilesClick: onAllFilesClick,             // onClick function for Api endpoint for retrieving all projects or root folder
    onProjectClick={(id) => console.log('')}      // optional, onClick function for when a project is selected. Ex: Storing the Project ID
    status: fileStatus,                           // boolean for isLoading on getting the fileList from an endpoint
    fileList: fileList,                           // list of files { _id: '', name: '', type: '', extension: '', size: '' }
    breadcrumbList: breadcrumbList,               // list of folders { _id: '', name: '', type: '' }
    projectList={projectList}                     // optional, list of { _id: '', title: '', poster: '' }
  }}                  
  google={false}                  // TODO: Turn on uploading through Google, default false
  dropbox={false}                 // TODO: Turn on uploading through DropBox, default false
  aspera={false}                  // Turn on uploading through Aspera, default false
  file={file}                     // The file that has been uploaded
  icon={icon}                     // The icon to be displayed on the left of the upload progress/completion component
  completionPending={false}       // Turn on the pending icon on the upload complete component
  multiple={false},               // boolean for single or multiple file imports
  extensions={['mp4', 'mp3']},    // Array of allowed file extensions to be filtered in the file selection, default null
/>`

export default Upload
