import React from 'react'
import Showcase from '../showcase'
import LiveTableGrid from './live_table_grid'

const TableExamples = () =>
  <Showcase
    name="TableGrid"
    examples={
      <div className='space-y-4'>
        <LiveTableGrid/>
      </div>}
    code={code}/>

const code =
`<TableGrid
  table={{}} // all table-specific rendering props
  grid={{}} // all grid-specific rendering props
  header={{
    columns: [
      {
        text: 'Name',
        key: 'name'
      },
      {
        text: 'Brewery Type'
      },
      {
        text: 'City',
        key: 'city'
      },
      {
        text: 'Country',
        key: 'country'
      }
    ],
    sorting: {
      onSortChange: newSorting => setSorting(newSorting),
      direction: sorting.direction,
      key: sorting.key
    },
    filters: {
      search,
      searchPlaceholder: 'Search by city, brewery, postal code',
      onSearch: value => setSearch(value)
    },
    customElement: <Button minimal={true} onClick={() => alert('Clicked me!')}>Custom Element</Button>,
    checkbox: {
      checked: checked.length > 0,
      indeterminate: checked.length !== list.length,
      onChange: () => {
        if (checked.length === 0) {
          setChecked(list.map(({ _id }) => _id))
        }
        else {
          setChecked([])
        }
      }
    }
  }}
  paginate={{
    totalRows: total,
    currentPage: pagination.currentPage,
    onPageChange: page => setPage(page)
    rowsPerPage: pagination.rowsPerPage,
    onRowsPerPageChange: rowsPerPage => setRowsPerPage(rowsPerPage)
  }}/>`

export default TableExamples
