import React, { useEffect, useState } from 'react'
import { But<PERSON>, TableGrid, Poster } from '../../components'
import { get } from '../../components/helpers/fetch'

const baseURL = 'https://api.openbrewerydb.org/v1/breweries'
const total = 100

const filterOptions = [
  {
    label: 'City (Checkboxes)',
    key: 'city',
    type: 'checkboxes',
    options: [
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'denver', label: 'Denver' },
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'portland', label: 'Portland' },
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'reno', label: 'Reno' }
    ]
  },
  {
    label: 'State (Radio)',
    key: 'state',
    type: 'radio_buttons',
    options: [
      { key: 'ohio', label: 'Ohio' },
      { key: 'new_york', label: 'New York' },
      { key: 'new_mexico', label: 'New Mexico' }
    ]
  },
  {
    label: 'Last Updated (Date Range)',
    key: 'updated_at',
    type: 'date_range'
  },

]

const sortingOptions = [
  {
    key: 'name',
    label: 'Name'
  },
  {
    key: 'city',
    label: 'City'
  },
  {
    key: 'state',
    label: 'State'
  },
  {
    key: 'country',
    label: 'Country'
  }
]

const LiveTableGrid = () => {
  const [list, setList] = useState([])
  const [images, setImages] = useState([])
  const [checked, setChecked] = useState([])
  const [sorting, setSorting] = useState({ direction: 'ASC', key: "name" })
  const [pagination, setPagination] = useState({ currentPage: 0, rowsPerPage: 10 })
  const [search, setSearch] = useState('')
  const [filters, setFilters] = useState()
  useEffect(() => {
    let queries = [
      `sort=${sorting.key}:${sorting.direction.toLowerCase()}`,
      `page=${pagination.currentPage + 1}`,
      `per_page=${pagination.rowsPerPage}`,
    ]
    if (filters && filters.city) {
      queries.push(`by_city=${filters.city}`)
    }
    if (filters && filters.state) {
      queries.push(`by_state=${filters.state}`)
    }
    //setList(null)
    fetch(`${baseURL}${search ? '/search?query=' + search + '&' : '?'}${queries.join("&")}`)
      .then(response => response.json())
      .then(data => setList(data))
  }, [sorting, pagination, search, filters])
  useEffect(() => {
    if (images.length > 0) {
      return
    }
    get('https://picsum.photos/v2/list', res => {
      setImages(res.map((res, index) => res.download_url))
    }, false)
  }, [])
  return (
    <>
      <TableGrid
        widths={["auto", 160, 160, 50, 160]}
        initialColumns={[0, 1]}
        header={{
          title: <div className='text-xl font-bold flex items-center'>Breweries (Live API, Compact Table)</div>,
          columns: [
            {
              text: 'Name',
              key: 'name'
            },
            {
              text: 'Brewery Type',
            },
            {
              text: 'City',
              key: 'city'
            },
            {
              text: 'State',
              key: 'state'
            },
            {
              text: 'Country',
              key: 'country'
            }
          ],
          sorting: {
            options: sortingOptions,
            onSortChange: newSorting => setSorting(newSorting),
            direction: sorting.direction,
            key: sorting.key,
            sortingKey: sorting.key
          },
          searching: {
            search,
            searchPlaceholder: 'Search by city, brewery, postal code',
            onSearch: value => setSearch(value),
          },
          filtering: {
            options: filterOptions,
            filters,
            onFiltersChange: value => setFilters(value)
          },
          checkbox: {
            checked: checked.length > 0,
            indeterminate: list && checked.length !== list.length,
            onChange: () => {
              if (checked.length === 0) {
                setChecked(list.map(({ id }) => id))
              }
              else {
                setChecked([])
              }
            }
          }
        }}
        tableBody={{
          data: list,
          row: {
            compact: true,
            truncate: true,
            checkbox: {
              checked: (data, index) => checked.includes(data.id),
              onChange: (data, index) => checked.includes(data.id)
                ? setChecked(checked.filter(i => i !== data.id))
                : setChecked([...checked, data.id])
            },
            onClick: (event, data, index) => checked.includes(data.id)
            ? setChecked(checked.filter(i => i !== data.id))
            : setChecked([...checked, data.id]),
            render: [
              data => data.name,
              data => data.brewery_type,
              data => data.city,
              data => data.state,
              data => data.country
            ]
          },
          empty: {
            title: 'No breweries!',
            text: 'Try searching for something else...',
            icon: 'sports_bar' // Material-Icon
          }
        }}
        gridBody={{
          data: list,
          render: (data, i) => {
            const props = {
              url: data.url,
              title: data.name,
              description: `${data.city} - ${data.state_province} - ${data.country}`,
              clickText: "Click to upload a new thumbnail",
              clickFunction: i % 2 === 0 ? () => alert("do something") : null,
              selected: checked.includes(data.id),
              onSelect: () =>
                checked.includes(data.id)
                  ? setChecked(checked.filter((index) => index !== data.id))
                  : setChecked([...checked, data.id]),
              cover: true,
              buttonDropdownContent: [
                {
                  text: "Click me!",
                  icon: "star",
                  onClick: () => alert("Alright you did it"),
                },
                {
                  text: "Click me instead!",
                  icon: "language",
                  onClick: () => alert("Alright you did this one great"),
                },
              ],
            };
            return <Poster {...props}/>
          }
        }}
        paginate={{
          totalRows: total,
          currentPage: pagination.currentPage,
          rowsPerPage: pagination.rowsPerPage,
          onPageChange: currentPage => setPagination({ ...pagination, currentPage }),
          onRowsPerPageChange: rowsPerPage => setPagination({ ...pagination, rowsPerPage })
        }}
      />
    </>
  )
}

export default LiveTableGrid
