import React, { useEffect, useState } from 'react'
import Showcase from '../showcase'
import { <PERSON>rid, Poster, SegmentedControl, Thumbnail, Toggle } from '../../components'
import { get } from '../../components/helpers/fetch'

const baseURL = 'https://api.openbrewerydb.org/v1/breweries'
const total = 100

const filterOptions = [
  {
    label: 'State (Radio)',
    key: 'state',
    type: 'radio_buttons',
    options: [
      { key: 'ohio', label: 'Ohio' },
      { key: 'new_york', label: 'New York' },
      { key: 'new_mexico', label: 'New Mexico' }
    ]
  }
]

const sortingOptions = [
  {
    key: 'name',
    label: 'Name'
  }
]

export default function GridExample() {
  const [list, setList] = useState([]);
  const [images, setImages] = useState([]);
  const [pending, setPending] = useState(false)
  const [selected, setSelected] = useState([]);
  const [checked, setChecked] = useState([]);
  const [search, setSearch] = useState("");
  const [sorting, setSorting] = useState({ direction: "ASC", key: "name" });
  const [filters, setFilters] = useState();
  const [pagination, setPagination] = useState({
    currentPage: 0,
    rowsPerPage: 10,
  });
  const [cover, setCover] = useState(false);
  const [displayType, setDisplayTime] = useState("posters");
  const setListWithImages = (list) => {
    setList(
      list.map((item) => ({
        ...item,
        url: images[Math.floor(Math.random() * images.length)],
      }))
    );
  };
  useEffect(() => {
    let queries = [
      `sort=${sorting.key}:${sorting.direction.toLowerCase()}`,
      `page=${pagination.currentPage + 1}`,
      `per_page=${pagination.rowsPerPage}`,
    ];
    if (filters && filters.city) {
      queries.push(`by_city=${filters.city}`);
    }
    if (filters && filters.state) {
      queries.push(`by_state=${filters.state}`);
    }
    fetch(
      `${baseURL}${
        search ? "/search?query=" + search + "&" : "?"
      }${queries.join("&")}`
    )
      .then((response) => response.json())
      .then((data) => setListWithImages(data));
  }, [sorting, pagination, search, filters]);
  useEffect(() => {
    if (images.length > 0) {
      return;
    }
    get(
      "https://picsum.photos/v2/list",
      (res) => {
        setImages(res.map((res, index) => res.download_url));
      },
      false
    );
  }, []);
  return (
    <Showcase
      name="Grid"
      examples={
        <div className="space-y-4">
          <div className="flex space-x-4 w-96">
            <SegmentedControl
              size="small"
              secondary
              options={[
                { label: "Poster View", value: "posters" },
                { label: "Thumbnail View", value: "thumbnails" },
              ]}
              value={displayType}
              onChange={(type) => setDisplayTime(type)}
            />
            <Toggle
              checked={cover}
              label="Overlay Text"
              onChange={() => setCover(!cover)}
            />
            <Toggle
              checked={pending}
              label="Pending?"
              onChange={() => setPending(!pending)}
            />
          </div>
          <Grid
            status={{
              pending: pending,
              message: "This is a pending message"
            }}
            isPoster={displayType === "posters"}
            header={{
              title: <h4>Grid Example</h4>,
              searching: {
                search,
                searchPlaceholder: "Search for image name",
                onSearch: (value) => setSearch(value),
              },
              filtering: {
                options: filterOptions,
                filters,
                onFiltersChange: (value) => setFilters(value),
              },
              sorting: {
                options: sortingOptions,
                onSortChange: (newSorting) => setSorting(newSorting),
                direction: sorting.direction,
                key: sorting.key,
                sortingKey: sorting.key,
              },
              checkbox: {
                checked: checked.length > 0,
                indeterminate: checked.length !== list.length,
                onChange: () => {
                  if (checked.length === 0) {
                    setChecked(urlData.map(({ _id }) => _id));
                  } else {
                    setChecked([]);
                  }
                },
              },
            }}
            body={{
              data: list,
              render: (data, i) => {
                const props = {
                  url: data.url,
                  title: data.name,
                  description: `${data.city} - ${data.state_province} - ${data.country}`,
                  clickText: "Click to upload a new thumbnail",
                  clickFunction:
                    i % 2 === 0 ? () => alert("do something") : null,
                  selected: selected.includes(i),
                  onSelect: () =>
                    selected.includes(i)
                      ? setSelected(selected.filter((index) => index !== i))
                      : setSelected([...selected, i]),
                  cover,
                  buttonDropdownContent: [
                    {
                      text: "Click me!",
                      icon: "star",
                      onClick: () => alert("Alright you did it"),
                    },
                    {
                      text: "Click me instead!",
                      icon: "language",
                      onClick: () => alert("Alright you did this one great"),
                    },
                  ],
                  lightBackground: true,
                  emptyIcon: 'image'
                };
                return displayType === "posters" ? (
                  <Poster {...props} />
                ) : (
                  <Thumbnail {...props} />
                );
              },
            }}
            paginate={{
              totalRows: total,
              currentPage: pagination.currentPage,
              rowsPerPage: pagination.rowsPerPage,
              onPageChange: (currentPage) =>
                setPagination({ ...pagination, currentPage }),
              onRowsPerPageChange: (rowsPerPage) =>
                setPagination({ ...pagination, rowsPerPage }),
            }}
          />
        </div>
      }
      code={code}
    />
  );
}

const code = `<Grid
  header={{
    columns: [
      {
        text: 'Name',
        key: 'name'
      },
      {
        text: 'Brewery Type'
      },
      {
        text: 'City',
        key: 'city'
      },
      {
        text: 'Country',
        key: 'country'
      }
    ],
    sorting: {
      onSortChange: newSorting => setSorting(newSorting),
      direction: sorting.direction,
      key: sorting.key
    },
    filters: {
      search,
      searchPlaceholder: 'Search by city, brewery, postal code',
      onSearch: value => setSearch(value)
    },
    customElement: <Button minimal={true} onClick={() => alert('Clicked me!')}>Custom Element</Button>,
    checkbox: {
      checked: checked.length > 0,
      indeterminate: checked.length !== list.length,
      onChange: () => {
        if (checked.length === 0) {
          setChecked(list.map(({ _id }) => _id))
        }
        else {
          setChecked([])
        }
      }
    }
  }}
  body={{
    data: list,
    render: (data, i) => {
      const props = {
        url: someUrl,
        title='Title Below Poster'
        description='Description Below Title'
        isThumbnail: true,
        isPoster: false,
        clickFunction={() => upload()}
        clickText='Upload a new poster!'
        selected: items.find(item => item._id === 'abc'),
        onSelect: () => someFunc(),
        cover: false,
        buttonDropdownContent: [
          {
            text: "Click me!",
            icon: "star",
            onClick: () => alert("Alright you did it")
          },
          {
            text: "Click me instead!",
            icon: "language",
            onClick: () => alert("Alright you did this one great")
          }
        ]
      }
      return displayType !== "posters" ? <Poster {...props} /> : <Thumbnail {...props} />
    }
  }}
  paginate={{
    totalRows: total,
    currentPage: pagination.currentPage,
    rowsPerPage: pagination.rowsPerPage,
    onPageChange: currentPage => setPagination({ ...pagination, currentPage }),
    onRowsPerPageChange: rowsPerPage => setPagination({ ...pagination, rowsPerPage })
  }}
/>`;