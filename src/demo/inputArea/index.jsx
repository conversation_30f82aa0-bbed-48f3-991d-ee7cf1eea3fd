import React, { useState } from 'react'
import Showcase from "../showcase"
import { InputArea } from "../../components"
import { Star, InfoCircle } from "../../components/icons"

const InputAreaExample = () => {
  const [text, setText] = useState({ 0: "Placeholder", 1: "", 2: "", 3: "", 4: "", 5: "", 6: "", 7: "", 8: "" })

  const hoverIcon = { icon: InfoCircle, tooltip: { direction: "top", text: "This is some information about this field." } }
  const clickIcon = { icon: InfoCircle, onClick: () => window.alert("You clicked me!") }
  const customCopyIcon = { tooltip: { text: "Click to copy 12345!", copy: { value: "12345", onCopyMessage: "You copied 12345!" } } }
  const defaultCopyIcon = { tooltip: { copy: { } } }
  
  return (
    <Showcase
      name="InputArea"
      examples={
        <div className='space-y-2'>
          {[
            { id: 'input-1' },
            { label: "Input Area with Label" },
            { label: "Disabled Input Area", disabled: true },
            { label: "Input Area with No Resize", resize: false },
            { label: "Input Area with Label", description: "Input Area with Description" },
            { label: "Input Area with Label", description: "Input Area with Description", message: "Input Area with Message" },
            { label: "Input Area with Custom Width", inputWidth: "w-[250px]" },
            { label: "Input Area with Custom Height", minInputHeight: "h-[50px]", inputHeight: "h-[50px]" },
            { label: 'Both Icons', input: { leftIcon: hoverIcon, rightIcon: clickIcon } },
            { label: 'Right Icon Default Copy', input: { rightIcon: defaultCopyIcon } },
            { label: 'Left Icon Custom Message', input: { leftIcon: customCopyIcon } },
            { message: { text: 'Message Icon Left with Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus nulla enim, finibus nec felis ut, pharetra bibendum ex. Vivamus sed tortor eu enim vulputate pellentesque. Ut tempus elementum eleifend. Duis tempor, arcu vel rutrum porta, nulla neque elementum magna, semper lacinia mi justo vel ante.', icon: { icon: Star } } },
            { value: 'Message Icon Left with Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus nulla enim, finibus nec felis ut, pharetra bibendum ex. Vivamus sed tortor eu enim vulputate pellentesque. Ut tempus elementum eleifend. Duis tempor, arcu vel rutrum porta, nulla neque elementum magna, semper lacinia mi justo vel ante.' },
            { label: 'Disabled Input', disabled: true },
          ].map((input, i) =>
            <div key={i} className={'pt-2'}>
              <InputArea
                value={text[i]}
                onChange={(e) => setText({ ...text, [i]: e.target.value})}
                {...input}
              />
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<InputArea
  id={"unique-id"}                      // optional
  resize={false}                        // default true
  minInputHeight={"min-h-[10px]"}       // default is min-h-[120px]
  inputHeight={"h-100"}                 // default is h-60
  inputWidth={"w-[50px]"}               // default is w-full
  value={text}                          // required
  onChange={(input) => setText(input)}  // required
  label={"My label"}                    // default no label -- can be an object label={ text: "My Label", icon: { icon: InfoCircle, onClick: () => window.alert("You clicked me!") }
  description={"Custom Description"}    // default no description 
  message={"Custom Message"}            // default no message -- can be an object message={ text: "My Message", icon: { icon: InfoCircle, onClick: () => window.alert("You clicked me!") }
  disabled={true}                       // default false
  success={true}                        // default false
  error={true}                          // default false
  width={'w-[100px]'}                   // default 240px
  leftIcon={
    icon: Star,                         // icon to be displayed
    size: "medium"                      // default medium, small, large
  }
  rightIcon={
    size: "medium"                      // default medium, small, large
    tooltip: {
      text: "Click to copy 12345!",
      copy: {
        value: "12345",
        onCopyMessage: "You copied 12345!" 
      }
    }
  }
/>`

export default InputAreaExample;
