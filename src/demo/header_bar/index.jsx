import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import HeaderBar from '../../components/header_bar'
import { Settings } from '../../components/icons'
import GlobalSearch from '../../components/global_search'
import { Button, Toggle } from '../../components'
import packageJson from '../../../package.json'

const ThemeHeaderBar = ({
  sidebarTheme,
  setSidebarTheme,
  mainTheme,
  setMainTheme,
  lockExpanded,
  setLockExpanded  
}) => {
  const [showAvatar, setShowAvatar] = useState()
  const [searchOpen, setSearchOpen] = useState(false)
  const [searchValue, setSearchValue] = useState("")
  const [results, setResults] = useState([])
  const navigate = useNavigate()
  return (
    <HeaderBar
      name="CineSend Developer"
      organization="BitCine Technologies"
      // leftMargin={null}
      photoURL={showAvatar ? "https://cinesend-public-assets-dev.s3.amazonaws.com/cinesend-theme-upload-zone/obama.jpg" : null}
      clickCloses={false}
      classToClose={'this-will-close'}
      dropdownContent={[
        {
          text: "Click me!",
          icon: "star",
          onClick: () => window.alert("You clicked me!")
        },
        {
          text: "Toggle avatar!",
          icon: Settings,
          onClick: () => setShowAvatar(!showAvatar)
        },
        {
          text: "This uses a Material Icon",
          icon: 'shopping_cart',
          onClick: () => setShowAvatar(!showAvatar)
        },
        {
          component: <div className='p-4 text-sm bg-gray-100 this-will-close' onClick={() => window.alert("Good job on the clicking thing")}>
            This is a single component, maybe click me?
          </div>
        },
        {
          text: "Log out",
          onClick: () => window.alert("You could use this to log out!"),
          icon: "person"
        }
      ]}>
      <div className='flex items-center justify-between px-4'>
        <div className='flex items-center space-x-4'>
          <div className='flex flex-col'>
            <div>Welcome!</div>
            <div className='text-xs text-gray-600'>Version: {packageJson.version}</div>
          </div>
          <Toggle
            label={'Main Page Dark Mode'}
            checked={mainTheme === 'dark'}
            onChange={() => setMainTheme(mainTheme === 'dark' ? 'light' : 'dark')}/>
          {mainTheme !== 'dark' ? <Toggle
            label={'Sidebar Dark Mode'}
            checked={sidebarTheme === 'dark'}
            onChange={() => setSidebarTheme(sidebarTheme === 'dark' ? 'light' : 'dark')}/> : null}
            <Toggle
            label={'Sidebar Lock Expanded'}
            checked={lockExpanded}
            onChange={() => {
              setLockExpanded(!lockExpanded)
            }}/>
        </div>
        <Button minimal icon="search" onClick={() => setSearchOpen(true)}/>
      </div>
      {searchOpen ?
        <GlobalSearch
          onClose={() => {
            setSearchOpen(false)
            setSearchValue("")
          }}
          value={searchValue}
          onValueChange={value => {
            setSearchValue(value)
            setResults(searchComponents(value))
          }}
          searchMessage="Search for a component"
          results={results}
          onResultClick={result => {
            navigate(result.path)
            setSearchOpen(false)
            setSearchValue("")
          }}
        /> :
        null}
    </HeaderBar>
  )
}

const components = [
  {
    name: "Standard Button",
    type: "Common",
    path: "/common/buttons",
    icon: "mouse"
  },
  {
    name: "Button Dropdown",
    type: "Common",
    path: "/common/button-dropdowns",
    icon: "mouse"
  },
  {
    name: "Standard Input",
    type: "Input",
    path: "/inputs/text",
    icon: "edit"
  }
]

const searchComponents = search => {
  return components.filter(component => {
    return component.name.toLowerCase().includes(search.toLowerCase())
  })
}
      
export default ThemeHeaderBar