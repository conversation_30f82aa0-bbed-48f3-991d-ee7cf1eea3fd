import React, { useState } from 'react'
import Showcase from '../showcase'
import { Radio } from '../../components'

const RadioExamples = () => {
  const [selected, setSelected] = useState({ 4: true, 5: true, 10: true, 11: true})
  return (
    <Showcase
      name="Radio"
      examples={
        <div className='space-y-2'>
          {[
            { id: '0' },
            { id: '1', small: true },
            { id: '2', label: 'Standard' },
            { id: '3', small: true, label: 'Small' },
            { id: '4', label: 'Standard Checked' },
            { id: '5', small: true, label: 'Small Checked' },
            { id: '6', disabled: true, label: 'Disabled'},
            { id: '7', small: true, disabled: true, label: 'Small Disabled' },
            { id: '8', label: 'Standard Disabled', disabled: true },
            { id: '9', small: true, label: 'Small Disabled', disabled: true },
            { id: '10', label: 'Checked Disabled', disabled: true },
            { id: '11', size: 'large', label: 'Large Checked' },
            { id: '12', size: 'large', label: 'Large Disabled', disabled: true },
            { id: '13', size: 'large', label: 'Large Disabled', disabled: true }
          ].map((radio, i) =>
            <div key={i}>
              <Radio
                index={i}
                checked={selected[i]}
                onChange={() => setSelected({ ...selected, [i]: !selected[i] })}
                {...radio}
              />
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<Radio
  id={"ID"}
  size={"small"} // small, medium, or large (default medium)
  checked={false}
  small={false}
  label={"Label"}
  disabled={false}
  onChange={() => console.log("Handle on change.")}/>`

export default RadioExamples
