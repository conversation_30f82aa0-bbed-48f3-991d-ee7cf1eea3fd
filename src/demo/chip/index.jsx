import React from 'react'
import Showcase from '../showcase'
import { Chip } from '../../components'
import { Star, CrossCircle } from '../../components/icons'

const ChipExamples = () =>
  <Showcase
    name="Chip"
    examples={
      <div className='space-y-2'>
        {[
          { label: 'Medium Chip' },
          { label: 'Small Chip', size: 'small' },
          { label: 'Selected Medium Chip', selected: true },
          { label: 'Selected Small Chip', size: 'small', selected: true },
          { label: 'Disabled Medium Chip', disabled: true },
          { label: 'Disabled Small Chip', size: 'small', disabled: true },
          { label: 'Close Small Chip', size: 'small', close: true, onDelete: () => window.alert("Delete Chip") },
          { label: 'Close Selected Medium Chip', selected: true, close: true, onDelete: () => window.alert("Delete Chip") },
          { label: 'Icon Medium Chip', icon: Star },
          { label: 'Icon Small Chip', size: 'small', icon: Star },
          { label: 'Icon Selected Medium Chip', selected: true, icon: CrossCircle },
          { label: 'Icon Selected Small Chip', size: 'small', selected: true, icon: CrossCircle },
          { label: 'Icon Disabled Medium Chip', disabled: true, icon: Star },
          { label: 'Icon Disabled Small Chip', size: 'small', disabled: true, icon: Star },
          { label: 'Close Icon Small Chip', size: 'small', icon: Star, close: true, onDelete: () => window.alert("Delete Chip") },
          { label: 'Close Icon Selected Medium Chip', selected: true, icon: CrossCircle, close: true, onDelete: () => window.alert("Delete Chip") },
        ].map((tag, i) =>
          <div key={i}>
            <Chip
              index={i}
              {...tag}
              label={tag.label}/>
          </div>)}
      </div>
    }
    code={code}/>

const code =
`<Chip
  label={"Some Chip Label"}
  size={"medium"}                 // default medium, medium or small
  icon=Star                       // default null, imported icons from '../../components/icons'
  close={true}                    // default false, or true
  onDelete={() => handleDelete()} // optional, handle delete when close is true
  disabled={false}                // default false, or true
/>`

export default ChipExamples
