import React, { useEffect, useState } from 'react'
import Showcase from '../showcase'
import { Poster, Toggle } from '../../components'
import { get } from '../../components/helpers/fetch'

export default function PosterExample() {
  const [urls, setUrls] = useState([])
  const [selected, setSelected] = useState([])
  const [cover, setCover] = useState(false)
  useEffect(() => {
    if (urls.length > 0) {
      return
    }
    get('https://picsum.photos/v2/list', res => {
      setUrls(res.map(res => res.download_url))
    }, false)
  }, [])
  return (
    <Showcase
      name="Poster"
      examples={
        <div className="flex flex-col space-y-4">
          <Toggle
            label="Cover Text"
            checked={cover}
            onChange={() => setCover(!cover)}
          />
          <div className="grid grid-cols-4 gap-4">
            {urls.map((url, i) => (
              <div key={i}>
                <Poster
                  url={url}
                  title={`This is the title: ${i + 1}`}
                  description={`Longer description for ${i + 1}`}
                  clickText="Click to upload a new poster"
                  clickFunction={
                    i % 2 === 0 ? () => alert("do something") : null
                  }
                  selected={selected.includes(i)}
                  onSelect={() =>
                    selected.includes(i)
                      ? setSelected(selected.filter((index) => index !== i))
                      : setSelected([...selected, i])
                  }
                  buttonDropdownContent={[
                    {
                      text: "Click me!",
                      icon: "star",
                      onClick: () => alert("Alright you did it"),
                    },
                    {
                      text: "Click me instead!",
                      icon: "language",
                      onClick: () => alert("Alright you did this one great"),
                    },
                  ]}
                  cover={cover}
                />
              </div>
            ))}
          </div>
        </div>
      }
      code={code}
    />
  );
}

const code = `<Poster
  url='https://images.cinesend.com/some-poster.jpg'
  title='Title Below Poster'
  description='Description Below Title'
  clickFunction={() => something()}
  clickText='Upload a new poster!'/>
/>`;