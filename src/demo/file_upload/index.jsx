import React, { useState } from 'react'
import Showcase from '../showcase'
import { FileUpload } from '../../components'

const PUBLIC_S3_URL = "https://cinesend-public-assets-dev.s3.amazonaws.com/cinesend-theme-upload-zone"
const DEV_API_URL = "https://local-api.cinesend.com.test/api/uploads"

const Upload = () => {
  const [files, setFiles] = useState({
    three: {
      name: "LetsSayThisFileWasAlreadyUploadedLongFileName.mov",
      size: 25950000000
    }
  })
  return (
    <Showcase
      name="FileUpload"
      examples={
        <div className='space-y-2'>
          <div className='flex items-center space-x-2'>
            <FileUpload
              className='w-1/3 aspect-video'
              upload={{
                icon: "star",
                publicURL: PUBLIC_S3_URL,
                onComplete: (file => setFiles({ ...files, one: file }))
              }}
              file={files.one}/>
            <FileUpload
              className='w-1/3 aspect-video'
              upload={{
                message: "Custom message: upload here!",
                dropMessage: "Drop your file here please!",
                publicURL: PUBLIC_S3_URL,
                onComplete: (file => setFiles({ ...files, two: file }))
              }}
              button={{ text: "Upload DCP!", icon: "movie", type: "success" }}
              file={files.two}/>
            <FileUpload
              className='w-1/3 aspect-video'
              upload={{
                publicURL: PUBLIC_S3_URL,
                onComplete: (file => setFiles({ ...files, three: file }))
              }}
              button={{ text: "Upload DCP" }}
              file={files.three}/>
          </div>
          <div className='flex items-center space-x-2'>
            <FileUpload
              className='w-1/2'
              upload={{
                icon: "star",
                message: "This upload has Aspera enabled!",
                // onComplete: (file => setFiles({ ...files, four: file })),
                apiURL: DEV_API_URL,
                asperaEnabled: true,
                specifications: {
                  video_id: "6233552093cdc676751ceaf5"
                }
              }}
              file={files.four}/>
          </div>
        </div>
      }
      code={code}/>
  )
}
  

const code = `<FileUpload
  file={{
    name: "Some File Name",
    size: 100000 // in bytes
  }}
  upload={{
    icon: "file",
    message: "Drag something here!",
    dropMessage: "Drop here!",
    publicURL: null,               // This is used if the file can be dropped directly at the public destination URL
    onFileSelected: (file, callback) => {
      // This is used if signed URLs are required.
      // Fetch on an API endpoint to generate a signed URL, then callback with that response.
      // const response = { destinationURL: "https://s3.some-signed-url.com?signature=12345" }
      // callback(response)
    },
    onProgress: () => console.log("Progress"),
    onComplete: () => window.alert("On upload change")
  }}
  button={
    text: "Upload an image",
  },
  includeRemoveButton={true}
  className={""},
/>`

export default Upload