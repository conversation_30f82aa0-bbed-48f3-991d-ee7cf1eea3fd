import React, { useState } from "react"
import Showcase from "../showcase"
import SelectFilesModal from "../../components/select_files_modal/select_files_modal"
import { Button } from "../../components"
import useCineSendImporter from "../hooks/use_cinesend_importer"

const SelectFilesExamples = () => {
  const [showSelectFoldersModal, setShowSelectFoldersModal] = useState(false)
  const { projectList, breadcrumbList, status, fileList, onProjectClick, onOpenFolderClick, onAllFilesClick } =
    useCineSendImporter()
  return (
    <Showcase
      name="SelectFilesModal"
      examples={
        <div className="space-y-4">
          <Button
            size="small"
            className="cs-button outline capitalize small ml-2"
            onClick={() => setShowSelectFoldersModal(true)}
          >
            Import
          </Button>

          {showSelectFoldersModal && (
            <SelectFilesModal
              projectList={projectList}
              fileList={fileList}
              breadcrumbList={breadcrumbList}
              header={"Import From CineSend Account"}
              onClose={() => setShowSelectFoldersModal(false)}
              onAllFilesClick={onAllFilesClick}
              onOpenFolderClick={onOpenFolderClick}
              onProjectClick={onProjectClick}
              status={status}
              onImportFilesClick={(ids) => console.log("Import endpoint triggered with file ids: " + ids)}
              extensions={null}
            />
          )}
        </div>
      }
      code={code}
    />
  )
}

const code = `<SelectFilesModal
  getRootFolderFileList={() => getRootFolderFileList}             // optional, gets the root level filesList, normally used for setting the projectList
  projectList={projectList}                                       // optional, list of { _id: '', title: '', poster: '' }
  onProjectClick={(id) => console.log('upload function')}         // optional, onClick function for when a project is selected. Ex: Storing the Project ID
  fileList={fileList}                                             // required, list of { _id: '', name: '', type: '', extension: '', size: '' }
  breadcrumbList={breadCrumbList}                                 // required, list of { _id: '', name: '', type: '' }
  header={'Import From Cinesend Account'}                         // default no header
  onClose={() => setShowSelectFoldersModal(false)}                // default null, onClose function for select files
  onAllFilesClick={onAllFilesClick}                               // required, onClick function for navigating to the root folder / projects to show all files
  onOpenFolderClick={onOpenFolderClick}                           // required, onClick function for navigating into a folder in the list or breadcrumb
  status={status}                                                 // default 'READY'
  onImportFilesClick={(ids) => console.log('upload function')}    // required, onClick function that imports or uploads the files
  multiple: false,                                                // boolean for single or multiple file imports
  extensions={['mp4', 'mp3']},                                    // optional Array of file extensions to be filtered in the file selection, default null

  />`

export default SelectFilesExamples
