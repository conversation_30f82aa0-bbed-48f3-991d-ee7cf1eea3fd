import React, { useState } from 'react'
import { Button, Modal, ButtonDropdown, Select } from '../../components'
import Showcase from '../showcase'

const ModalExamples = () => {
  const [openModal, setOpenModal] = useState(null)
  return (
    <Showcase 
      name="Modal"
      examples={<div className='space-y-2'>
        <Button onClick={() => setOpenModal(1)}>Basic Modal</Button>
        {openModal === 1 &&
          <Modal header='Basic Modal' minWidth={false} className='w-full max-w-md' onClose={() => setOpenModal(null)}>This basic modal has no confirm actions.</Modal>}
        <br/>
        <Button onClick={() => setOpenModal(2)}>Confirmation Modal</Button>
        {openModal === 2 &&
          <Modal
            header='Confirmation Modal'
            confirmButton={{
              text: "Click to Confirm",
              onClick: () => window.alert("You clicked!")
            }}
            onClose={() => setOpenModal(null)}>
            <p>
              This modal has one confirmation button, which automatically creates a Cancel button as well.
            </p>
            <br/>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis tincidunt id massa tristique aliquam. Donec semper dolor augue, a accumsan sapien interdum nec. Aliquam vitae sodales erat. Sed at urna nec erat condimentum molestie vitae ac enim. Mauris posuere odio tristique, dignissim metus id, aliquet mauris. Vivamus sit amet lacinia mauris. Cras aliquam eleifend elit eu pharetra. In hac habitasse platea dictumst. Mauris mauris tellus, lacinia quis lacinia at, viverra a dolor. Morbi sed egestas risus. Nam dignissim consequat risus hendrerit feugiat. Fusce id metus quis enim vestibulum laoreet quis ut augue. Nam vel ex non lorem scelerisque aliquet in vitae orci. Ut in ipsum feugiat, fringilla ligula in, dictum lorem. Etiam tincidunt in mi ac dignissim. Morbi pretium vehicula nisi, at sagittis libero dapibus sed. Vestibulum finibus faucibus ultricies. Etiam elementum ante et dui tempus, id laoreet nisl luctus. Nulla facilisi. Ut rhoncus nibh vestibulum odio vulputate pretium. In ut diam et enim suscipit viverra quis eget enim. Etiam interdum quam vel facilisis fermentum.
            </p>
          </Modal>}
        <br/>
        <Button onClick={() => setOpenModal(3)}>Secondary Option Modal</Button>
        {openModal === 3 &&
          <Modal
            header='Secondary Option Modal'
            subheader='Some secondary text'
            confirmButton={{
              text: "Click to Confirm",
              onClick: () => window.alert("You clicked!")
            }}
            secondaryButton={{
              text: "Less Common Option",
              tertiary: true,
              onClick: () => window.alert("You clicked the secondary option!")
            }}
            onClose={() => setOpenModal(null)}>
            <p>
              This basic modal has one secondary option. It's also using the subheader text.
            </p>
          </Modal>}
          <br/>
        <Button onClick={() => setOpenModal(4)}>Custom Class Modal</Button>
        {openModal === 4 &&
          <Modal
            className='min-w-[1200px] min-h-[300px]'
            header='Secondary Option Modal'
            confirmButton={{
              text: "Click to Confirm",
              onClick: () => window.alert("You clicked!")
            }}
            secondaryButton={{
              text: "Less Common Option",
              onClick: () => window.alert("You clicked the secondary option!")
            }}
            onClose={() => setOpenModal(null)}>
            <div className='space-y-2'>
              <p>This basic modal adds class names to stretch the width and height, and tests button dropdown and select z-index.</p>
              <ButtonDropdown
                button={{ text: 'Custom' }}
                dropdown={{
                  content: [...Array(3).keys()].map(index => ({ text: index }))
                }}
              />
              <Select
                options={[...Array(10).keys()].map(index => ({ value: index, label: index }))}/>  
            </div>
          </Modal>}
          <br/>
          <Button onClick={() => setOpenModal(5)}>Disabled Option Modal</Button>
          {openModal === 5 &&
            <Modal
              header='Disabled Option Modal'
              confirmButton={{
                text: "Click to Confirm",
                disabled: true,
                onClick: () => window.alert("This should not work!")
              }}
              secondaryButton={{
                text: "Less Common Option",
                disabled: true,
                tertiary: true,
                onClick: () => window.alert("You should not be able to click this!")
              }}
              onClose={() => setOpenModal(null)}>
              <p>
                This modal has the primary and secondary buttons disabled.
              </p>
            </Modal>}
          <br/>
          <Button type="error" onClick={() => setOpenModal(6)}>Custom Button Props Modal</Button>
          {openModal === 6 &&
            <Modal
              header='Important Action Modal'
              confirmButton={{
                text: "Click to Delete Account",
                type: "error",
                onClick: () => window.alert("This should work!")
              }}
              secondaryButton={{
                text: "Less Common Option",
                type: "error",
                tertiary: true,
                onClick: () => window.alert("You clicked the secondary button!")
              }}
              onClose={() => setOpenModal(null)}>
              <p>
                This modal makes use of more button properties.
              </p>
            </Modal>}
          <br/>
          <Button onClick={() => setOpenModal(7)}>Tall Modal</Button>
          {openModal === 7 &&
            <Modal
              header='Tall Modal'
              confirmButton={{
                text: "Click to Close",
                type: "error",
                onClick: () => window.alert("This should work!")
              }}
              secondaryButton={{
                text: "Less Common Option",
                type: "error",
                tertiary: true,
                onClick: () => window.alert("You clicked the secondary button!")
              }}
              onClose={() => setOpenModal(null)}>
              <>
                {[...Array(10).keys()].map(index => 
                  <div className='h-40 w-full bg-black mb-4 text-white text-2xl flex items-center justify-center' key={index}>{index}</div>
                )}
                <Select
                  options={[...Array(10).keys()].map(index => ({ value: index, label: index }))}/> 
              </>
            </Modal>}
          <br/>
          <Button onClick={() => setOpenModal(8)}>Pending Modal</Button>
          {openModal === 8 &&
            <Modal
              header='Pending Modal'
              pending={true}
              confirmButton={{
                text: "Okay!",
                onClick: () => window.alert("Disabled")
              }}
              onClose={() => setOpenModal(null)}>
              This modal is pending.
            </Modal>}
      </div>}
      code={code}/> 
  )
}

const code =
`<Modal
  className="min-w-[1200px]" // additional classes
  header="Header Text"
  subheader="Some secondary text"
  confirmButton={{
    text: "Click to Confirm",
    onClick: () => window.alert("You clicked!")
  }}
  secondaryButton={{
    text: "Less Common Option",
    tertiary: true,
    onClick: () => window.alert("You clicked the secondary option!")
  }}
  onClose={() => window.alert("Do something on close")}>
  <p>Some interesting content that belongs in a modal.</p>
  minWidth={true} // default true 
</Modal>`

export default ModalExamples