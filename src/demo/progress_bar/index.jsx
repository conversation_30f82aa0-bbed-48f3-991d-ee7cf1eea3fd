import React from 'react'
import Showcase from '../showcase'
import { ProgressBar } from "../../components"

const ProgressBarExamples = () => {
  return (
    <Showcase
      name="ProgressBar"
      examples={
        <div className='space-y-2 w-3/5'>
          <ProgressBar completed={0}/>
          <ProgressBar completed={1}/>
          <ProgressBar completed={25} displayPercentage={false}/>
          <ProgressBar completed={50}/>
          <ProgressBar completed={75} displayPercentage={false}/>
          <ProgressBar completed={99}/>
          <ProgressBar completed={100}/>
        </div>
      }
      code={code}/>
  )
}

const code =
`<ProgressBar
  completed={100}                 // the percentage of progress bar that is completed
  displayPercentage={true}        // whether to display the percentage complete besides the progress bar, default true
/>`

export default ProgressBarExamples