import React, { useState } from "react"
import Showcase from "../showcase"
import { GlobalSearch, Button } from '../../components'
import { useNavigate } from "react-router-dom"

const GlobalSearchExample = () => {
  const [modal, setModal] = useState({
    0: false,
    1: false,
    2: false,
    3: false,
    4: false,
    5: false,
  })
  const [value, setValue] = useState({
    0: "",
    1: "",
    2: "",
    3: "",
    4: "",
    5: "",
  })
  const video = {
    icon: 'movie',
    name: '<PERSON>ne',
    type: 'Video',
    url: 'video-url',
    note: '2:49:21'
  }
  const file = {
    icon: 'description',
    name: 'Dune Document',
    type: 'File',
    url: 'file-url',
  }
  const screener = {
    icon: 'live_tv',
    name: 'Crocodile DuneDee',
    type: 'Screener',
    url: 'screener-url',
  }
  const playlist = {
    icon: 'list',
    name: 'Top 2022 Dune Movies',
    type: 'Playlist',
    url: 'playlist-url',
  }
  const user = {
    icon: 'person',
    name: '<PERSON>',
    type: 'User',
    url: 'user-url',
  }
  const folder = {
    icon: 'folder',
    name: ['2022', 'Movies', 'Dune'],
    url: 'folder-url',
  }
  const onClick = url => console.log(url)
  const navigate = useNavigate()
  return (
    <Showcase
      name="Global Search"
      examples={
        <div className="space-y-2">
          {[
            { results: [video, file, screener, playlist, user, folder], pending: false, label: "Default", onClick: onClick },
            { results: [], pending: false, label: "No results", onClick: onClick },
            { results: [], pending: true, label: "Pending state", onClick: onClick },
            { results: [video], pending: false, label: "One result", onClick: onClick },
            { results: new Array(100).fill(video), pending: false, label: "Many results", onClick: onClick },
            { results: [video], pending: false, label: "Redirect OnClick", onClick: () => navigate('/') },
          ].map((input, i) =>
          <div key={i} className='pt-2'>
            <div>{input.label}</div>
            <Button minimal icon="search" onClick={() => setModal({...modal, [i]: !modal[i]})} />
            {modal[i] &&
              <GlobalSearch
                value={value[i]}
                onValueChange={(res) => setValue({...value, [i]: res})}
                pending={input.pending}
                results={value[i].length > 0 ? input.results : []}
                onClose={() => setModal({...modal, [i]: false})}
                onResultClick={input.onClick}
              />
            }
          </div>
          )}
      </div>
      }
      code={code}
    />
  )
}

const code = `<GlobalSearch
 value={value}
 onValueChange={(res) => setValue(res)}
 onClose={() => setModal(false)}
 pending={pending}
 results={[{              // List of results
    icon: 'movie',        // movie, description, live_tv, list, person, folder
    name: 'Dune',         // Use array of strings for folders
    type: 'Video',        // Video, File, Screener, Playlist, User, Folder
    path: '/video-url',   // redirect path (within app)
    note: '2:49:21'
 }]}
 onResultClick={result => navigate(result.path)}
 className="space-x-2"
 />`

export default GlobalSearchExample
