import React, { useState } from 'react'
import { Device } from '../../components'
import data from './data'
import { CineSendLogo } from '../../components/logos'
import NetworkInterfaces from '../../components/device/networking'
import Showcase from '../showcase'
import RaidHealth from '../../components/device/raid/health'

const raid =
  {
    "health_status": "Normal",
    "raid_name": "raid5 (clean)",
    "format_type": "ext4",
    "storage": {
      "used": 555168233552,
      "total": 1499184283648
    },
    "disks": [
      {
        "title": "Disk 1",
        "size": 500107862016,
        "location": "\/dev\/sda",
        "storage_pool": "\/dev\/md0",
        "estimated_lifespan": "100%",
        "bad_record_count": 0,
        "model": "WDC WD5000AZLX-00CL5A0",
        "serial_number": "WD-WCC3FR3UAZ95",
        "firmware_version": "01.01A01",
        "temperature": "29 \u00b0C",
        "health_status": "N\/A"
      },
      {
        "title": "Disk 2",
        "size": 500107862016,
        "location": "\/dev\/sdd",
        "storage_pool": "\/dev\/md0",
        "estimated_lifespan": "100%",
        "bad_record_count": 0,
        "model": "WDC WD5000AZLX-00CL5A0",
        "serial_number": "WD-WCC3F3REP1JD",
        "firmware_version": "01.01A01",
        "temperature": "29 \u00b0C",
        "health_status": "N\/A"
      },
      {
        "title": "Disk 3",
        "size": 500107862016,
        "location": "\/dev\/sdb",
        "storage_pool": "\/dev\/md0",
        "estimated_lifespan": "100%",
        "bad_record_count": 0,
        "model": "WDC WD5000AZLX-00CL5A0",
        "serial_number": "WD-WCC3F6LD5H9C",
        "firmware_version": "01.01A01",
        "temperature": "29 \u00b0C",
        "health_status": "N\/A"
      },
      {
        "title": "Disk 4",
        "size": 500107862016,
        "location": "\/dev\/sdc",
        "storage_pool": "\/dev\/md0",
        "estimated_lifespan": "100%",
        "bad_record_count": 0,
        "model": "WDC WD5000AZLX-00CL5A0",
        "serial_number": "WD-WMC3F0J17D8V",
        "firmware_version": "01.01A01",
        "temperature": "29 \u00b0C",
        "health_status": "N\/A"
      },
      {
        "title": "Disk 5",
        "size": 500073234432,
        "location": "\/dev\/sdf",
        "storage_pool": "\/dev\/sdf",
        "estimated_lifespan": "100%",
        "bad_record_count": 0,
        "model": "WDC WD5000BMVW-11S5XS0",
        "serial_number": "WD-WXK1E72AAXW3",
        "firmware_version": "01.01A01",
        "temperature": "36 \u00b0C",
        "health_status": "N\/A"
      },
      {
        "title": "Disk 6",
        "size": 500072315904,
        "location": "\/dev\/sdg",
        "storage_pool": "\/dev\/sdg",
        "estimated_lifespan": "100%",
        "bad_record_count": 0,
        "model": "WDC WD5000LMVW-11VEDS6",
        "serial_number": "WD-WX11E83HRL97",
        "firmware_version": "01.01A01",
        "temperature": "32 \u00b0C",
        "health_status": "N\/A"
      }
    ]
  }

const Raid = () => {
  // const [interfaces, setInterfaces] = useState([])
  const [mm, setMm] = useState('x')
  const [isFetching, setIsFetching] = useState(false)
  return (
    <Showcase
      name={'Raid'}
      examples={[<RaidHealth raid={raid} isRefetching={isFetching}/>]}
      code={'<RaidHealth raid={raid} isRefetching={isFetching}/>'} />
  )
}

export default Raid
