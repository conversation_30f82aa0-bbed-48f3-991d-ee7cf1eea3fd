import React, { useState } from 'react'
import { Device } from '../../components'
import data from './data'
import { CineSendLogo } from '../../components/logos'

const DeviceExamples = () => {
  const [selectedLibraryCPLs, setSelectedLibraryCPLs] = useState([])
  const [selectedDriveCPLs, setSelectedDriveCPLs] = useState([])
  return (
    <Device
      topOffsetPx={64} // when no title/settings button, even shorter offset.
      allowedSettings={false}
      printLabel={false}
      headerOn={false}
      header={<h4>Scotiabank Cineplex Toronto</h4>}
      library={data.library}
      settings={data}
      downloads={data.downloads}
      selectedLibraryCPLs={selectedLibraryCPLs}
      setSelectedLibraryCPLs={setSelectedLibraryCPLs}
      selectedDriveCPLs={selectedDriveCPLs}
      setSelectedDriveCPLs={setSelectedDriveCPLs}
      // onCopyFromLibraryToCloud={cpls => window.alert("Might copy " + cpls.length + " cpls")}
      onLibraryColorChange={(cpl, color) => {
        window.alert(`Updating ${cpl.uuid} to ${color} in library`)
      }}
      drives={data.drives}
      logo={<CineSendLogo />}
    />
  )
}

export default DeviceExamples
