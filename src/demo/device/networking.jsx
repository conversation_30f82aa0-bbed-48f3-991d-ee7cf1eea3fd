import React, { useState } from 'react'
import { Device } from '../../components'
import data from './data'
import { CineSendLogo } from '../../components/logos'
import NetworkInterfaces from '../../components/device/networking'
import Showcase from '../showcase'

const interfaces = [
  {
    "address": "**********",
    "netmask": "*************",
    "family": "IPv4",
    "mac": "d0:50:99:d3:10:d4",
    "internal": false,
    "cidr": "**********\/24",
    "name": "enp4s0",
    "label": "Eth 1",
    "description": "Internal Networking",
    "system_name": null,
    "method": null,
    "dns": "--",
    "gateway": null,
    "bytes_received": 12345213,
    "bytes_transmitted": 12553939,
    "is_internet_connected": false
  },
  {
    "address": "***********22",
    "netmask": "*************",
    "family": "IPv4",
    "mac": "d0:50:99:d3:10:d5",
    "internal": false,
    "cidr": "***********/24",
    "name": "enp4s1",
    "label": "Eth 2",
    "description": "Innernet",
    "system_name": null,
    "method": null,
    "dns": "--",
    "gateway": null,
    "bytes_received": 12222345213,
    "bytes_transmitted": 12553939,
    "is_internet_connected": true
  }

]

const Networking = () => {
  // const [interfaces, setInterfaces] = useState([])
  const [mm, setMm] = useState('x')
  const [isFetching, setIsFetching] = useState(false)
  return (
    <Showcase
      name={'NetworkInterfaces'}
      examples={[<NetworkInterfaces networkInterfaces={interfaces} isRefetching={isFetching}/>]}
      code={'<NetworkInterfaces networkInterfaces={interfaces} isRefetching={isFetching} updateDescription={} updateInterface={} updateLabel={} />'} />
  )
}

export default Networking
