import drive from './drive'
import library from './library'
import downloads from './downloads'

const drives = [
  {"is_formatting":false,"is_busy":false,"device":"/dev/sda","size":317415612416,"location_index":0,"location_name":"CRU 1","partitions":{"/dev/sda1":{"device":"/dev/sda1","is_root":false,"fix_permissions_required":false,"format_required":false},"/dev/sdb1":{"device":"/dev/sdb1","is_root":false,"fix_permissions_required":false,"format_required":false}},"cpl_count":0,"used":66207040},
  {"is_formatting":false,"has_formatting_job":true,"is_busy":false,"device":"/dev/sdb","size":2976815210496,"location_index":1,"location_name":"CRU 2","partitions":{"/dev/sdf1":{"device":"/dev/sdf1","is_root":false,"fix_permissions_required":false,"format_required":false}},"cpl_count":0,"used":1420813978944},
  {"is_formatting":false,"is_busy":true,"device":"/dev/sdf","size":2976815210496,"location_index":2,"location_name":"CRU 3","partitions":{"/dev/sdf1":{"cpls": {}, "device":"/dev/sdf1","is_root":false,"fix_permissions_required":false,"format_required":true}},"cpl_count":0,"used":1420813978944},
  {"is_formatting":true,"is_busy":false,"device":"/dev/sdg","size":2976815210496,"location_index":3,"location_name":"CRU 4","partitions":{"/dev/sdg1":{"device":"/dev/sdg1","is_root":false,"fix_permissions_required":false,"format_required":false}},"cpl_count":0,"used":1420813978944},
  drive
]

const network = {"ip_address":"**********","ftp":{"installed":true,"active":true,"shares":{"ingest":{"connection":"IPADDRESS","username":"cinesendimport","password":"1234"},"export":{"connection":"IPADDRESS","username":"cinesendexport","password":"1234"},"content":{"connection":"**********","username":"cinesendadmin","password":"1234"}}},"samba":{"installed":true,"active":false,"shares":{"ingest":{"connection":"IPADDRESS\/import","username":"cinesendsamba","password":"1234"},"export":{"connection":"IPADDRESS\/export","username":"cinesendsamba","password":"1234"}}}}

const storage = {"health_status":"Normal","raid_name":"raid5","format_type":"ext4","storage":{"used":231596359680,"total":1499184283648},"disks":[{"title":"Disk 1","size":500107862016,"location":"\/dev\/sda","storage_pool":"\/dev\/md0","estimated_lifespan":"100%","bad_record_count":0,"model":"WDC WD5000AZLX-00CL5A0","serial_number":"WD-WCC3FR3UAZ95","firmware_version":"01.01A01","temperature":"27 \u00b0C","health_status":"Normal"},{"title":"Disk 2","size":500107862016,"location":"\/dev\/sdc","storage_pool":"\/dev\/md0","estimated_lifespan":"100%","bad_record_count":0,"model":"WDC WD5000AZLX-00CL5A0","serial_number":"WD-WMC3F0J17D8V","firmware_version":"01.01A01","temperature":"27 \u00b0C","health_status":"Normal"},{"title":"Disk 3","size":500107862016,"location":"\/dev\/sdd","storage_pool":"\/dev\/md0","estimated_lifespan":"100%","bad_record_count":0,"model":"WDC WD5000AZLX-00CL5A0","serial_number":"WD-WCC3F3REP1JD","firmware_version":"01.01A01","temperature":"27 \u00b0C","health_status":"Normal"},{"title":"Disk 4","size":500107862016,"location":"\/dev\/sdb","storage_pool":"\/dev\/md0","estimated_lifespan":"100%","bad_record_count":0,"model":"WDC WD5000AZLX-00CL5A0","serial_number":"WD-WCC3F6LD5H9C","firmware_version":"01.01A01","temperature":"27 \u00b0C","health_status":"Normal"},{"title":"Disk 5","size":500073234432,"location":"\/dev\/sdg","storage_pool":"\/dev\/sdg","estimated_lifespan":"100%","bad_record_count":0,"model":"WDC WD5000BMVW-11S5XS0","serial_number":"WD-WXK1E72AAXW3","firmware_version":"01.01A01","temperature":"30 \u00b0C","health_status":"Normal"}]}

const system = { target_rate_in_mbps: 101 }

const emails = [
  { email: '<EMAIL>', download_complete: true, download_started: false, hard_drive_failure: true, device_offline: false  },
  { email: '<EMAIL>', download_complete: true, download_started: true, hard_drive_failure: false, device_offline: true }
]

const data = { library, drives, downloads, system, network, storage, email_notifications: emails }

export default data