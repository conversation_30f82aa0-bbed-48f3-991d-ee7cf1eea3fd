import React, { useState } from 'react'
import Showcase from '../showcase'
import { Geoblocker } from "../../components"

const GeoblockerExamples = () => {
  const [value, setValue] = useState({})
  return (
    <Showcase
      name="Geoblocker"
      examples={
        <Geoblocker
          value={value}
          className="border p-4"
          onChange={newValue => setValue(newValue)}/>
      }
      code={code}/>
  )
}

const code =
`<Geoblocker
  value={{
    use_blocklist_geoblocking: false,     // Handles whether we are blocking or allowing the specified countries
    geoblock_country_codes: ["CA", "US"],          // Which country codes are selected
    geoblock_region_codes: {
      CA: ["AB", "ON"],
      US: ["AK", "NY"]
    },
    geoblock_area_codes: {
      US: ["90210","37188"]
    }
  }}
  onChange={value => setValue(value)}     // Responds with the entirely updated value object
/>`

export default GeoblockerExamples