import React, { useState } from 'react'
import { Table } from '../../components'

const tableData = [
  { title: 'This row functions like a link! Try right clicking me!'},
]



const LinkTable = () => {

  return (
    <Table
      className='border-gray-200'
      widths={[200]}
      header={{
        title: <div className='text-xl font-bold flex items-center'>Link Table</div>,
        columns: [
          {
            text: 'Text',
            key: 'title'
          },
       
        ]
      }}
      body={{
        data: tableData,
        row: {
          truncate: false,
          to: {
            path: "https://cinesend.com",
            internal: false
          },
          render: [
            data => data.truncate ? <div className={'truncate'}>{data.title}</div> : data.title,
          ]
        },
        empty: {
          title: 'Custom Empty Title',
          text: 'Custom Empty Text',
          icon: '' // Material-Icon
        }
      }}/>
  )
}

export default LinkTable