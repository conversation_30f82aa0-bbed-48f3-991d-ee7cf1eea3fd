import React, { useState } from 'react'
import { Table } from '../../components'

const DraggableTable = () => {
  const [list, setList] = useState([
    { id: 1, title: 'Autosized first column', first_name: '<PERSON>', last_name: '<PERSON>', order: 0 },
    { id: 2, title: 'With clickable rows', first_name: '<PERSON>', last_name: '<PERSON>', order: 0 },
    { id: 3, title: 'Third row with very long, very long, very long, very long, very long, very long, very long truncated content', first_name: 'The', last_name: 'Flash', order: 0 },
  ])
  const [checked, setChecked] = useState([])
  return (
    <>
      <div className='text-xl pt-4 pb-2 font-bold'>Table with Draggable rows</div>
      <Table
        draggable
        widths={[25, 120, 'auto', 120, 120]}
        header={{
          columns: [
            {
              text: 'ID',
              key: '_id'
            },
            {text: 'sorted-order'},
            {
              text: 'Auto Width',
              key: 'title'
            },
            {
              text: 'First Name',
              key: 'first_name'
            },
            {
              text: 'Last Name',
              key: 'last_name'
            }
          ],
          sorting: {},
          filters: {},
          checkbox: {
            checked: checked.length > 0,
            indeterminate: checked.length !== list.length,
            onChange: () => {
              if (checked.length === 0) {
                setChecked(list.map(({ id }) => id))
              }
              else {
                setChecked([])
              }
            }
          }
        }}
        body={{
          onDragEnd: (newData) => setList(newData),
          data: list,
          row: {
            compact: true,
            truncate: true,
            checkbox: {
              checked: (data, index) => checked.includes(data.id),
              onChange: (data, index) => checked.includes(data.id)
                ? setChecked(checked.filter(i => i !== data.id))
                : setChecked([...checked, data.id])
            },
            onClick: (event, data, index) => checked.includes(data.id)
            ? setChecked(checked.filter(i => i !== data.id))
            : setChecked([...checked, data.id]),
            render: [
              data => data.id,
              data => data.order,
              data => data.title,
              data => data.first_name,
              data => data.last_name
            ]
          },
          empty: {
            title: 'Empty Data',
            text: `There's just nothin' here.`,
            icon: 'cloud' // Material-Icon
          }
        }}
        paginate={{}}/>
    </>
  )
}

export default DraggableTable
