import React from 'react'
import Showcase from '../showcase'
import LiveTable from './live_table'
import DraggableTable from './draggable_table'
import LinkTable from './link_table'

const TableExamples = () =>
  <Showcase
    name="Table"
    examples={
      <div className='space-y-4'>
        <LiveTable/>
        <LinkTable />
        <DraggableTable/>
      </div>}
    code={code}/>

const code =
`<Table
  draggable={false}
  widths={["auto", 140, 120, 120]}
  customHeader={<SomeJsxElement />} // Optional
  header={{
    columns: [
      {
        text: 'Name',
        key: 'name'
      },
      {
        text: 'Brewery Type'
      },
      {
        text: 'City',
        key: 'city'
      },
      {
        text: 'Country',
        key: 'country'
      }
    ],
    sorting: {
      onSortChange: newSorting => setSorting(newSorting),
      direction: sorting.direction,
      key: sorting.key
    },
    filters: {
      search,
      searchPlaceholder: 'Search by city, brewery, postal code',
      onSearch: value => setSearch(value)
    },
    customElement: <Button minimal={true} onClick={() => alert('Clicked me!')}>Custom Element</Button>,
    checkbox: {
      checked: checked.length > 0,
      indeterminate: checked.length !== list.length,
      onChange: () => {
        if (checked.length === 0) {
          setChecked(list.map(({ _id }) => _id))
        }
        else {
          setChecked([])
        }
      }
    }
  }}
  body={{
    data: list,
    onDragChange: () => {}
    row: {
      compact: true,
      checkbox: {
        checked: (data, index) => checked.includes(data._id),
        onChange: (data, index) => checked.includes(data._id)
          ? setChecked(checked.filter(i => i !== data._id))
          : setChecked([...checked, data._id])
      },
      to: {
        path: (data) => return "/route/" + data._id, // if the route is dynamic based on the data provied, use a function that returns a string. Otherwise pass in a string 
        internal: true // should be true if it's a route, false if it's a link
      },
      onClick: (event, data, index) => checked.includes(data._id)
      ? setChecked(checked.filter(i => i !== data._id))
      : setChecked([...checked, data._id]),
      render: [
        data => data.name,
        data => data.brewery_type,
        data => data.city,
        data => data.country
      ]
    },
    empty: {
      title: 'Custom Empty Title',
      text: 'Custom Empty Text',
      icon: 'cloud' // Material-Icon
    }
  }}
  paginate={{
    totalRows: total,
    currentPage: pagination.currentPage,
    onPageChange: page => setPage(page)
    rowsPerPage: pagination.rowsPerPage,
    onRowsPerPageChange: rowsPerPage => setRowsPerPage(rowsPerPage)
  }}/>`

export default TableExamples
