import React, { useEffect, useState, useMemo } from 'react'
import { <PERSON>, <PERSON><PERSON>, <PERSON>ut<PERSON>, Toggle, Select } from '../../components'

const baseURL = 'https://api.openbrewerydb.org/v1/breweries'
const total = 100

const filterOptions = [
  {
    label: "Insects (Checkboxes)",
    key: "insects",
    type: "checkboxes",
    searchable: true,
    options: [
      { key: "ant", label: "Ant" },
      { key: "bee", label: "Bee" },
      { key: "beetle", label: "Beetle" },
      { key: "butterfly", label: "<PERSON>" },
      { key: "cockroach", label: "Cockroach" },
      { key: "cricket", label: "Cricket" },
      { key: "dragonfly", label: "Dragonfly" },
      { key: "flea", label: "Flea" },
      { key: "fly", label: "Fly" },
      { key: "grasshopper", label: "Grasshopper" },
      { key: "hornet", label: "Horne<PERSON>" },
      { key: "ladybug", label: "Ladybug" },
      { key: "mantis", label: "Man<PERSON>" },
      { key: "mosquito", label: "Mosquito" },
      { key: "moth", label: "Moth" },
      { key: "roach", label: "Roach" },
      { key: "spider", label: "Spider" },
      { key: "termite", label: "Termite" },
      { key: "wasp", label: "Wasp" },
      { key: "weevil", label: "Weevil" },
      { key: "yellowjacket", label: "Yellowjacket" },
      { key: "aphid", label: "Aphid" },
      { key: "caterpillar", label: "Caterpillar" },
      { key: "cicada", label: "Cicada" },
      { key: "daddy_longlegs", label: "Daddy Longlegs" },
      { key: "firefly", label: "Firefly" },
      { key: "gnat", label: "Gnat" },
      { key: "locust", label: "Locust" },
      { key: "mayfly", label: "Mayfly" },
      { key: "silverfish", label: "Silverfish" },
    ],
  },
  {
    label: 'City (Checkboxes)',
    key: 'city',
    type: 'checkboxes',
    options: [
      { key: 'portland', label: 'Portland' },
      { key: 'denver', label: 'Denver' },
      { key: 'reno', label: 'Reno' }
    ]
  },
  {
    label: 'State (Radio)',
    key: 'state',
    type: 'radio_buttons',
    searchable: true,
    options: [
      { key: 'ohio', label: 'Ohio' },
      { key: 'new_york', label: 'New York' },
      { key: 'new_mexico', label: 'New Mexico' }
    ]
  },
  {
    label: 'Last Updated (Date Range)',
    key: 'updated_at',
    type: 'date_range',
    options: {
      show_time: true
    }
  },
  {
    label: 'Custom',
    key: 'test',
    type: 'custom',
    element: (props) => <Button tertiary onClick={() => console.log(props)}>Click me</Button>,
  },

]

const sortingOptions = [
  {
    key: 'name',
    label: 'Name'
  },
  {
    key: 'city',
    label: 'City'
  },
  {
    key: 'state',
    label: 'State'
  },
  {
    key: 'country',
    label: 'Country'
  }
]

const tableOptions = [
  { label: 'Live', value: 'live' },
  { label: 'Loading', value: 'loading' },
  { label: 'Error', value: 'error' },
  { label: 'Empty', value: 'empty' }
]

const LiveTable = () => {
  const [showStats, setShowStats] = useState(false)
  const [expanded, setExpanded] = useState([])
  const [useShadedBackground, setUseShadedBackground] = useState(false)
  const [tableStatus, setTableStatus] = useState('live')
  const [list, setList] = useState([])
  const [checked, setChecked] = useState([])
  const [sorting, setSorting] = useState({ direction: 'ASC', key: "name" })
  const [pagination, setPagination] = useState({ currentPage: 0, rowsPerPage: 25 })
  const [search, setSearch] = useState('')
  const [filters, setFilters] = useState()
  const aggregateChartData = (key) => useMemo(() => {
    const aggregation = list.reduce((acc, item) => {
      const existing = acc.find(x => x.name === item[key]);
      if (existing) {
        existing.value++
      } else {
        acc.push({ name: item[key], value: 1 })
      }
      return acc
    }, [])
    return aggregation
  }, [list, key])
  
  const breweryTypesData = aggregateChartData('brewery_type')
  const countriesData = aggregateChartData('country')
  const statesData = aggregateChartData('state')
  const citiesData = aggregateChartData('city')
  useEffect(() => {
    let queries = [
      `sort=${sorting.key}:${sorting.direction.toLowerCase()}`,
      `page=${pagination.currentPage + 1}`,
      `per_page=${pagination.rowsPerPage}`,
    ]
    if (filters && filters.city) {
      queries.push(`by_city=${filters.city}`)
    }
    if (filters && filters.state) {
      queries.push(`by_state=${filters.state}`)
    }
    //setList(null)
    fetch(`${baseURL}${search ? '/search?query=' + search + '&' : '?'}${queries.join("&")}`)
      .then(response => response.json())
      .then(data => setList(data))
  }, [sorting, pagination, search, filters])
  return (
    <>
      <div className='flex items-center space-x-4'>
        <div className='text-xl font-bold flex items-center'>Customizable Table</div>
        <Toggle label='Use Shaded Expansion' checked={useShadedBackground} onChange={() => setUseShadedBackground(!useShadedBackground)}/>
        <Toggle label='Show Stats' checked={showStats} onChange={() => setShowStats(!showStats)}/>
        <div className='flex items-center space-x-4 max-w-md'>
          <div className='text-gray-800 text-sm border-l-2 pl-2 whitespace-nowrap'>Table status:</div>
          <Select
            options={tableOptions}
            value={tableOptions.find(opt => opt.value === tableStatus)}
            onChange={opt => setTableStatus(opt.value)}/>
        </div>
      </div>
      <Table
        status={{
          pending: tableStatus === 'loading',
          pendingMessage: "This is a custom loading message on the table status prop...",
          error: tableStatus === 'error',
          errorMessage: "Failed to load data on purpose"
        }}
        widths={["auto", 160, 160, 160, 160]}
        // initialColumns={[0, 1]}
        header={{
          columns: [
            {
              text: 'Name',
              key: 'name'
            },
            {
              text: 'Brewery Type',
            },
            {
              text: 'City',
              key: 'city'
            },
            {
              text: 'State',
              key: 'state'
            },
            {
              text: 'Country',
              key: 'country'
            }
          ],
          sorting: {
            options: sortingOptions,
            onSortChange: newSorting => setSorting(newSorting),
            direction: sorting.direction,
            key: sorting.key,
            sortingKey: sorting.key
          },
          searching: {
            search,
            searchPlaceholder: 'Search by city, brewery, postal code',
            onSearch: value => setSearch(value),
            rightIcon: {
              icon: 'close',
              onClick: () => setSearch(""),
              tooltip: {
                text: "Clear search"
              }
            }
          },
          filtering: {
            options: filterOptions,
            filters,
            onFiltersChange: value => setFilters(value)
          },
          customElement: <Button minimal={true} onClick={() => alert('This element can be whatever you want!')}>Click me!</Button>,
          checkbox: {
            checked: checked.length > 0,
            indeterminate: list && checked.length !== list.length,
            onChange: () => {
              if (checked.length === 0) {
                setChecked(list.map(({ id }) => id))
              }
              else {
                setChecked([])
              }
            }
          }
        }}
        customSubheader={list.length > 0 && showStats ? <div className='flex justify-center'>
          <DonutChart data={breweryTypesData}  tooltip chartHeader={<h5>Brewery Types</h5>} onClick={(item) => alert(`Clicked ${item.name}`)} />
          <DonutChart data={countriesData}  tooltip chartHeader={<h5>Countries</h5>} onClick={(item) => alert(`Clicked ${item.name}`)} />
          <DonutChart data={citiesData}  tooltip chartHeader={<h5>Cities</h5>} onClick={(item) => setFilters({ city: item.name })}/>
          <DonutChart data={statesData}  tooltip chartHeader={<h5>States</h5>} onClick={(item) => setFilters({ state: item.name })}/>
        </div> : null}
        body={{
          data: tableStatus === 'empty' ? [] : list,
          row: {
            compact: true,
            truncate: true,
            checkbox: {
              checked: (data, index) => checked.includes(data.id),
              onChange: (data, index) => checked.includes(data.id)
                ? setChecked(checked.filter(i => i !== data.id))
                : setChecked([...checked, data.id])
            },
            onClick: (event, data, index) => expanded.includes(index)
              ? setExpanded(expanded.filter(i => i !== index))
              : setExpanded([...expanded, index]),
            render: [
              data => data.name,
              data => data.brewery_type,
              data => data.city,
              data => data.state,
              data => data.country
            ],
            expansion: {
              visible: (data, index) => expanded.includes(index),
              component: (data, index) => <div className={`
                border-t
                ${useShadedBackground ? 'bg-primary-200' : ''}
                py-8 px-4 flex items-center justify-center text-sm flex-col space-y-4`}>
                <div>Street: {data.street}</div>
                <div>Latitude: {data.latitude}</div>
                <div>Longitude: {data.longitude}</div>
                <div>Website: {data.website_url}</div>
                <Button
                  size='small'
                  secondary
                  onClick={() => setExpanded(expanded.filter(opt => opt !== index))}>Click me or the expand parent to collapse.</Button>
              </div>
            }
          },
          empty: {
            title: 'No breweries!',
            text: 'Try searching for something else...',
            icon: 'sports_bar' // Material-Icon
          }
        }}
        paginate={{
          totalRows: total,
          currentPage: pagination.currentPage,
          rowsPerPage: pagination.rowsPerPage,
          onPageChange: currentPage => setPagination({ ...pagination, currentPage }),
          onRowsPerPageChange: rowsPerPage => setPagination({ ...pagination, rowsPerPage })
        }}
      />
    </>
  )
}

export default LiveTable
