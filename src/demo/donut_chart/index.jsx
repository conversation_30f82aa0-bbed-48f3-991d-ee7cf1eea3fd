import React from 'react'
import Showcase from '../showcase'
import DonutChart from '../../components/donut_chart'

const DonutChartExample = () => {
  return (
    <Showcase
      name="DonutChart"
      examples={
        <div className='flex'>
          <DonutChart
            data={[
              { "name": "Big", "value": 3},
              { "name": "Medium", "value": 2 },
              { "name": "Small", "value": 2 }
            ]}
            chartHeader={<h3>Large Chart</h3>}
            centerText
            tooltip={false}
            size={'large'}
            onClick={(item, index) => {
              console.log("Click handle", item, index)
            }} />
          <DonutChart
            data={[
              { name: 'Yes', value: 92, color: 'green' },
              { name: 'No', value: 92, color: 'red' },
              { name: 'Maybe', value: 8, color: 'orange' }]}
            chartHeader={<h4>Medium Chart</h4>}
            size={'medium'}
            tooltip={false}
            centerText
            onClick={(item, index) => {
              console.log("Click handle", item, index)
            }}
          />

          <DonutChart
            data={[
              { name: 'Online', value: 10 },
              { name: 'Neither', value: 10 },
              { name: 'Both', value: 10 },
              { name: 'Offline', value: 10 }]}
            chartHeader={<h5>Small Chart</h5>}
            size={'small'}
            chartColor='#8884d8'
            hideLegend
            onClick={(item, index) => {
              console.log("Click handle", item, index)
            }}
          />
        </div>
      }
      code={code} />
  )
}

const code =
  `<DonutChart 
    data={[{ name: "Canada", value: 10, color: "#ff0000" }, // Name and value are required. If no color is provided, one will automatically generate
      { name: "Brazil", value: 10, color: "#009b3a" },  
      { name: "Australia", value: 15, color: "#00008b" },  
      { name: "India", value: 20, color: "#ff9933" },  
      { name: "France", value: 18, color: "#0055a4" }]}          
    chartHeader={<h3>Large Chart</h3>} 
    centerText  // Default false - Toggle hovered sector data appearing in middle of chart
    tooltip // Default true - Toggle tooltip on hover
    size={{  // string 'small', 'medium', 'large' OR size object
      hoverRadiusIncrease: 3, // Value applied to inner and outer radius when hovered
      height: 400, // Height of container
      width: 400, // Width of continer
      outerRadius: 150, 
      innerRadius: 135
    }}
    chartColor='red' // Optional - if provided all elements in chart will be this color
    hideLegend // Default false
    legendHeight=50 // Height for legend before it starts to scroll. 
    onClick={(item, index) => console.log("Click handle", item, index) } 
  />`

export default DonutChartExample