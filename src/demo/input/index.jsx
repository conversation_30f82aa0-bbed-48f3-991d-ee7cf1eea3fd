import React, { useState } from 'react'
import Showcase from '../showcase'
import { Input } from "../../components"
import { Star, InfoCircle, Search } from "../../components/icons"
import createNumberMask from 'text-mask-addons/dist/createNumberMask'

const InputExamples = () => {
  const [text, setText] = useState({
    0: "Placeholder",
    1: "",
    2: "",
    3: "",
    4: "Placeholder",
    5: "",
    6: "",
    7: "",
    8: "",
    9: "",
    10: "",
    11: "",
    12: "",
    13: "",
    14: "",
    15: "",
    16: "",
    17: "",
    18: "2",
    19: "some-secret-password",
    20: "123-456-789",
    21: "123-456-789",
    22: "123-456-789",
    22: "123-456-789",
    23: "12:00",
    24: "12:00",
    25: "",
  })

  const hoverIcon = { icon: InfoCircle, tooltip: { direction: "top", text: "This is some information about this field." } }
  const clickIcon = { icon: InfoCircle, onClick: () => window.alert("You clicked me!") }
  const hoverAndClickIcon = { ...hoverIcon, onClick: () => window.alert("You clicked me!") }
  const customCopyIcon = { tooltip: { text: "Click to copy 12345!", copy: { value: "12345", onCopyMessage: "You copied 12345!" } } }
  const defaultCopyIcon = { tooltip: { copy: { } } }
  const numberMask = {
    includeThousandsSeparator: false,
    prefix: "",
    allowDecimal: true,
    decimalSymbol: ":",
    requireDecimal: true
  }

  return (
    <Showcase
      name="Input"
      examples={
        <div className='space-y-2'>
          {[
            { id: 'input-1' },
            { label: 'Label' },
            { label: { text: 'Label', icon: clickIcon } },
            { label: { text: 'Label', icon: hoverIcon } },
            { label: { text: 'Label', icon: [clickIcon, hoverIcon] } },
            { label: 'Label', description: 'Description' },
            { label: 'Label', description: 'Description', message: 'Message' },
            { label: 'Label', width: 'w-full', description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus nulla enim, finibus nec felis ut, pharetra bibendum ex. Vivamus sed tortor eu enim vulputate pellentesque. Ut tempus elementum eleifend. Duis tempor, arcu vel rutrum porta, nulla neque elementum magna, semper lacinia mi justo vel ante. ' },
            { label: 'Custom Width', inputWidth: 'w-full' },
            { label: 'Left Icon Hover', input: { leftIcon: hoverIcon } },
            { label: 'Right Icon Click', input: { rightIcon: hoverAndClickIcon } },
            { label: 'Both Icons', input: { leftIcon: hoverIcon, rightIcon: clickIcon } },
            { label: 'Right Icon Default Copy', input: { rightIcon: defaultCopyIcon } },
            { label: 'Right Icon Copy', copyIcon: true },
            { label: 'Left Icon Custom Message', input: { leftIcon: customCopyIcon } },
            { message: { text: 'Message Icon Left with Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus nulla enim, finibus nec felis ut, pharetra bibendum ex. Vivamus sed tortor eu enim vulputate pellentesque. Ut tempus elementum eleifend. Duis tempor, arcu vel rutrum porta, nulla neque elementum magna, semper lacinia mi justo vel ante.', icon: { icon: Star } } },
            { label: 'Disabled Input', disabled: true },
            { label: 'Success Input', success: true },
            { label: 'Error Input', error: true },
            { label: 'Numeric Input', type: 'number', min: '1', max: '20' },
            { label: 'Password Input', type: 'password' },
            { label: 'Toggle-enabled Hidden option', hidden: true },
            { label: 'Hidden and copy option', hidden: true, copyIcon: true },
            { label: 'Disabled and copy option', disabled: true, copyIcon: true },
            { label: 'Both Icons and hidden', disabled: true, hidden: true, input: { leftIcon: hoverIcon, rightIcon: clickIcon } },
            { label: 'Masked Input', mask: createNumberMask(numberMask) },
            { label: 'No border', border: false },
            { label: 'Phone Number Input', phoneNumber: true },
            { label: 'Black Background TransparentInput', use_black_background: true, inputClassName: "focus:text-white text-white bg-transparent" },
            { label: 'Black Background White Input', use_black_background: true },
            {
              label: {
                text: 'Custom background on both icons',
                textColor: 'text-white'
              },
              input: { leftIcon: { icon: Search, iconClassName: "bg-black text-gray-300" }, rightIcon: { ...clickIcon, iconClassName: "bg-black text-gray-300" }},
              use_black_background: true,
              border: false,
              borderClassName: 'border border-gray-100',
              inputClassName: "bg-black text-white focus:text-white"
            },

            // { label: 'Label Icon Left', icon: iconProps },
            // { label: 'Label Icon Right', icon: { ...iconProps, position: "right" } },
            // { label: 'Input Icon Left', icon: { ...iconProps, location: "input"} },
            // { label: 'Input Icon Right', icon: { ...iconProps, position: "right", location: "input"} },
            // { label: 'Input Icon Both', icon: { ...iconProps, position: "both", location: "input"} },

            // { message: 'Message Icon Right', icon: { ...iconProps, position: "right", location: "message" } },
            // { label: 'Small Text', description: "Description", message: "Message", icon: { ...iconProps, position: "right", location: "label", size: "small" } },
            // { label: 'Input with default text', placeholder: "Placeholder Text" },
            // { label: 'Input with copy option', placeholder: "Placeholder Text", copy: copyProps },
          ].map((input, i) =>
            <div key={i} className={`pt-2 ${input.use_black_background ? 'bg-black p-2' : ''}`}>
              <Input
                value={text[i]}
                placeholder='This is a placeholder'
                onChange={(e) => {
                  if (e) {
                    e.target ? setText({ ...text, [i]: e.target.value}) : setText({ ...text, [i]: e})
                  }}
                }
                {...input}
              />
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<Input
  id={"unique-id"}                      // optional
  value={text}                          // required
  type={"text"}                         // default text, or number
  min={"1"}                             // optional, min allowable value when type is number
  max={"20"}                            // optional, max allowable value when type is number
  onChange={(input) => setText(input)}  // required
  onBlur={(input) => setText(input)}    // default nothing
  label={"My label"}                    // default no label -- can be an object label={ text: "My Label", icon: { icon: InfoCircle, onClick: () => window.alert("You clicked me!") }
  description={"Custom Description"}    // default no description
  message={"Custom Message"}            // default no message -- can be an object message={ text: "My Message", icon: { icon: InfoCircle, onClick: () => window.alert("You clicked me!") }
  disabled={true}                       // default false
  success={true}                        // default false
  error={true}                          // default false
  width={'w-[100px]'}                   // default 240px
  leftIcon={
    icon: Star,                         // icon to be displayed
    size: "medium"                      // default medium, small, large
  }
  rightIcon={
    size: "medium"                      // default medium, small, large
    tooltip: {
      text: "Click to copy 12345!",
      copy: {
        value: "12345",
        onCopyMessage: "You copied 12345!"
      }
    }
  }
  mask={maskOptions}                    // default null, specify an input mask         
  phoneNumber={false}                    // default false, specify if the input should be for a phone number
/>`

export default InputExamples
