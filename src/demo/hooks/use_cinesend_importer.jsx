import { useState } from "react"

const projectList = [
  {
    _id: "subFolderFileList2",
    title: "Cars",
    active_poster_id: "653c222c50a6d8bfff0e2b2e",
    poster:
      "https://images.dev.cinesend.com//projects/653c0ff850a6d8bfff0e2b29/posters/653c222c50a6d8bfff0e2b2e/653c222c50a6d8bfff0e2b2e.jpg",
  },
  {
    _id: "subFolderFileList2",
    title: "Trucks",
    active_poster_id: "652d48de66099d43390edc42",
    poster:
      "https://images.dev.cinesend.com//projects/650b004e9b91e2811906c892/posters/652d48de66099d43390edc42/652d48de66099d43390edc42.jpg",
  },
  {
    _id: "subFolderFileList2",
    title: "Planes",
    poster: "",
  },
]

const parentFileList = [
  {
    _id: "subFolderFileList2",
    name: "Really Long Folder Name 1",
    type: "folder",
  },
  {
    _id: "2234abcd5678efgh9101ijkl",
    name: "caption_file.vtt",
    extension: "vtt",
    size: 1233343
  },
  {
    _id: "3234abcd5678efgh9101ijkl",
    name: "audioFile.mp3",
    extension: "mp3",
    size: 1233343
  },
  {
    _id: "5234abcd5678efgh9101ijkl",
    name: "stockVid6.mp4",
    extension: "mp4",
    size: 1233343
  },
  {
    _id: "5234abcd5678efgh9101ijkasc",
    name: "document.txt",
    extension: "txt",
    size: 1233343
  },
]

const subFolderFileList2 = [
  {
    _id: "subFolderFileList3",
    name: "Really Long Folder Name 2",
    type: "folder",
  },
  {
    _id: "5234abcd5678efgh9101ijkl",
    name: "audio_file.mp3",
  },
]

const subFolderFileList3 = [
  {
    _id: "subFolderFileList4",
    name: "Cars Folder 1",
    type: "folder",
  },
  {
    _id: "653fc58aa3e5567bd80a18c2",
    name: "stockVid3 long title.mp4",
    extension: "mp4",
    size: 1233343
  },
  {
    _id: "653fc30f9cfadfd57b0a2ef2",
    name: "stockVid2.mp3",
    extension: "mp3",
    size: 1233343
  },
]

const subfolderBreadcrumbList1 = [
  {
    _id: "subFolderFileList2",
    name: "Really Long Folder Name 1",
    type: "folder",
  },
]

const subfolderBreadcrumbList2 = [
  ...subfolderBreadcrumbList1,
  ...[
    {
      _id: "subFolderFileList3",
      name: "Really Long Folder Name 2",
      type: "folder",
    },
  ],
]

const subfolderBreadcrumbList3 = [
  ...subfolderBreadcrumbList2,
  ...[
    {
      _id: "subFolderFileList4",
      name: "Cars Folder 1",
      type: "folder",
    },
  ],
]

export default function useCineSendImporter() {
  const [breadcrumbList, setbreadcrumbList] = useState([])
  const [status, setStatus] = useState("READY")
  const [fileList, setFileList] = useState(parentFileList)

  const onProjectClick = (projectId) => {
    console.log("project selected, fetching files", projectId)
    fetchFileList(projectId)
  }

  const getRootFolderFileList = (callback) => {
    setStatus("PENDING")
    fetchSubFolderFileList("projects").then((res) => {
      setStatus("READY")
      setFileList(res.files)
      setbreadcrumbList(res.breadcrumb)
      if (typeof callback === "function") {
        callback()
      }
    })
  }

  const onOpenFolderClick = async (folderId) => {
    setStatus("PENDING")
    fetchSubFolderFileList(folderId).then((res) => {
      setStatus("READY")
      setFileList(res.files)
      setbreadcrumbList(res.breadcrumb)
    })
  }

  const onImportFilesClick = async (files, onCompleteCallback, onErrorCallback) => {
    console.log("Importing files", files)

    try {
      setTimeout(() => onCompleteCallback(files), 1000)
    } catch (error) {
      onErrorCallback(error)
    }
  }

  const fetchSubFolderFileList = async (folderId) => {
    let returnFileList = null
    let returnbreadcrumbList = null

    if (folderId === "subFolderFileList2") {
      returnFileList = subFolderFileList2
      returnbreadcrumbList = subfolderBreadcrumbList1
    } else if (folderId === "subFolderFileList3") {
      returnFileList = subFolderFileList3
      returnbreadcrumbList = subfolderBreadcrumbList2
    } else if (folderId === "subFolderFileList4") {
      returnFileList = []
      returnbreadcrumbList = subfolderBreadcrumbList3
    } else if (folderId === "projects") {
      returnFileList = parentFileList
      returnbreadcrumbList = []
    }

    return new Promise((resolve) =>
      setTimeout(
        () =>
          resolve({
            files: returnFileList,
            breadcrumb: returnbreadcrumbList,
          }),
        1000
      )
    )
  }

  const fetchFileList = async (folderId) => {
    return new Promise((resolve) => setTimeout(() => resolve({ files: parentFileList, breadcrumb: [] }), 1000))
  }

  const onAllFilesClick = (folderId) => {
    setStatus("PENDING")
    fetchFileList().then((res) => {
      setStatus("READY")
      setFileList(res.files)
      setbreadcrumbList(res.breadcrumb)
    })
  }

  return {
    projectList,
    breadcrumbList,
    status,
    fileList,
    onProjectClick,
    onOpenFolderClick,
    onAllFilesClick,
    onImportFilesClick,
    getRootFolderFileList,
  }
}