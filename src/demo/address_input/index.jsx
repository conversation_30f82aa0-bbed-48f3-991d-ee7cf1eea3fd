import React, { useState } from 'react'
import Showcase from '../showcase'
import { AddressInput } from "../../components"

const AddressInputExample = () => {
  const [addressValue, setAddressValue] = useState({
    address1: '',
    address2: '',
    country: '',
    city: '',
    province: '',
    postal: '',
  })
  const [addressValue2, setAddressValue2] = useState({
    address1: '',
    address2: '',
    country: '',
    city: '',
    province: '',
    postal: '',
  })

  return (
    <Showcase
      name="AddressInput"
      examples={
        <div className='space-y-6'>
          <div className='py-6 px-6 space-y-6 w-[1000px] rounded-lg border shadow-sm bg-white'>
            <h4>Query Off</h4>
            <AddressInput
              value={addressValue}
              onChange={setAddressValue}
              onBlur={(e) => console.log(e)}
              useQuery={false}
            />
          </div>
          <div className='py-6 px-6 space-y-6 w-[1000px] rounded-lg border shadow-sm bg-white'>
            <h4>Query On</h4>
            <AddressInput
              value={addressValue2}
              onChange={setAddressValue2}
              onBlur={(e) => console.log(e)}
            />
          </div>
        </div>
      }
      code={code}/>
  )
}

const code =
`<AddressInput
  value={value}                     // object containing the address values
  onChange={() => setValue}    // onChange function for the object containing the address values
  onBlur={() => setValue}      // function to set the object containing the address values
  useQuery={true}                   // default true, whether or not to use google maps to autocomplete the address
/>`

export default AddressInputExample
