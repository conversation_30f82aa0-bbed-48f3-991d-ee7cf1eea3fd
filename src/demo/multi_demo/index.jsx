import React, { useState } from 'react'
import { Input, Select, DatePicker, InputArea, Checkbox, Toggle, Button, Radio, SegmentedControl } from '../../components'

const MultiDemo = () => {
  const [disabled, setDisabled] = useState(false)
  return (
    <div className='border-t mt-2 py-4 space-y-2'>
      <div className='flex items-center justify-between'>
        <h4>Multiple Components Demo</h4> 
        <Toggle checked={disabled} onChange={() => setDisabled(!disabled)} label='Disabled?'/>
      </div>
      <div className='flex items-center space-x-2'>
        <Input disabled={disabled} label="Input" placeholder="Input 1"/>
        <Input disabled={disabled} label="Input" placeholder="Input 2"/>
      </div>
      <div className='flex items-center space-x-2'>
        <Select disabled={disabled} label="Select" placeholder="Select 1"/>
        <Select disabled={disabled} label="Select" placeholder="Select 2"/>
        <Select disabled={disabled} label="Select" placeholder="Select 3"/>
      </div>
      <div className='flex items-center space-x-2'>
        <DatePicker disabled={disabled} label="Datepicker" buttonText="Datepicker 1"/>
        <DatePicker disabled={disabled} label="Datepicker" buttonText="Datepicker 2"/>
        <DatePicker disabled={disabled} label="Datepicker" buttonText="Datepicker 3"/>
        <DatePicker disabled={disabled} label="Datepicker" buttonText="Datepicker 4"/>
      </div>
      <div className='grid grid-cols-5 items-center space-x-2'>
        <div className='col-span-2'>
          <Input disabled={disabled} label="Input" placeholder="Spread 2/5"/>
        </div>
        <div className='col-span-2'>
          <Select disabled={disabled} label="Select" className='col-span-2' placeholder="Spread 2/5"/>
        </div>
        <DatePicker disabled={disabled} label="Datepicker" buttonText="Spread 1/5"/>
      </div>
      <div className='flex items-center space-x-2'>
        <Input disabled={disabled} label="Input" width="w-1/4"/>
        <Select disabled={disabled} label="Select" width="w-1/4"/>
        <DatePicker disabled={disabled} label="Datepicker" width="w-1/4"/>
        <Button className="mt-5 w-1/4" tertiary type='neutral'>Inline Button</Button>
      </div>
      <div className='flex flex-col space-y-2'>
        <Input disabled={disabled} label="Input"/>
        <Select disabled={disabled} label="Select"/>
        <SegmentedControl disabled={disabled} label="Segmented Control" options={[{label: 'One', value: 'one'}, {label: 'Two', value: 'two'}]} value="one" onChange={() => {}}/>
        <DatePicker disabled={disabled} label="Datepicker"/>
        <InputArea disabled={disabled} label="Input Area"/>
        <Checkbox disabled={disabled} label="Checkbox"/>
        <Toggle disabled={disabled} label="Toggle"/>
        <Radio disabled={disabled} label="Radio"/>
      </div>
    </div>  
  )
}

export default MultiDemo