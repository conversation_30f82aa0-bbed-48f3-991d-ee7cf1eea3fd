import React from 'react'
import { But<PERSON> } from '../../components'
import { Star, Check, MoreVert } from '../../components/icons'
import Showcase from '../showcase'

const ButtonExamples = () =>
  <Showcase 
    name="Button"
    examples={<div className='space-y-2'>
      <div className='flex items-center space-x-2'>
        <Button onClick={() => window.alert("You clicked me!")}>Default <PERSON><PERSON></Button>
        <Button disabled onClick={() => window.alert("You clicked me!")}>Disabled But<PERSON></Button>
        <Button icon={"star_outline"} onClick={() => window.alert("You clicked me!")}>Icon Left</Button>
        <Button icon={Star} iconPosition="right" onClick={() => window.alert("You clicked me!")}>Icon Right</Button>
        <Button icon={Star} onClick={() => window.alert("You clicked me!")}></Button>
        <Button icon={"movie"} onClick={() => window.alert("You clicked me!")}></Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button size="small" onClick={() => window.alert("You clicked me!")}>Small Button</Button>
        <Button size="small" icon={"star_outline"} onClick={() => window.alert("You clicked me!")}>Icon Left</Button>
        <Button size="small" icon={Star} iconPosition="right" onClick={() => window.alert("You clicked me!")}>Icon Right</Button>
        <Button size="small" icon={Star} onClick={() => window.alert("You clicked me!")}></Button>
        <Button size="small" icon={"movie"} onClick={() => window.alert("You clicked me!")}></Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button size="large" onClick={() => window.alert("You clicked me!")}>Large Button</Button>
        <Button size="large" icon={"star_outline"} onClick={() => window.alert("You clicked me!")}>Icon Left</Button>
        <Button size="large" icon={Star} iconPosition="right" onClick={() => window.alert("You clicked me!")}>Icon Right</Button>
        <Button size="large" icon={Star} onClick={() => window.alert("You clicked me!")}></Button>
        <Button size="large" icon={"movie"} onClick={() => window.alert("You clicked me!")}></Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button type="success" onClick={() => window.alert("You clicked me!")}>Success Button</Button>
        <Button type="warning" onClick={() => window.alert("You clicked me!")}>Warning Button</Button>
        <Button type="error" onClick={() => window.alert("You clicked me!")}>Error Button</Button>
        <Button type="info" onClick={() => window.alert("You clicked me!")}>Info Button</Button>
        <Button type="neutral" onClick={() => window.alert("You clicked me!")}>Neutral Button</Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button secondary onClick={() => window.alert("You clicked me!")}>Secondary Button</Button>
        <Button secondary type="success" onClick={() => window.alert("You clicked me!")}>Secondary</Button>
        <Button secondary type="warning" onClick={() => window.alert("You clicked me!")}>Secondary</Button>
        <Button secondary type="error" onClick={() => window.alert("You clicked me!")}>Secondary</Button>
        <Button secondary type="info" onClick={() => window.alert("You clicked me!")}>Secondary</Button>
        <Button secondary type="neutral" onClick={() => window.alert("You clicked me!")}>Secondary</Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button tertiary onClick={() => window.alert("You clicked me!")}>Tertiary Button</Button>
        <Button tertiary type="success" onClick={() => window.alert("You clicked me!")}>Tertiary</Button>
        <Button tertiary type="warning" onClick={() => window.alert("You clicked me!")}>Tertiary</Button>
        <Button tertiary type="error" onClick={() => window.alert("You clicked me!")}>Tertiary</Button>
        <Button tertiary type="info" onClick={() => window.alert("You clicked me!")}>Tertiary</Button>
        <Button tertiary type="neutral" onClick={() => window.alert("You clicked me!")}>Tertiary</Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button minimal onClick={() => window.alert("You clicked me!")}>Minimal Button</Button>
        <Button minimal type="success" icon={Check} onClick={() => window.alert("You clicked me!")}>Minimal</Button>
        <Button minimal type="warning" icon={Check} onClick={() => window.alert("You clicked me!")}>Minimal</Button>
        <Button minimal type="error" onClick={() => window.alert("You clicked me!")}>Minimal</Button>
        <Button minimal type="info" onClick={() => window.alert("You clicked me!")}>Minimal</Button>
        <Button minimal type="neutral" onClick={() => window.alert("You clicked me!")}>Minimal</Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button link onClick={() => window.alert("You clicked me!")}>Link Button</Button>
        <Button link type="success" icon={Check} onClick={() => window.alert("You clicked me!")}>Link</Button>
        <Button link type="warning" icon={Check} onClick={() => window.alert("You clicked me!")}>Link</Button>
        <Button link type="error" onClick={() => window.alert("You clicked me!")}>Link</Button>
        <Button link type="info" onClick={() => window.alert("You clicked me!")}>Link</Button>
        <Button link type="neutral" onClick={() => window.alert("You clicked me!")}>Link</Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button tertiary icon={MoreVert} iconClassName={"w-6 h-6"} onClick={() => window.alert("You clicked me!")}/>
        <Button tertiary type="success" icon={MoreVert} onClick={() => window.alert("You clicked me!")}/>
        <Button tertiary type="warning" icon={MoreVert} onClick={() => window.alert("You clicked me!")}/>
        <Button secondary type="error" icon={MoreVert} onClick={() => window.alert("You clicked me!")}/>
        <Button secondary type="info" icon={MoreVert} onClick={() => window.alert("You clicked me!")}/>
        <Button secondary type="neutral" icon={MoreVert} onClick={() => window.alert("You clicked me!")}/>
      </div>
      <div className='flex items-center space-x-2'>
        <Button disabled onClick={() => window.alert("You clicked me!")}>Disabled Button</Button>
        <Button disabled type="success" onClick={() => window.alert("You clicked me!")}>Disabled Success Button</Button>
        <Button disabled tertiary onClick={() => window.alert("You clicked me!")}>Disabled Tertiary Button</Button>
        <Button disabled secondary onClick={() => window.alert("You clicked me!")}>Disabled Secondary Button</Button>
        <Button disabled tertiary type="success" icon={Star} onClick={() => window.alert("You clicked me!")}/>
      </div>
      <div className='flex items-center space-x-2'>
        <Button type="error" size="large" icon="error" iconPosition="right" onClick={() => window.alert("You clicked me!")}>Large Error Button Icon Right</Button>
      </div>
      <div className='flex items-center space-x-2'>
        <Button tooltip={{ text: "Hello!", direction: "left" }}type="success" onClick={() => window.alert("You clicked me!")}>Tooltip Left Button</Button>
        <Button tooltip={{ text: "Hello!", direction: "right" }}type="warning" onClick={() => window.alert("You clicked me!")}>Tooltip Right Button</Button>
        <Button tooltip={{ text: "Hello!", direction: "top" }}type="error" onClick={() => window.alert("You clicked me!")}>Tooltip Top Button</Button>
        <Button tooltip={{ text: "Hello!", direction: "bottom" }}type="info" onClick={() => window.alert("You clicked me!")}>Tooltip Bottom Button</Button>
      </div>
    </div>}
    code={code}/>

const code =
`<Button
  size="medium"             // small, medium, or large
  type="primary"            // primary, info, success, warning, error
  secondary={false}         // represents the secondary colours for each value (not currently setup)
  tertiary={false}          // represents outlined versions of primary
  link={false}              // represents the link version with no outline
  disabled={false}          // true, false
  icon="star_outline"       // uses material icons https://fonts.google.com/icons?selected=Material+Icons
  iconPosition="left"       // left, right
  onClick={() => window.alert("Clicked!")}
  >
  Click Me!
</Button>`

export default ButtonExamples