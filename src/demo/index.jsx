import React, { useEffect, useState } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import Home from "./home";
import Button from "./button";
import Modal from "./modal";
import Table from "./table";
import Input from "./input";
import InputArea from "./inputArea";
import Checkbox from "./checkbox";
import Radio from "./radio";
import ButtonDropdown from "./buttonDropdown";
import Tag from "./tag";
import Toggle from "./toggle";
import Chip from "./chip";
import Select from "./select";
import Status from "./status";
import DatePicker from "./datePicker";
import DateRange from "./dateRange";
import Snackbar from "./snackbar";
import HeaderBar from "./header_bar";
import Sidebar from "./sidebar";
import FileUpload from "./file_upload";
import ImageUpload from "./image_upload";
import VideoUpload from "./video_upload";
import UploadTab from "./upload_tab";
import SegmentedControl from "./segmented_control";
import Geoblocker from "./geoblocker";
import GlobalSearch from "./global_search";
import Device from "./device";
import ProgressBar from "./progress_bar";
import AddressInput from "./address_input";
import CreditCardInput from "./credit_card_input";
import Networking from './device/networking'
import Raid from './device/raid'
import PosterExample from "./poster";
import ImageExample from "./image";
import ThumbnailExample from "./thumbnail";
import GridExample from "./grid";
import TableGrid from "./table_grid";
import DonutChart from "./donut_chart";
import SelectFiles from "./select_files";

const Example = () => {
  const [mainTheme, setMainTheme] = useState('light')
  const [sidebarTheme, setSidebarTheme] = useState('dark')
  const [lockExpanded, setLockExpanded] = useState(false)
  
  useEffect(() => {
    if (mainTheme === 'dark') {
      document.documentElement.classList.add('dark')
    }
    else {
      document.documentElement.classList.remove('dark')
    }
  }, [mainTheme])
  return (
    <div className={`flex h-screen w-screen font-content`}>
      <div className={`flex ${sidebarTheme}`}>
        <Sidebar lockExpanded={lockExpanded}/>
      </div>
      <div className={`${mainTheme} w-full h-full overflow-auto dark:bg-primary-900 dark:text-gray-200`}>
        <HeaderBar
          mainTheme={mainTheme}
          setMainTheme={setMainTheme}
          sidebarTheme={sidebarTheme}
          setSidebarTheme={setSidebarTheme}
          lockExpanded={lockExpanded}
          setLockExpanded={setLockExpanded}
          />
        <div className='dark:bg-primary-900 dark:text-gray-200'>
          <Routes>
            <Route path="" element={<Home />} />
            <Route
              path="common"
              element={<Navigate replace to="/common/buttons" />}
            />
            <Route path="common/buttons" element={<Button />} />
            <Route path="common/button-dropdowns" element={<ButtonDropdown />} />
            <Route path="common/modals" element={<Modal />} />
            <Route
              path="data"
              element={<Navigate replace to="/data/tables" />}
            />
            <Route path="data/tables" element={<Table />} />
            <Route path="data/grids" element={<GridExample />} />
            <Route path="data/table-grids" element={<TableGrid />} />
            <Route
              path="inputs"
              element={<Navigate replace to="/inputs/text" />}
            />
            <Route path="inputs/text" element={<Input />} />
            <Route path="inputs/area" element={<InputArea />} />
            <Route path="inputs/select" element={<Select />} />
            <Route path="inputs/select_files" element={<SelectFiles />} />
            <Route path="inputs/date" element={<DatePicker />} />
            <Route path="inputs/date_range" element={<DateRange />} />
            <Route path="inputs/checkbox" element={<Checkbox />} />
            <Route path="inputs/toggle" element={<Toggle />} />
            <Route
              path="inputs/segmented-control"
              element={<SegmentedControl />}
            />
            <Route path="inputs/radio" element={<Radio />} />
            <Route path="inputs/address-input" element={<AddressInput />} />
            <Route
              path="inputs/credit-card-input"
              element={<CreditCardInput />}
            />
            <Route
              path="extras"
              element={<Navigate replace to="/extras/tags" />}
            />
            <Route path="extras/tags" element={<Tag />} />
            <Route path="extras/chips" element={<Chip />} />
            <Route path="extras/status" element={<Status />} />
            <Route path="extras/snackbar" element={<Snackbar />} />
            <Route path="extras/geoblocker" element={<Geoblocker />} />
            <Route path="extras/progress-bar" element={<ProgressBar />} />
            <Route path="extras/global-search" element={<GlobalSearch />} />
            <Route path="extras/donut-chart" element={<DonutChart />} />
            <Route
              path="images"
              element={<Navigate replace to="/images/image" />}
            />
            <Route path="images/poster" element={<PosterExample />} />
            <Route path="images/image" element={<ImageExample />} />
            <Route path="images/thumbnail" element={<ThumbnailExample />} />
            <Route
              path="uploads"
              element={<Navigate replace to="/uploads/files" />}
            />
            <Route path="uploads/files" element={<FileUpload />} />
            <Route path="uploads/images" element={<ImageUpload />} />
            <Route path="uploads/videos" element={<VideoUpload />} />
            <Route path="uploads/tab" element={<UploadTab />} />
            <Route
              path="reusables"
              element={<Navigate replace to="/reusables/devices" />}
            />
            <Route path="reusables/devices" element={<Device />} />
            <Route path="reusables/networking" element={<Networking />} />
            <Route path="reusables/storage" element={<Raid />} />
          </Routes>
        </div>
      </div>
    </div>
  )
};

export default Example;
