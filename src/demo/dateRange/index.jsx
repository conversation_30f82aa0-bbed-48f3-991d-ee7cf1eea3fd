import React, {useState} from 'react'
import Showcase from "../showcase";
import { DateRange } from "../../components";
import {Star} from "../../components/icons";

import "react-datepicker/dist/react-datepicker.css";

const DateRangeExample = () => {
  const newDate = new Date()
  const futureDate = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate()+7)

  const [startDate, setStartDate] = useState({
    0: newDate,
    1: newDate,
    2: newDate,
    3: null,
    4: newDate,
    5: null,
    6: newDate,
    7: newDate,
    8: newDate,
  })

  const [endDate, setEndDate] = useState({
    0: futureDate,
    1: futureDate,
    2: futureDate,
    3: futureDate,
    4: null,
    5: null,
    6: futureDate,
    7: futureDate,
    8: futureDate,
  })

  return (
    <Showcase
      name="DateRange"
      examples={
        <div className='space y-2'>
          {[
            { id: "id" },
            { label: "Date Range with Label" },
            { label: "Date Range with Icon", icon: Star },
            { label: "Date Range with no start time" },
            { label: "Date Range with no end time" },
            { label: "Date Range with no set time" },
            { label: "Date Range with disabled start time", disableStart: true },
            { label: "Date Range with disabled end time", disableEnd: true },
            { label: "Date Range with disabled start and end", disableStart: true, disableEnd: true },
          ].map((input, i) =>
            <div key={i} className={'pt-2'}>
              <DateRange
                startDate={startDate[i]}
                endDate={endDate[i]}
                changeStartDate={(newDate) => setStartDate({...startDate, [i]: newDate})}
                changeEndDate={(newDate) => setEndDate({...endDate, [i]: newDate})}
                icon={input.icon}
                label={input.label}
                disableStart={input.disableStart}
                disableEnd={input.disableEnd}
              />
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<DateRange
    startDate={startDate}                                 // required
    endDate={endDate}                                     // required
    changeStartDate={(newDate) => setStartDate(newDate)}  // required
    changeEndDate={(newDate) => setEndDate(newDate)}      // required
    icon={Icon}                                           // optional
    label={"My Label"}                                    // optional
    disableStart={true}                                   // default false
    disableEnd={true}                                     // default false
/>`

export default DateRangeExample;
