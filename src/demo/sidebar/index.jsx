import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
// import DCDCLogo from './DCDCLogo'
import Sidebar from '../../components/sidebar'

const ThemeSidebar = ({ lockExpanded }) => {
  const navigate = useNavigate()
  const pathname = useLocation().pathname
  const [showAvatar, setShowAvatar] = useState()
  const [showSecondaryHeader, setShowSecondaryHeader] = useState(false)
  return (
    <Sidebar
      pathname={pathname}
      lockExpanded={lockExpanded}
      small
      toggleKey='t'
      // topMargin={0}
      footer={{
        name: "CineSend Developer",
        organization: "BitCine Technologies",
        photoURL: showAvatar ? "https://cinesend-public-assets-dev.s3.amazonaws.com/cinesend-theme-upload-zone/obama.jpg" : null,
        dropdownContent: [
          {
            text: "Click me!",
            icon: "star",
            onClick: () => window.alert("You clicked me!")
          },
          {
            text: "Toggle avatar!",
            icon: "settings",
            onClick: () => setShowAvatar(!showAvatar)
          },
          {
            text: "Toggle secondary header!",
            icon: "top_panel_open",
            onClick: () => setShowSecondaryHeader(!showSecondaryHeader)
          },
          {
            text: "This uses a Material Icon",
            icon: 'shopping_cart',
            onClick: () => setShowAvatar(!showAvatar)
          },
          {
            text: "Log out",
            onClick: () => window.alert("You could use this to log out!"),
            icon: "person"
          }
        ]
      }}
      header={{
        // logo: <DCDCLogo/>,
        onLogoClick: () => navigate('/'),
        appName: "Theme",
        apps: [
          {
            name: "Theme",
            icon: "star",
            description: "This is the CineSend Theme package, you're on it now!",
            onClick: () => window.alert("Switch to CineSend Theme!")
          },
          {
            name: "Files",
            icon: "description",
            description: "Upload, request, and share your files securely.",
            onClick: () => window.alert("Switch to CineSend Files!")
          },
          {
            name: "On Demand",
            icon: "ondemand_video",
            description: "Share your content with audiences worldwide.",
            onClick: () => window.alert("Switch to CineSend On Demand!")
          },
          {
            name: "Requests",
            icon: "cloud_upload",
            description: "Request files and manage your physical CineSend devices.",
            onClick: () => window.alert("Switch to CineSend Requests!")
          }
        ]
      }}
      secondaryHeader={
        showSecondaryHeader
          ? <div className='text-xs text-black p-4 my-4 border-b border-white bg-white'>We can put some custom content in this secondary header component.</div>
          : null
      }
      links={[
        { text: 'Home', to: '/', icon: 'house' },
        {
          text: 'Common',
          to: '/common',
          icon: 'mouse',
          showChildrenByDefault: true,
          children: [
            { text: 'Buttons', to: '/common/buttons' },
            { text: 'Button Dropdowns', to: '/common/button-dropdowns' },
            { text: 'Modals', to: '/common/modals' },
          ],
        },
        {
          text: 'Data',
          to: '/data',
          icon: 'table_rows',
          children: [
            { text: 'Tables', to: '/data/tables' },
            { text: 'Grids', to: '/data/grids' },
            { text: 'Table Grids', to: '/data/table-grids' }
          ]
        },
        {
          text: 'Hidden',
          show: false,
          to: '/nowhere'
        },
        {
          text: 'Inputs',
          to: '/inputs',
          icon: 'keyboard',
          children: [
            { text: 'Text Input', to: '/inputs/text' },
            { text: 'Text Input Area', to: '/inputs/area' },
            { text: 'Select', to: '/inputs/select' },
            { text: 'Select File(s)', to: '/inputs/select_files' },
            { text: 'Date Pickers', to: '/inputs/date' },
            { text: 'Date Ranges', to: '/inputs/date_range' },
            { text: 'Checkboxes', to: '/inputs/checkbox' },
            { text: 'Toggles', to: '/inputs/toggle' },
            { text: 'Segmented Control', to: '/inputs/segmented-control' },
            { text: 'Radio', to: '/inputs/radio' },
            { text: 'Address Input', to: '/inputs/address-input' },
            { text: 'Credit Card Input', to: '/inputs/credit-card-input' }
          ]
        },
        {
          text: 'Uploads',
          to: '/uploads',
          icon: 'cloud_upload',
          children: [
            { text: 'File Uploads', to: '/uploads/files' },
            { text: 'Image Uploads', to: '/uploads/images' },
            { text: 'Video Uploads', to: '/uploads/videos' },
            { text: 'Uploads Tab', to: '/uploads/tab' },
          ]
        },
        {
          text: 'Extras',
          to: '/extras',
          icon: 'pending',
          children: [
            { text: 'Tags', to: '/extras/tags' },
            { text: 'Status', to: '/extras/status' },
            { text: 'Snackbar', to: '/extras/snackbar' },
            { text: 'Chips', to: '/extras/chips' },
            { text: 'Geoblocker', to: '/extras/geoblocker' },
            { text: 'Progress Bar', to: '/extras/progress-bar' },
            { text: 'Donut Chart', to: '/extras/donut-chart' },
            { text: 'Global Search', to: '/extras/global-search' },
          ]
        },
        {
          text: 'Images',
          to: '/images',
          icon: 'photo',
          children: [
            { text: 'Image', to: '/images/image' },
            { text: 'Poster', to: '/images/poster' },
            { text: 'Thumbnail', to: '/images/thumbnail' },
          ]
        },
        {
          text: 'Reusables',
          to: '/reusables',
          icon: 'interests',
          children: [
            { text: 'Devices', to: '/reusables/devices' },
            { text: 'Networking', to: '/reusables/networking' },
            { text: 'Storage', to: '/reusables/storage' },
          ]
        }
      ].map(opt => ({
        ...opt,
        onClick: () => navigate(opt.to),
        children: opt.children ? opt.children.map(child => ({
          ...child,
          onClick: () => navigate(child.to)
        })) : null
      }))}
    />
  )
}

export default ThemeSidebar
