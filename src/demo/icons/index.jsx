import React, { useState } from 'react'
import Icon from '../../components/icon'
import { Select, Toggle } from '../../components'

const iconStrings = [
  'movie', 'settings', 'info', 'check_circle', 'light_mode', 'search', 'home', 'person', 'database_search'
]
const colourOptions = ['text-black', 'text-white', 'text-gray-600', 'text-success-600', 'text-error-600', 'text-pink-600'].map(opt => ({ label: opt, value: opt }))
const sizeOptions = ['text-xl', 'text-2xl', 'text-3xl', 'text-4xl'].map(opt => ({ label: opt, value: opt }))

const Icons = () => {
  const [outlined, setOutlined] = useState(false)
  const [colour, setColour] = useState(colourOptions[0].value)
  const [size, setSize] = useState(sizeOptions[1].value)
  return (<div className='flex flex-col space-y-4'>
    <h4 className='border-t border-gray-300 pt-4'>Material Symbols</h4>
    <div>
      Note: this uses the newer Material Symbols as defined here: <a href='https://fonts.google.com/icons' target='_blank' rel='noreferrer'>https://fonts.google.com/icons</a>.
    </div>
    <code>
      {"<Icon icon={'icon-name'} outline={false} className={'text-success-600 text-xl'}/>"}
    </code>
    <div className='flex flex-col space-y-4'>
      <Select options={colourOptions} value={colourOptions.find(opt => opt.value === colour)} label='Colour' onChange={opt => setColour(opt.value)} className='max-w-sm'/>
      <Select options={sizeOptions} value={sizeOptions.find(opt => opt.value === size)} label='Size' onChange={opt => setSize(opt.value)} className='max-w-sm'/>
      <Toggle label='Outlined Icon' checked={outlined} onChange={() => setOutlined(!outlined)}/>
    </div>
    <div className='flex items-center space-x-4'>
      {iconStrings.map(string =>
        <Icon
          icon={string}
          outlined={outlined}
          className={`${colour} ${size}`}/>)}
    </div>
  </div>)
}

export default Icons
