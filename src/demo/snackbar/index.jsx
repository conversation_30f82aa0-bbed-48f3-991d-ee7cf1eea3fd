import React, { useState } from 'react'
import { Button, Checkbox, Radio, Snackbar } from '../../components'
import Showcase from '../showcase'

const SnackbarExamples = () => {
  const [state, setState] = useState({
    items: [],
    timeout: 3000,
    description: null,
    showCloseButton: false,
    buttons: null,
    showIcon: false,
    alignment: "right"
  })

  function removeItem(itemID) {
    setState(state => ({ ...state, items: state.items.filter(i => i._id !== itemID )} ))
  }

  function addItem(type) {
    setState(state => ({
      ...state,
      items: [
        ...state.items,
        {
          _id: Math.random().toString(),
          message: `${type[0].toUpperCase() + type.slice(1) } message`,
          description: state.description,
          type: type,
          showIcon: state.showIcon,
          timeout: state.timeout,
          showCloseButton: state.showCloseButton,
          onClose: (itemID) => removeItem(itemID),
          buttons: state.buttons,
          onClick1: () => console.log("button 1 pressed"),
          onClick2: () => console.log("button 2 pressed")
        }
      ]}
    ))
  }

  return (
    <Showcase
      name="Modal"
      examples={<div className='space-y-2'>
        <Snackbar alignment={state.alignment} items={state.items}/>
        <div className='flex items-center space-x-2'>
          <Button onClick={() => addItem('info')} type="info">Add Info Item</Button>
          <Button onClick={() => addItem('success')} type="success">Add Success Item</Button>
          <Button onClick={() => addItem('warning')} type="warning">Add Warning Item</Button>
          <Button onClick={() => addItem('error')} type="error">Add Error Item</Button>
        </div>
        <div className='pt-2 space-y-2'>
          <h4>Timeout</h4>
          <Radio
            label='Default (3000ms)'
            checked={state.timeout === 3000}
            onChange={() => setState({ ...state, timeout: 3000})}/>
          <br/>
          <Radio
            label='Custom (5000ms)'
            checked={state.timeout === 5000}
            onChange={() => setState({ ...state, timeout: 5000})}/>
          <br/>
          <Radio
            label='No Timeout (with closable)'
            checked={state.timeout === null}
            onChange={() => setState({ ...state, timeout: null})}/>
        </div>
        <div className='pt-2 space-y-2'>
          <h4>Options</h4>
          <Checkbox
            label='Description'
            checked={!!state.description}
            onChange={() => setState({ ...state, description: !!state.description ? null : 'This is a description'})}/>
          <br/>
          <Checkbox
            label='Closeable'
            checked={!!state.showCloseButton}
            onChange={() => setState({ ...state, showCloseButton: !state.showCloseButton})}/>
          <br/>
          <Checkbox
            label='Buttons'
            checked={!!state.buttons}
            onChange={() => setState({ ...state, buttons: !!state.buttons ? null : ["Button 1", "Button 2"]})}/>
          <br/>
          <Checkbox
            label='Icon'
            checked={!!state.showIcon}
            onChange={() => setState({ ...state, showIcon: !state.showIcon})}/>
        </div>
        <div className='pt-2 space-y-2'>
          <h4>Alignment</h4>
          <Radio
            label='Default (Right)'
            checked={state.alignment === "right"}
            onChange={() => setState({ ...state, alignment: "right"})}/>
          <br/>
          <Radio
            label='Center'
            checked={state.alignment === "center"}
            onChange={() => setState({ ...state, alignment: "center"})}/>
          <br/>
          <Radio
            label='Left'
            checked={state.alignment === "left"}
            onChange={() => setState({ ...state, alignment: "left"})}/>
        </div>
      </div>}
      code={code}/>
  )
}

const code =
`<Snackbar
  alignment={"right"}                                   // default right, or left, center
  items={[                                              // default [], list of snack items by props
    {
      message: "This is a message",                     // default null
      description: "This is a description",             // default null
      type: "info",                                     // default info, or success, warning, error
      showIcon: false,                                  // default false, or true
      icon: null,                                       // default null, overrides default icons based on type
      timeout: 5000,                                    // default 3000, timeout in ms, or null if no timeout
      showCloseButton: false,                           // default false, or true
      onClose: () => console.log("Close Snackbar"),     // default null, onClose function for snackbar
      buttons: ["Button 1, Button 2"],                  // default null, list of up to 2 buttons specified by label
      onClick1: () => console.log("Click Button 1"),    // default null, onClick function for button 1
      onClick2: () => console.log("Click Button 2"),    // default null, onClick function for button 2
      _id: 1
    }
  ]}
/>`

export default SnackbarExamples
