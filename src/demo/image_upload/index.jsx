import React, { useState } from 'react'
import Showcase from '../showcase'
import { ImageUpload } from '../../components'

const PUBLIC_S3_URL = "https://cinesend-public-assets-dev.s3.amazonaws.com/cinesend-theme-upload-zone"

const Upload = () => {
  const [urls, setUrls] = useState({ three: "https://cinesend-public-assets-dev.s3.amazonaws.com/cinesend-theme-upload-zone/ratatouille_hdstill.jpg" })
  return (
    <Showcase
      name="ImageUpload"
      examples={
        <div className='space-y-2'>
          <div className='flex items-center space-x-2'>
            <ImageUpload
              className='w-1/3 aspect-video'
              upload={{
                onComplete: (file, url) => setUrls(urls => ({ ...urls, one: url })),
                publicURL: PUBLIC_S3_URL
              }}
              image={{
                url: urls.one
              }}/>
            <ImageUpload
              className='w-1/3 aspect-video'
              upload={{
                onComplete: (file, url) => setUrls(urls => ({ ...urls, two: url })),
                message: "Upload here!",
                publicURL: PUBLIC_S3_URL
              }}
              image={{
                url: urls.two
              }}
              button={{ text: "Upload", icon: "star", type: "success" }}/>
            <ImageUpload
              className='w-1/3 aspect-video'
              upload={{
                publicURL: PUBLIC_S3_URL,
                onComplete: (file, url) => setUrls(urls => ({ ...urls, three: url })),
              }}
              image={{
                url: urls.three
              }}
              button={{
                text: "Click me",
                onClick: () => window.alert("You clicked me! This custom button click won't trigger uploading.")
              }}/>
          </div>
          <div className='flex items-center space-x-2'>
            <ImageUpload
              className="w-1/2 aspect-video"
              upload={{
                message: "This upload zone will use background-cover.",
                publicURL: PUBLIC_S3_URL,
                onComplete: (file, url) => setUrls(urls => ({ ...urls, four: url }))
              }}
              image={{
                url: urls.four,
                cover: true
              }}/>
            <ImageUpload
              className="w-1/2 aspect-video"
              upload={{
                message: "This upload zone will use background-contain.",
                publicURL: PUBLIC_S3_URL,
                onComplete: (file, url) => setUrls(urls => ({ ...urls, five: url }))
              }}
              image={{
                url: urls.five,
                contain: true
              }}/>
          </div>
          <ImageUpload
            className="w-full h-40"
            upload={{
              publicURL: PUBLIC_S3_URL,
              onComplete: (file, url) => setUrls(urls => ({ ...urls, six: url }))
            }}
            image={{
              url: urls.six
            }}/>
          <ImageUpload
            className="w-full aspect-video"
            upload={{
              message: "This upload zone below would require functionality in `onFileSelected` to return a signed URL, which will then complete the upload.",
              onFileSelected: (file, callback) => {
                // Fetch on an API endpoint to generate a signed URL, then callback with that response...
                callback({ destinationURL: `${PUBLIC_S3_URL}/${file.name}` })
              },
              onComplete: (file, url) => setUrls(urls => ({ ...urls, seven: url }))
            }}
            image={{
              url: urls.seven
            }}/>
        </div>
      }
      code={code}/>
  )
}
  

const code = `<ImageUpload
  url={null},
  icon={"image"},
  message={"Drag something here!"},
  dropMessage={"Drop image here!"},
  button={
    text: "Upload an image",
  },
  publicURL={null},               // This is used if the media can be dropped directly at the public destination URL
  onFileSelected={(file, callback) => {
    // This is used if signed URLs are required.
    // Fetch on an API endpoint to generate a signed URL, then callback with that response.
    // const response = { destinationURL: "https://s3.some-signed-url.com?signature=12345" }
    // callback(response)
  }} 
  onUploadChange={() => {}},
  includeRemoveButton={true}
  className={""},
/>`

export default Upload