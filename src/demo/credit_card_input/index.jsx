import React, { useState } from 'react'
import Showcase from '../showcase'
import { CreditCardInput } from "../../components"

const CreditCardInputExamples = () => {

  const [creditCardInfo, setCreditCardInfo] = useState({
    cardNumber: "",
    expiry: "",
    cvc: ""
  })

  return (
    <Showcase
      name="CreditCardInput"
      examples={
        <div className='space-y-2'>
            <div className={'pt-2'}>
              <CreditCardInput
                label='Credit Card'
                value={creditCardInfo}
                onChange={(e) => setCreditCardInfo(e)}
                onBlur={(e) => console.log(e)}
              />
            </div>
        </div>
      }
      code={code}/>
  )
}

const code =
`<CreditCardInput
  id={"unique-id"}                      // optional
  label={"My label"}                    // default no label -- can be an object label={ text: "My Label", icon: { icon: InfoCircle, onClick: () => window.alert("You clicked me!") }
  description={"Custom Description"}    // default no description
  message={"Custom Message"}            // default no message -- can be an object message={ text: "My Message", icon: { icon: InfoCircle, onClick: () => window.alert("You clicked me!") }
  value={{                    // default null, object with credit card information
    cardNumber: "",
    expiry: "",
    cvc: ""
  }}   
  onChange={(input) => setText(input)}  // onChange function for the credit card info object
  onBlur={(input) => setText(input)}    // onBlur function for the credit card info object    
  disabled={false}                      // default false
  success={false}                       // default false
  error={false}                         // default false  
  border={true}                         // default true
/>`

export default CreditCardInputExamples
