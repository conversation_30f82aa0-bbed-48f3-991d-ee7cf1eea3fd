import React, { useState } from 'react'
import Showcase from '../showcase'
import { VideoUpload } from '../../components'

const BACKGROUND_IMAGE = "https://cinesend-public-assets-dev.s3.amazonaws.com/cinesend-theme-upload-zone/eternalshd.jpeg"
const DEV_API_URL = "https://local-api.cinesend.com.test/api/uploads"

const Upload = () => {
  const [loading, setLoading] = useState(false)
  return (
    <Showcase
      name="VideoUpload"
      examples={
        <div className='space-y-2'>
          <div className='flex items-center space-x-2'>
            <VideoUpload
              className='w-1/2 aspect-video'
              upload={{
                onComplete: (file) => window.alert("Upload complete! One use-case would be to set a thumbnail image now."),
                asperaEnabled: true,
                apiURL: DEV_API_URL,
                specifications: {
                  video_id: "6233552093cdc676751ceaf5"
                }
              }}/>
            <VideoUpload
              className='w-1/2 aspect-video'
              upload={{
                asperaEnabled: true,
                apiURL: DEV_API_URL,
                specifications: {
                  video_id: "6233552093cdc676751ceaf5"
                }
              }}
              video={{
                backgroundImageURL: BACKGROUND_IMAGE,
                name: "eternals_hd_2021-07-01_international_release_trailer.mp4",
                onClick: () => {
                  setLoading(true)
                  setTimeout(() => {
                    window.alert("You would now launch the player, externally from this package.")
                    setLoading(false)
                  }, 3000)
                },
                loading,
              }}/>
          </div>
          <div className='flex items-center space-x-2'>
            <VideoUpload
              className='w-1/2 aspect-video'
              upload={{
                icon: "star",
                asperaEnabled: true,
                apiURL: DEV_API_URL,
                specifications: {
                  video_id: "6233552093cdc676751ceaf5"
                }
              }}
              button={{
                text: "Upload",
                icon: "arrow_drop_down",
                iconPosition: "right",
                onClick: () => window.alert("You might trigger a custom action here.")
              }}/>
            <VideoUpload
              className='w-1/2 aspect-video'
              upload={{
                icon: "star",
              }}
              button={{
                text: "Copy Request Link",
                icon: "link",
                onClick: () => window.alert("You might trigger a custom action here.")
              }}/>
          </div>
          <div className='flex items-center space-x-2'>
            <VideoUpload
              className='w-1/2 aspect-video'
              upload={{
                icon: "star",
                asperaEnabled: true,
                apiURL: DEV_API_URL,
                specifications: {
                  video_id: "6233552093cdc676751ceaf5"
                }
              }}
              video={{
                backgroundImageURL: null,
                name: "eternals_hd_2021-07-01_international_release_trailer.mp4",
                encoding: true,
                onClick: () => {
                  window.alert("This is not yet encoded.")
                },
              }}
              button={{
                text: "Upload",
                icon: "arrow_drop_down",
                iconPosition: "right",
                onClick: () => window.alert("You might trigger a custom action here.")
              }}/>
            <VideoUpload
              className='w-1/2 aspect-video'
              upload={{
                icon: "star",
              }}
              video={{
                backgroundImageURL: null,
                name: "eternals_hd_2021-07-01_international_release_trailer.mp4",
                error: true,
                errorMessage: "There was an error encoding your file: it was not the right frame rate or something.",
                onClick: () => {
                  window.alert("This is not yet encoded.")
                },
              }}
              button={{
                text: "Copy Request Link",
                icon: "link",
                onClick: () => window.alert("You might trigger a custom action here.")
              }}/>
          </div>
        </div>
      }
      code={code}/>
  )
}

const code = `<VideoUpload
  upload={{
    icon: "movie",
    message: "Custom message",
    dropMessage: "Custom drag and drop message",
    publicURL: "https://cinesend-public-assets-dev.s3.amazonaws.com/cinesend-theme-upload-zone"
  }}
  button={{
    text: "Custom button text",
    icon: "link",
    onClick: () => window.alert("Do something here")
  }}
  onFileSelected={(file, callback) => {
    // This is used if signed URLs are required.
    // Fetch on an API endpoint to generate a signed URL, then callback with that response.
    // const response = { destinationURL: "https://s3.some-signed-url.com?signature=12345" }
    // callback(response)
  }}
  onUploadChange={() => {}},
  includeRemoveButton={true}
  className={"w-1/2"},
/>`

export default Upload