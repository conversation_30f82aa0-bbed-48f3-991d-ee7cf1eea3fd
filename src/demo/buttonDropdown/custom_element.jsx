import React from "react"
import { <PERSON><PERSON> } from "../../components"
import { Check } from "../../components/icons"

const CustomElement = () => 
  <div className='p-8 text-xs text-center flex flex-col items-center justify-center space-y-4'>
    <Check className='w-10 h-10 text-primary-600'/>
    <span>Rendering custom elements in the dropdown.<br/>This dropdown will not close when you click inside it.</span>
    <Button
      size='small'
      className={'close-dropdown-element'}
      onClick={() => alert("After this alert closes, the dropdown should close.")}>
      But this button will close it
    </Button>
  </div>

export default CustomElement