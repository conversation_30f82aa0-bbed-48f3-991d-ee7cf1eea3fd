import React from 'react'
import Showcase from '../showcase'
import { Star, Search, MoreVert, CrossCircle, Delete } from "../../components/icons"
import { ButtonDropdown, Icon } from "../../components"
import CustomElement from "./custom_element"

const oneLevelDropdown = [
  {
    text: "Test with Icon",
    icon: Star,
    onClick: () => alert("Selected Test"),
  },
  {
    text: "Test2 with Break After",
    onClick: () => alert("Selected Test2"),
    icon: Delete,
    className: "text-red-600",
    breakAfter: true
  },
  {
    text: "Test3 no Icon no Alert",
    onClick: () => console.log("Selected Test3")
  },
]

const twoLevelsDropdown = [
  ...oneLevelDropdown,
  {
    text: "Extremely Long Test Content Stretch With Children",
    icon: Search,
    iconPosition: "right",
    children: [
      {
        text: "Test4.1",
        onClick: () => alert("Selected Test4.1"),
        icon: Star,
      },
      {
        text: "Test4.2",
        onClick: () => alert("Selected Test4.2"),
        icon: Star,
      }
    ]
  },
  {
    text: "Test5",
    icon: CrossCircle,
    children: [
      {
        text: "Test5.1",
        onClick: () => alert("Selected Test5.1"),
        icon: Star,
      },
      {
        text: "Test5.2",
        onClick: () => alert("Selected Test5.2"),
        icon: Star,
      },
      {
        component: <div className='p-4 flex flex-col space-y-4 bg-black text-white items-center justify-center text-center'>
          <div>This is a custom row component</div>
          <Icon icon='play_circle_outline'/>
        </div>
      },
      {
        text: {
          title: 'Test5.4',
          description: 'Use this to show more info to users.'
        },
        icon: Search,
        onClick: () => alert("Selected Test5.3"),
      }
    ]
  },
]

const threeLevelsDropdown = [
  ...twoLevelsDropdown,
  {
    text: "Three Levels",
    icon: CrossCircle,
    children: [
      {
        text: {
          title: 'Go Further',
          description: 'Use this to show more info to users.'
        },
        icon: Search,
        children: [
          {
            text: 'This is recursive',
            icon: Star,
            onClick: () => alert("Selected!")
          },
          {
            text: 'This is recursive',
            icon: Star,
            onClick: () => alert("Selected!")
          }
        ]
      }
    ]
  },
]

const ButtonDropdownExample = () => {
  return (
    <Showcase
      name="ButtonDropdown"
      examples={
        <div className='space-y-4'>
          {[
            { button: { text: "Two Level Dropdown", type: "success" }, dropdown: { content: twoLevelsDropdown  } },
            { button: { text: "Custom Element Dropdown" }, dropdown: { content: <CustomElement />, anchor: "left", clickCloses: false, classToClose: 'close-dropdown-element'  } },
            { button: { text: "Menu dropdown with custom width and anchored left", type: "warning", className: "w-[800px]" }, dropdown: { content: oneLevelDropdown, anchor: "left"  } },
            { button: { text: "Dropdown with left icon", icon: Star, iconPosition: 'left' }, dropdown: { content: oneLevelDropdown  } },
            { button: { text: "Dropdown with right icon", type: "error", icon: Star, iconPosition: 'right' }, dropdown: { content: oneLevelDropdown  } },
            { button: { text: "Disabled Dropdown", disabled: true } },
            { button: { text: "Three Levels Dropdown" }, dropdown: { content: threeLevelsDropdown  } },
            { button: { icon: Star }, dropdown: { content: twoLevelsDropdown } },
            { button: { icon: MoreVert, type: "neutral", minimal: true, menu: true }, dropdown: { content: twoLevelsDropdown  } },
            { button: { icon: MoreVert, link: true, menu: true }, dropdown: { content: twoLevelsDropdown  } },
            { button: { text: "Minimal Button with Tooltip", tooltip: { text: "Hello!" }, icon: Star, minimal: true }, dropdown: { content: twoLevelsDropdown  } },
            { 
              button: { component: <span>This is a component, not a button.</span>, tooltip: { text: "Still got a tooltip!" }, },
              dropdown: { anchor: "left", content: twoLevelsDropdown  }
            },
            { kebab: true, dropdown: { content: <div className='p-6'>This is a kebab rendered by using the 'kebab' prop</div>}}
          ].map((input, i) =>
            <div key={i} className='flex justify-start'>
              <ButtonDropdown {...input}/>
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<ButtonDropdown
  dropdown={
    anchor: "left", // Anchor's the element to be aligned with the left or right side of the button
    content: {[
      {
        text: "Title",
        onClick: () => alert("Button pressed"),
        icon: Star,
        },
      {
        text: "Multi-level Dropdown",
        icon: Star,
        children: [
          {
            content: "Layer 2",
            onClick: () => alert("Selected Layer2"),
            icon: Star,
            iconPosition: "right",
          }
        ]
      }
    }]
  }
  type="primary"            // default primary, info, success, warning, error
  size="medium"             // default medium, small, large
  secondary={true}          // default false
  tertiary={true}           // default false
  minimal={true}            // default false
  link={true}               // default false
  icon={Star}               // optional
  iconPosition={"right"}    // default both, left, right
  disabled={true}           // default false
  className={""}            // additional classes that get passed to the button element
  kebab={false}             // default false - overrides button props to render kebab
/>`

export default ButtonDropdownExample
