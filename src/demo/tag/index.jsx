import React from 'react'
import Showcase from '../showcase'
import { Tag } from '../../components'
import { Star, CrossCircle } from '../../components/icons'

const TagExamples = () =>
  <Showcase 
    name="Tag"
    examples={
      <div className='space-y-2'>
        {[
          { label: 'BASIC TAG' },
          { label: 'SMALL TAG', size: 'small' },
          { label: 'LARGE TAG', size: 'large' },
          { label: 'LABEL OUTLINE', outline: true },
          { label: 'SMALL OUTLINE', outline: true, size: 'small', type: 'success' },
          { label: 'LARGE OUTLINE', outline: true, size: 'large', type: 'error', icon: CrossCircle },
          { label: 'Success Tag', type: 'success' },
          { label: 'Neutral Tag', type: 'neutral' },
          { label: 'Info Tag', type: 'info' },
          { label: 'Warning Tag', type: 'warning' },
          { label: 'Error Tag', type: 'error' },
          { label: 'Icon Left Tag', icon: Star },
          { label: 'Icon Left Small Tag', icon: Star, size: "small" },
          { label: 'Icon Left Large Tag', icon: Star, size: "large" },
          { label: 'Icon Right Tag', icon: Star, iconPosition: 'right' },
          { icon: Star },
          { icon: Star, size: "small", type: "warning" },
          { icon: Star, size: "large", type: "info" },
          { icon: Star, size: "large", outline: true, type: "success" },
          { label: 'Super long long long long tag that should wrap and look good with borders' },
          { label: 'Hover with copy tooltip', tooltip: { text: "Click me to copy!", copy: { value: "12345", onCopyMessage: "You copied 12345!" }} }
        ].map((tag, i) => 
          <div key={i} className='w-44'>
            <Tag
              index={i}
              {...tag}
              label={tag.label}/>
          </div>)}
      </div>
    }
    code={code}/>

const code =
`<Tag
  label={"Some Tag Label"}
  type={"primary"}          // default primary, or neutral, info, success, warning, error
  outline={false}           // default false, or true
  size={"small"}            // default medium, small or large
  icon=Star                 // default null, imported icons from '../../components/icons'
  iconPosition="left"       // default left, right
/>`

export default TagExamples