import React, { useState } from 'react'
import Showcase from '../showcase'
import { SegmentedControl } from "../../components"

const twoButtons = [
  { label: "One", value: "one"},
  { label: "Two", value: "two" }
]

const threeButtons = [
  ...twoButtons,
  { label: "Three", value: "three" }
]

const fourButtons = [
  ...threeButtons,
  { label: "Four", value: "four" }
]

const SegmentedControlExample = () => {
  const [valueOne, setValueOne] = useState("one")
  const [valueTwo, setValueTwo] = useState("one")
  const [valueThree, setValueThree] = useState("one")
  const [valueFour, setValueFour] = useState("one")
  const [valueFive, setValueFive] = useState("one")
  const [valueSix, setValueSix] = useState("one")
  return (
    <Showcase
      name="SegmentedControl"
      examples={
        <div className='space y-2'>
          {[
            { options: twoButtons, value: valueOne, onChange: setValueOne },
            { label: 'Three Buttons', options: threeButtons, value: valueTwo, onChange: setValueTwo },
            { label: 'Four Buttons', options: fourButtons, value: valueThree, onChange: setValueThree },
            { label: 'Four Warning Buttons', options: fourButtons, value: valueFour, onChange: setValueFour, type: 'warning' },
            { label: 'Two Secondary Buttons', options: twoButtons, value: valueFive, onChange: setValueFive, secondary: true },
            { label: 'Two Disabled Buttons', options: twoButtons, value: valueSix, onChange: setValueSix, disabled: true },
          ].map((input, i) =>
            <div key={i} className={'py-4 max-w-md'}>
              <SegmentedControl {...input}/>
            </div>)}
        </div>
      }
      code={code}/>
  )
}

const code =
`<SegmentedControl
  options={[                // the options array that defines the buttons
    { label: "One", value: "one"},
    { label: "Two", value: "two" },
    { label: "Three", value: "three" },
    { label: "Four", value: "four" }
  ]}
  value={"one"}             // the value of the button that is currently selected
  onChange={setValue()}     // function that sets the currentValue
  type={"primary"}          // default primary, the type of button that appears
  className={""}            // additional classes that get passed to the button element
/>`

export default SegmentedControlExample
