import React, { useState, useEffect } from 'react'
import SegmentedControl from '../segmented_control'
import Select from '../select'
import Regional from './components/regional'
import { InfoCircle } from '../icons'

const url = 'https://api.cinesend.com/api/utilities/countries'
const countriesWithRegionalGeoblocking = [
  { code: "CA", },
  { code: "US", include_area_codes: true }
]

const Geoblocker = ({
  value = {},
  className = "",
  onChange = () => {},
}) => {
  const [countries, setCountries] = useState([])
  useEffect(() => {
    fetch(url).then(res => res.json()).then(res => setCountries(res))
  }, [setCountries])

  const useBlocklist = value.use_blocklist_geoblocking === true ? true : false
  const countryCodes = value.geoblock_country_codes || []

  const update = data => {
    onChange({ ...value, ...data })
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <SegmentedControl
        secondary
        options={[{ label: "Use allow list", value: false }, { label: "Use block list", value: true }]}
        value={useBlocklist}
        onChange={value => update({ use_blocklist_geoblocking: value })}/>
      <div className='text-sm'>Countries <strong>{useBlocklist ? 'blocked from viewing' : 'allowed to view'}</strong></div>
      <Select
        key={'country_select_key'}
        isMulti
        isLoading={countries.length === 0}
        options={countries}
        value={countries.filter(opt => countryCodes.includes(opt.value))}
        message="If none are selected, all are allowed."
        bottomIcon={InfoCircle}
        bottomIconPosition="left"
        onChange={values => update({ geoblock_country_codes: values ? values.map(v => v.value) : []})}
      />
      {countries.length > 0 && <>{countriesWithRegionalGeoblocking
        .filter(country => countryCodes.includes(country.code))
        .map(country =>
          <Regional
            value={value}
            region={country.code}
            countries={countries}
            onChange={regions => update(regions)}
            includeAreaCodes={country.include_area_codes}/>)}
      </>}
    </div>
  )
}

export default Geoblocker