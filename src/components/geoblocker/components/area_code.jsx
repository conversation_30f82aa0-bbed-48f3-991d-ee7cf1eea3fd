import React, { useState } from 'react'
import Button from '../../button'
import Input from '../../input'
import Select from '../../select'

const regionTypes = {
  US: 'zip codes',
}

const AreaCodeGeoblocking = ({
  value,
  region,
  onChange = () => {}
}) => {
  const geoblockAreaCodes = value.geoblock_area_codes || {}
  const regionalAreaCodes = geoblockAreaCodes[region] || []
  const [importCodes, setImportCodes] = useState('')
  const [importing, setImporting] = useState(false)
  return (
    <div className="flex items-end space-x-2">
      {!importing ?
        <Select
          label={<span>Enter which {regionTypes[region]} are <strong>allowed</strong> to view videos. If none are added, all {regionTypes[region]} are allowed.</span>}
          value={regionalAreaCodes.map(code => ({ value: code, label: code }))}
          isCreatable
          formatCreateLabel={value => `Add ${value}`}
          isClearable
          isMulti
          placeholder="Add an entry and hit [TAB]..."
          onChange={values => {
            const codes = values.map(opt => opt.value)
            onChange({
              geoblock_area_codes: { ...value.geoblock_area_codes, [region]: codes }
            })
          }}/> :
        <Input
          label="Enter a comma-separated list of codes and press Import"
          placeholder="90210,12345,ETC"
          value={importCodes}
          onChange={e => setImportCodes(e.target.value)}/>}
      {importing && <Button
        disabled={!importCodes}
        tertiary
        onClick={() => {
          onChange({ geoblock_area_codes: { ...value.geoblock_area_codes, [region]: importCodes.split(',') }})
          setImportCodes('')
          setImporting(false)
        }}>
        Import
      </Button>}
      <Button
        tertiary
        type={importing ? "neutral" : "primary"}
        onClick={() => setImporting(!importing)}>
        {importing ? "Cancel" : "Import List"}
      </Button>
    </div>
  )
}

export default AreaCodeGeoblocking
