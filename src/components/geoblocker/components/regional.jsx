import React, { useState } from 'react'
import Toggle from '../../toggle'
import Select from '../../select'
import AreaCode from './area_code'
import { InfoCircle } from '../../icons'

const regionTypes = {
  CA: 'provinces',
  US: 'states',
}

const RegionalGeoblocking = ({
  value,
  onChange = () => {},
  countries = [],
  region,
  includeAreaCodes = false,
}) => {
  const geoblockAreaCodes = value.geoblock_area_codes || {}
  const [useAreaCodes, setUseAreaCodes] = useState(!!geoblockAreaCodes[region])
  const country = countries.filter(c => c.value === region)[0]
  const regions = country.regions.map(r => ({ label: r.region, value: r['region-code'] }))
  const geoblockRegionCodes = value.geoblock_region_codes || {}
  const specificRegionCodes = geoblockRegionCodes[region] || []
  const selectedRegions = regions.filter(region => specificRegionCodes.includes(region.value))
  return (
    <div className="border p-4 space-y-4">
      <div className="flex items-center justify-between">
        <p className={`text-xs uppercase`}>{country.country} Policies</p>
        {includeAreaCodes &&
          <Toggle
            checked={useAreaCodes}
            size={"small"}
            label="Use Zip Codes Override"
            onChange={() => {
              const key = useAreaCodes ? 'geoblock_area_codes' : 'geoblock_region_codes'
              const value = useAreaCodes ? { ...geoblockAreaCodes, [region]: null } : { ...geoblockRegionCodes, [region]: null }
              onChange({ [key]: value })
              setUseAreaCodes(!useAreaCodes)
            }}
          />}
      </div>
      {useAreaCodes ?
        <AreaCode value={value} region={region} onChange={onChange}/> :
        <Select
          key={`${region}_region_select_key`}
          label={`Select which ${regionTypes[region]} are ${value.use_blocklist_geoblocking ? 'blocked' : 'allowed'}.`}
          message={`If none are selected, all ${regionTypes[region]} are ${value.use_blocklist_geoblocking ? 'blocked' : 'allowed'}`}
          bottomIcon={InfoCircle}
          bottomIconPosition="left"
          isMulti
          options={regions}
          value={selectedRegions}
          onChange={values => onChange({ geoblock_region_codes: {
            ...geoblockRegionCodes,
            [region]: values ? values.map(v => v.value) : [],
          }})}
        />
      }
    </div>
  )
}

export default RegionalGeoblocking
