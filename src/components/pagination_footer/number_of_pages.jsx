import React, { useEffect, useState } from 'react'
import Select from '../select'
import useWindowResize from '../hooks/use_window_resize'

const NumberOfPages = ({ itemsPerPage = 10, onRowsPerPageChange, isGrid = false, breakpoints, totalRows }) => {

  const size = useWindowResize()
  const defaults = [5, 10, 15, 25, 50, 100]
  if(totalRows) defaults.push('Show all')
  const [paginationOptions, setPaginationOptions] = useState(defaults)
  const width = size.width

  useEffect(() => {
    if (isGrid) {
      const options = getOptionsByScreenWidth(width, breakpoints)
      // only reset default if the number of pages has changed...
      if (options[0] !== paginationOptions[0]) {
        const count = getRowIndex(paginationOptions, itemsPerPage)
        onRowsPerPageChange(options[count])
        setPaginationOptions(options)
      }
    }
    else {
      setPaginationOptions(defaults)
    }
  }, [width])

  const selectOptions = paginationOptions.map(opt => ({ label: opt, value: opt }))
  
  return (
    <div className='flex items-center md:justify-start justify-center text-sm'>
      <div>Show per page:</div>
      <Select
        className='ml-2'
        width='w-24'
        value={selectOptions.find(opt => {
          const value = opt.value === 'Show all' ? totalRows : opt.value
          return value === itemsPerPage
        })}
        options={selectOptions}
        onChange={opt => {
          opt.value === 'Show all' ?  onRowsPerPageChange(totalRows) : onRowsPerPageChange(opt.value)
        }}/>
    </div>
  )
}

function getRowIndex(options, itemsPerPage) {
  for (let i = 0; i < options.length; i++) {
    if (options[i] === itemsPerPage) {
      return i
    }
  }
  return 1
}

function getOptionsByScreenWidth(screenWidth, breakpoints) {
  let result = [5, 10, 15, 20, 25]
  for (const [width, rowSize] of Object.entries(breakpoints)) {
    if (screenWidth >= width) {
      result = [rowSize, rowSize * 2, rowSize * 3, rowSize *4, rowSize * 5]
    }
  }
  return result
}

export default NumberOfPages
