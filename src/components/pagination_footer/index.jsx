import React from 'react'
import NumberOfPages from './number_of_pages'
import PageOptions from './page_options'

const PaginationFooter = ({
  rowsPerPage = 10,
  currentPage,
  totalRows = 0,
  onPageChange,
  onRowsPerPageChange,
  isGrid,
  breakpoints
}) => {
  return (
    <div className='grid grid-cols-1 md:grid-cols-2 items-center space-y-2 my-4'>
      <NumberOfPages
        itemsPerPage={rowsPerPage}
        onRowsPerPageChange={onRowsPerPageChange}
        totalRows={totalRows}
        breakpoints={breakpoints}
        isGrid={isGrid}/>
      <PageOptions
        rowsPerPage={rowsPerPage}
        currentPage={currentPage}
        totalRows={totalRows}
        onPageChange={onPageChange}/>
    </div>
  )
}

export default PaginationFooter
