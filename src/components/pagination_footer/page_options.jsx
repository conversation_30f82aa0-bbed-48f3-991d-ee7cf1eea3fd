import React, { useEffect, useState } from 'react'
import Input from '../input'
import { ChevronLeft, ChevronRight } from '../icons'

const commonSquareClass = 'ml-2 h-10 w-10 rounded-lg flex items-center justify-center'

const PageOptions = ({ rowsPerPage = 10, currentPage, totalRows = 0, onPageChange }) => {

  const [totalPages, setTotalPages] = useState()
  const [iterator, setIterator] = useState([])
  const [inputPage, setInputPage] = useState(currentPage + 1)
  useEffect(() => {
    setInputPage(currentPage + 1)
  }, [currentPage, setInputPage])
  useEffect(() => {

    setTotalPages(Math.ceil(totalRows / rowsPerPage))

    let iterator = [...Array(totalPages).keys()]

    // Only set the iterator if there are less than 5 pages
    setIterator(iterator.length <= 5 ? iterator : null)

  }, [totalPages, totalRows, rowsPerPage, setTotalPages])

  const canPageLeft = currentPage > 0
  const canPageRight = currentPage < totalPages - 1

  return (
    <div className='flex items-center md:justify-end justify-center space-x-2'>
      <div
        onClick={() => canPageLeft && onPageChange(currentPage - 1)}
        className={` border 
          ${commonSquareClass}
          ${canPageLeft
            ? 'border-primary-500 text-primary-500 cursor-pointer'
            : 'border-gray-200 text-gray-400'}`}>
        <ChevronLeft className='h-4 w-4'/>
      </div>
      {iterator ? iterator.map((page, key) =>
        <div
          key={key}
          onClick={() => onPageChange(page)}
          className={`${commonSquareClass}
          ${page > 9999 ? 'text-xs' : page > 999 ? 'text-sm' : ''}
          ${currentPage === page
            ? 'bg-primary-500 text-white'
            : 'cursor-pointer border border-gray-200 text-gray-600'}`}>
          {page + 1}
        </div>
      ) :
        <div className='flex items-center'>
          <Input
            className='flex items-center justify-center'
            width={`${inputPage > 999 ? 'w-16' : inputPage > 99 ? 'w-14' : inputPage > 9 ? 'w-12' : 'w-10'}`}
            value={inputPage}
            onChange={e => setInputPage(e.target.value)}
            onBlur={e => {
              const newPage = parseInt(e.target.value)
              if (Number.isInteger(newPage) && newPage > 0) {
                onPageChange(parseInt(e.target.value) - 1)
              }
            }}/>
          <span className='text-gray-900 ml-2 text-sm'>of {totalPages}</span>
        </div>
      }
      <div
        onClick={e => canPageRight && onPageChange(currentPage + 1)}
        className={`border 
          ${commonSquareClass}
          ${canPageRight
            ? 'border-primary-500 text-primary-500 cursor-pointer'
            : 'border-gray-200 text-gray-400'}`}>
        <ChevronRight className={`h-4 w-4`}/>
      </div>
    </div>
  )
}

export default PageOptions
