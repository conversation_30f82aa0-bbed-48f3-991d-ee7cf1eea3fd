import React, { useState } from "react";
import Table from "../table";
import Grid from "../grid";

export default function TableGrid(props) {
  const [view, setView] = useState("table");
  const newProps = {
    ...props,
    header: {
      ...props.header,
      view: {
        type: view,
        onViewChange: (newView) => setView(newView),
      },
    },
  };
  return (
    <div>
      {view === "table" ? (
        <Table {...newProps} body={props.tableBody} />
      ) : (
        <Grid {...newProps} body={props.gridBody} />
      )}
    </div>
  );
}
