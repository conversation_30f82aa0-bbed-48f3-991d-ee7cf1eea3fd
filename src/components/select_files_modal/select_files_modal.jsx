import React, { useEffect, useState } from "react"
import Status from "../status"
import Table from "../table"
import Icon from "../icon"
import Modal from "../modal"
import Breadcrumb from "../breadcrumb"
import iconMapper from "../helpers/icon_source_file_mapper"

export default function SelectFilesModal({
  header,
  onOpenFolderClick,
  onAllFilesClick,
  onProjectClick,
  projectList = [],
  fileList = [],
  breadcrumbList = [],
  status = "READY",
  onClose = null,
  onImportFilesClick,
  multiple = false,
  extensions = null,
}) {
  const [selectedProjectId, setSelectedProjectId] = useState(null)
  const [selectedFiles, setSelectedFiles] = useState([])
  const [files, setFiles] = useState([])

  const toggleSelectedFile = (selected) => {
    if (!multiple && selectedFiles.length > 0 && !selectedFiles.find((file) => file._id === selected._id)) {
      return true
    }

    if (selectedFiles.find((file) => file._id === selected._id)) {
      setSelectedFiles(selectedFiles.filter((file) => file._id !== selected._id))
    } else {
      setSelectedFiles([...selectedFiles, ...[selected]])
    }
  }

  useEffect(() => {
    if (fileList) {
      setSelectedFiles([])

      let tmpFiles = fileList
      if (extensions?.length > 0) {
        tmpFiles = fileList.filter((file) => extensions.includes(file.extension) || file.type === "folder")
      }

      setFiles(tmpFiles)
    }
  }, [fileList])

  return (
    <Modal
      header={header}
      onClose={() => onClose()}
      confirmButton={{
        text: "Import",
        disabled: !selectedFiles.length,
        onClick: () => onImportFilesClick(selectedFiles),
      }}
      className="w-4/5"
    >
      <Status pending={status === "PENDING"} error={status === "ERROR"}>
        {/* If projects have been supplied then show those otherwise disregard projects completely */}
        {!selectedProjectId && projectList.length ? (
          <Table
            header={{
              columns: [
                {
                  text: "Projects",
                  key: "projects",
                },
              ],
            }}
            widths={["auto"]}
            body={{
              data: projectList,
              empty: {
                icon: "description",
                title: "No projects",
              },
              row: {
                onClick: (event, project) => {
                  setSelectedProjectId(project._id)
                  onProjectClick(project._id)
                },
                render: [
                  (project) => (
                    <div className="flex items-center space-x-3 select-none">
                      <div className="w-[32px] h-[44px] bg-gray-300 rounded-[3px] flex items-center justify-center">
                        <img
                          src={project.poster}
                          onError={(e) => {
                            e.target.style.display = "none"
                          }}
                          className="w-[32px] h-[44px] rounded-[3px] object-cover mx-1"
                        />
                        <span className="text-gray-600"></span>
                      </div>
                      <div>{project.title}</div>
                    </div>
                  ),
                ],
              },
            }}
          />
        ) : (
          <div className="space-y-6">
            <div className="flex items-center">
              {projectList.length && (
                <div className="flex items-center opacity-50">
                  <div
                    className="cursor-pointer hover:bg-gray-300 rounded-full px-4 py-0.5"
                    onClick={() => setSelectedProjectId(false)}
                  >
                    All Projects
                  </div>
                  <Icon icon="chevron_right" />
                </div>
              )}
              {breadcrumbList.length > 0 ?
                <Breadcrumb
                  className="ml-4"
                  breadcrumb={breadcrumbList}
                  onAllClick={() => onAllFilesClick()}
                  onCrumbClick={(crumb) => onOpenFolderClick(crumb._id)}
                />
              : <div className="px-4">{(projectList.find(({ _id }) => _id === selectedProjectId) || {}).title || "N/A"}</div>
            }
            </div>

            <Table
              widths={["auto"]}
              body={{
                data: files,
                empty: {
                  icon: "description",
                  title: "No content",
                },
                row: {
                  checkbox: {
                    checked: (file) => selectedFiles.find((item) => item._id === file._id),
                    onChange: (file) => toggleSelectedFile(file),
                    disabled: (file) =>
                      file.type === "folder" ||
                      (!multiple && selectedFiles.length > 0 && !selectedFiles.find((item) => item._id === file._id)),
                  },
                  onClick: (event, file) => {
                    if (file.type === "folder") {
                      onOpenFolderClick(file._id)
                    } else {
                      toggleSelectedFile(file)
                    }
                  },
                  render: [
                    (file) => (
                      <div className="flex items-center space-x-3 select-none">
                        <Icon
                          icon={file.type === "folder" ? "folder" : iconMapper(file.extension)}
                          className={"text-gray-700"}
                        />
                        <div>{file.name}</div>
                      </div>
                    ),
                  ],
                },
              }}
            />
          </div>
        )}
      </Status>
    </Modal>
  )
}
