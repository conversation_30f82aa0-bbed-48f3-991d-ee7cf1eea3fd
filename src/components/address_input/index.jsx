import React, { useRef, useEffect } from 'react'
import InputWrapper from '../input'

const AddressInput = ({
  value,
  onChange = () => {},
  onBlur = () => {},
  useQuery = true,
}) => {

  const autoCompleteRef = useRef()
  const inputRef = useRef()

  const onChangeRef = useRef()
  onChangeRef.current = onChange

  const onBlurRef = useRef()
  onBlurRef.current = onBlur

  useEffect(() => {
    if (useQuery) {
      autoCompleteRef.current = new window.google.maps.places.Autocomplete(inputRef.current)
      autoCompleteRef.current.addListener("place_changed", async function () {
        const address = await autoCompleteRef.current.getPlace()
        const addressComponents = address.address_components
        const numberComponent = addressComponents.find(line => { return line.types.includes('street_number') }) ?? ''
        const routeComponent = addressComponents.find(line => { return line.types.includes('route') }) ?? ''
        const subpremiseComponent = addressComponents.find(line => { return line.types.includes('subpremise') }) ?? ''
        const countryComponent = addressComponents.find(line => { return line.types.includes('country') }) ?? ''
        const cityComponent = addressComponents.find(line => { return line.types.includes('locality') }) ?? ''
        const provinceComponent = addressComponents.find(line => { return line.types.includes('administrative_area_level_1') }) ?? ''
        const postalComponent = addressComponents.find(line => { return line.types.includes('postal_code') }) ?? ''
        onChangeRef.current({
          address1: `${numberComponent.long_name} ${routeComponent.long_name}`,
          address2: subpremiseComponent.long_name,
          country: countryComponent.long_name,
          city: cityComponent.long_name,
          province: provinceComponent.short_name,
          postal: postalComponent.long_name,
        })
        onBlurRef.current({
          address1: `${numberComponent.long_name} ${routeComponent.long_name}`,
          address2: subpremiseComponent.long_name,
          country: countryComponent.long_name,
          city: cityComponent.long_name,
          province: provinceComponent.short_name,
          postal: postalComponent.long_name,
        })
      })
    }
  }, [])

  return (
    <div className='space-y-2'>
      <InputWrapper
        innerRef={inputRef}
        description='Address Line 1'
        value={value.address1}
        onChange={(e) => onChange({ ...value, address1: e.target.value })}
        onBlur={(e) => onBlur({ ...value, address1: e.target.value })}
      />
      <InputWrapper
        description='Address Line 2'
        value={value.address2}
        onChange={(e) => onChange({ ...value, address2: e.target.value })}
        onBlur={(e) => onBlur({ ...value, address2: e.target.value })}
      />
      <InputWrapper
        description='Country'
        value={value.country}
        onChange={(e) => onChange({ ...value, country: e.target.value })}
        onBlur={(e) => onBlur({ ...value, country: e.target.value })}
      />
      <div className='flex space-x-2'>
        <InputWrapper
          description='City'
          value={value.city}
          onChange={(e) => onChange({ ...value, city: e.target.value })}
          onBlur={(e) => onBlur({ ...value, city: e.target.value })}
        />
        <InputWrapper
          description='Province/State'
          value={value.province}
          onChange={(e) => onChange({ ...value, province: e.target.value })}
          onBlur={(e) => onBlur({ ...value, province: e.target.value })}
        />
      </div>
      <InputWrapper
        description='Postal/Zip'
        value={value.postal}
        onChange={(e) => onChange({ ...value, postal: e.target.value })}
        onBlur={(e) => onBlur({ ...value, postal: e.target.value })}
      />
    </div>
  )
}

export default AddressInput