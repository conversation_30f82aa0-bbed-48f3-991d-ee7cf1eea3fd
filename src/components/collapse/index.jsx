import React, { useState } from "react"
import Icon from "../icon"

export default function Collapse({
  title = "",
  children,
  className = "",
  startCollapsed = false
}) {
  const [collapsed, setCollapsed] = useState(startCollapsed)
  return (
    <div className={className}>
      <div
        className='flex justify-between text-gray-800 px-4 py-2 bg-gradient-to-b from-white to-primary-100 border border-gray-200 cursor-pointer items-center'
        onClick={() => setCollapsed(!collapsed)}>
        <div className='uppercase text-xs font-semibold'>
          {title}
        </div>
        <Icon icon={collapsed ? 'expand_less' : 'expand_more'}/>
      </div>
      {!collapsed ? children : null}
    </div>
  )
}