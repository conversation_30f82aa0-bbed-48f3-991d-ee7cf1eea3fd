import React from "react"
import Avatar from "../avatar"
import ButtonDropdown from "../button_dropdown"
import { ChevronDown } from "../icons"
import useIsMobile from "../hooks/use_is_mobile"

const HeaderBar = ({
  className = "",
  name = "",
  photoURL = null,
  organization = "",
  dropdownContent = null,
  clickCloses = true,
  classToClose = null,
  children
}) => {
  const isMobile = useIsMobile()
  return (
    <div className={`${className}
      z-10 flex items-center w-full px-6 h-16 border-b-2
      bg-white border-gray-200 dark:bg-primary-900 dark:border-gray-600`}>
      <div className='grow'>
        {children}
      </div>
      <div className='justify-end pl-2'>
        <ButtonDropdown
          button={{
            component: (
              <div className='flex items-center space-x-4'>
                <Avatar url={photoURL} name={name}/>
                {!isMobile ?  <div className='flex-col flex'>
                  <span className='font-subheader text-sm text-gray-900 dark:text-gray-200 leading-4'>
                    {name}
                  </span>
                  <span className='text-xs text-gray-600 dark:text-gray-400 leading-4'>
                    {organization}
                  </span>
                </div> : null}
                <ChevronDown className='h-4 w-4 text-gray-400 dark:text-gray-600'/>
              </div>
            )
          }}
          dropdown={{
            anchor: "right",
            content: dropdownContent,
            classToClose,
            clickCloses,
          }}/>
      </div>
    </div>
  )
}

export default HeaderBar
