import React from 'react';

interface Props {
    className?: string
    name: string
    photoUrl?: string
    orginization: string
    dropdownContent?: [{
        anchor: string
        content: {
            text: string
            icon: string
            onClick: () => void
        }
        clickCloses: boolean
    }]
    leftMargin?: string
    children?: unknown
}

declare const HeaderBar: React.FunctionComponent<Props>

export default HeaderBar