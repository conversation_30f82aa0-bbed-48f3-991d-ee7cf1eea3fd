export const get = (apiURL, callback = null, includeCredentials = true) => {
  apiCall(apiURL, null, callback, 'GET', includeCredentials)
}

export const post = (apiURL, data, callback = null) => {
  apiCall(apiURL, data, callback, 'POST')
}

export const put = (apiURL, data, callback = null) => {
  apiCall(apiURL, data, callback, 'PUT')
}

const apiCall = (apiURL, data, callback, method, includeCredentials = true) => {
  if (!apiURL) {
    return
  }
  let defaults = {
    method,
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json"
    }
  }
  if (includeCredentials) {
    defaults.credentials = "include"
  }
  let payload = defaults
  if (method !== 'GET') {
    payload.body = JSON.stringify(data)
  }
  fetch(apiURL, payload).then(res => {
    const ok = res.ok
    res.json().then(json => {
      if (ok && callback && typeof callback === "function") {
        callback(json)
      }
    })
  })
  .catch(error => {
    console.error(error)
  })

}