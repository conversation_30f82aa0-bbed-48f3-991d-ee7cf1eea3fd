import {fileTypeFromBuffer} from 'file-type'; 

const readFileAsArrayBuffer = (file) => new Promise((resolve, reject) => {
  const reader = new FileReader();
  reader.onload = () => resolve(reader.result);
  reader.onerror = reject;
  reader.readAsArrayBuffer(file);
});

//We want to be as lenient as possible because we would rather let some bad files
//in, which can do little damage, than to have valid files be kept out. It will be
//frutstrating and unacceptable for certain 'invalid' files such as QuickTime videos to 
//not be uploaded for example.
export const isValidFileType = (fileToUpload) => {

  //We want to bypass any folders or array of files because they need to be handled differently
  //in a later ticket which will address multiple promises and tackle large buffer sizes of multiple files
  if(Array.isArray(fileToUpload)) {
    return Promise.resolve(true)
  }
  
  return readFileAsArrayBuffer(fileToUpload)
    .then(buffer => fileTypeFromBuffer(new Uint8Array(buffer)))
    .then(fileType => {

      //Return true if the package cannot determine file type because we do not want valid files
      //such as QuickTime videos that the package cant decipher to not be uploaded. 
      if(!fileType) {
        return true
      }

      try {
        // Make sure we have a proper extension from the file 
        // name of the file being uploaded otherwise return false
        const filename = fileToUpload?.name
        if (!filename || typeof filename !== 'string' || filename.trim() === '') {
          return false;
        }

        const lastDotIndex = filename.lastIndexOf('.');

        // The purpose being is that these types of files are most likely to be malicious in nature.
        // The package has determined there is a proper filetype / extension but it is not reflected in 
        // the file the user has uploaded.
        if (lastDotIndex === -1 || lastDotIndex === 0) {
          return false;
        }

        const uploadedFileExtension = filename.slice(lastDotIndex + 1);

        // Check if the detected extension matches fileDetails.extension. If so return true otherwise return false
        return fileType?.ext === uploadedFileExtension || fileType?.mime === fileToUpload?.type
      } catch (err) {
        //Return true if the package cannot determine file type because we do not want 
        //the chance of a valid file a user is trying to upload to never make it into s3
        console.error('Filetype uknown due to:', err);
        return true;
      } 
 
    })
    .catch(err => {
      //Return true if the package cannot determine file type because we do not want 
      //the chance of a valid file a user is trying to upload to never make it into s3
      console.error('Filetype uknown due to:', err);
      return true;
    });
}