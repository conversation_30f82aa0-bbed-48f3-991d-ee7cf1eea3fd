const iconMapper = (extension) => {
  const videoExtensions = ["mp4", "avi", "mkv", "mov", ".mpg", ".m4v"]
  const audioExtensions = ["mp3", "wav", "aac", "flac", "aiff"]
  const captionExtensions = ["srt", "vtt", "sub", "scc", "idx", "ttml"]

  if (videoExtensions.includes(extension)) {
    return "play_circle"
  }
  else if (audioExtensions.includes(extension)) {
    return "album"
  }
  else if (captionExtensions.includes(extension)) {
    return "closed_caption"
  }
  else {
    return "description"
  }
}

export default iconMapper
