import Uppy from '@uppy/core'
import XHRUpload from '@uppy/xhr-upload'
import { post, put } from './fetch'

export const initializeUppyUpload = (fileToUpload, specifications, updates, apiURL) => {

  updates.onStatusChange("Initializing")
  
  updates.onFileSelected(fileToUpload)

  const data = {
    method: 'HTTP',
    file_list: [parseFile(fileToUpload)],
    specifications
  }
  post(apiURL, data, res => {
    startUppyUpload(fileToUpload, res.upload_url, updates, `${apiURL}/${res.upload_details._id}`)
  })
  
}

export const startUppyUpload = (fileToUpload, destinationURL, updates, apiURL) => {

  // Set up Uppy
  const uppy = new Uppy()

  uppyInstance = uppy

  uppy.use(XHRUpload, {
    endpoint: destinationURL,
    method: "PUT",
    formData: false,
    withCredentials: true,
  })

  // @TODO: Maybe useful for multiple files / folders:
  // uppy.on('progress', (progress) => {
  //   // progress: integer (total progress percentage)
  //   console.log(progress)
  // })

  let cycleTimer = new Date().getTime()
  let threshold = 30 * 1000 // every 30 seconds

  uppy.on("upload-progress", (file, progress) => {
    updates.onStatusChange("Running")
    updates.onProgress({ total_bytes: progress.bytesTotal, current_bytes: progress.bytesUploaded })

    // If the current time is passed the threshold time, send an API update and reset the cycle timer.
    var currentTime = new Date().getTime()
    if (currentTime - cycleTimer > threshold) {
      put(apiURL, {
        status: "Running",
        current_size: progress.bytesUploaded,
        total_size: progress.bytesTotal,
        progress: `${Math.floor((progress.bytesUploaded / progress.bytesTotal) * 100)}%`,
      })
      cycleTimer = currentTime
    }
  })

  //Backend jobUpdate functionality has a bug so this event doesnt trigger in production
  // uppy.on("upload-success", (file, response) => {
  //   updates.onStatusChange(null)
  //   if (!apiURL) {
  //     updates.onComplete(fileToUpload, destinationURL)
  //   }

  //   put(apiURL, { status: "Complete" }, (res) => updates.onComplete(fileToUpload, destinationURL, res))
  // })
  uppy.on("error", (error) => {
    updates.onError(error)
  })
  uppy.on("cancel-all", () => {
    updates.onStatusChange(null)
    updates.onProgress(null)
  })
  uppy.on("complete", (result) => {
    if (result?.successful?.length > 0) {
      updates.onStatusChange(null)
      if (!apiURL) {
        updates.onComplete(fileToUpload, destinationURL)
      }
      put(apiURL, { status: "Complete" }, (res) => updates.onComplete(fileToUpload, destinationURL, res))
    }
  })
  uppy.addFile({
    name: fileToUpload.name,
    data: fileToUpload,
  })
  uppy.upload().then((result) => {
    if (result && result.failed.length > 0) {
      console.error("Errors:")
      result.failed.forEach((file) => {
        console.error(file.error)
      })
    }
  })
}

export const cancelUppy = () => {
  if (typeof uppyInstance.close() === "function") {
    uppyInstance.close()
  }
}

const parseFile = file => ({
  isDirectory: isDirectory(file),
  size: calculateFileSize(file),
  name: parseFileName(file),
  path: parseFilePath(file),
})

const parseFilePath = file => (isDirectory(file) ? "" : file.preview)

const parseFileName = file => (isDirectory(file) ? file[0].webkitRelativePath.split("/")[0] : file.name)

const calculateFileSize = file => {
  return parseFloat(isDirectory(file)
    ? file.reduce((totalSize, file) => totalSize + file.size, 0)
    : file.size)
}

const isDirectory = file => file.length > 0

var uppyInstance = null
