import { v4 as uuidv4 } from 'uuid'
import { post, put } from './fetch'

/*
 * This function initializes Aspera
 */
const initializeAsperaClient = () => {
  const { AW4 } = window

  const uuid = uuidv4()
  const sdkLocation = '//d3gcli72yxqn2z.cloudfront.net/connect/v4'

  const asperaWeb = new AW4.Connect({
    sdkLocation,
    minVersion: '3.6.0',
    id: uuid,
  })

  const asperaInstaller = new AW4.ConnectInstaller({ sdkLocation })

  asperaWeb.initSession(`nodeConnect-${uuid}`)

  asperaWeb.addEventListener(AW4.Connect.EVENT.STATUS, (eventType, status) => {
    asperaWebEventListener(eventType, status, asperaInstaller)
  })

  return [asperaWeb, uuid]
}

/*
 * After initializing Aspera, prompt the user to select one or more files
 */
export const initializeAsperaUpload = (apiURL, specifications, updates, accept) => {

  updates.onStatusChange("Launching Aspera")

  const [asperaWeb, uuid] = initializeAsperaClient()

  asperaWebInstance = asperaWeb

  asperaWeb.addEventListener('transfer', (e, asperaObject) => {
    asperaTransferEvents(apiURL, asperaObject, uuid, updates)
    if (asperaObject.transfers.length === 1) {
      uuidInstance = asperaObject.transfers[0].uuid
    }
  })

  const selectFileOptions = {
    success: ({ dataTransfer }) => {
      if (!dataTransfer.files || !dataTransfer.files.length) {
        asperaWeb.stop()
        updates.onProgress(null)
        updates.onComplete()
        return
      }
      updates.onFileSelected(dataTransfer.files[0])
      dataTransfer.files.forEach(file => {
        createUpload(apiURL, asperaWeb, file, specifications)
      })
    },
    error: res => {
      console.error(res)
      window.alert(res.data.message || 'Failed to upload, please try again')
    },
  }

  // use accept props and remove '.' from extension needed for react-dropzone
  const extraOptions = {
    allowedFileTypes: accept ? Object.keys(accept).map((key) => {
      const extensions = accept[key].map((string) => {
        return string.replace(/^\./, "");
    });
      return {
        filter_name: key,
        extensions
      }
    }) : [{
      filter_name: 'Video file',
      extensions: ['mp4', 'mov'],
    }]
  }


  asperaWeb.showSelectFileDialog(selectFileOptions, extraOptions)
}

/*
 * This function will make a POST call to the server to create an upload model and return a destination in S3
 */
const createUpload = (apiURL, asperaWeb, file, specifications = {}) => {
  const sourceFilePath = file.name
  const name = file.name.split('/').pop()
  const newFileList = file
  newFileList.name = name
  const data = {
    method: 'Aspera',
    file_list: [newFileList],
    specifications
  }
  // Creates the upload at the /uploads URL, then starts the upload in the Aspera client.
  post(apiURL, data, res => {
    startAsperaUpload(asperaWeb, file, res.aspera_setup, res.upload_details, sourceFilePath)
  })
}

/*
 * This function will start the upload using Aspera Connect
 */
const startAsperaUpload = (asperaWeb, file, asperaSetup, uploadDetails, sourceFilePath, apiURL) => {
  const transferSpec = {
    ...asperaSetup,
    paths: [{
      source: sourceFilePath
    }]
  }
  const connectSettings = {
    allow_dialogs: 'no',
    use_absolute_destination_path: false,
    upload_details: {
      ...uploadDetails,
      uuid: file.uuid,
    },
  }
  asperaWeb.startTransfer(transferSpec, connectSettings)
}

const asperaWebEventListener = (eventType, dataStatus, asperaInstaller) => {
  if (eventType !== AW4.Connect.EVENT.STATUS) {
    return
  }
  const status = AW4.Connect.STATUS
  switch (dataStatus) {
    case status.INITIALIZING:
      asperaInstaller.showLaunching()
      break
    case status.FAILED:
      asperaInstaller.showDownload()
      break
    case status.OUTDATED:
      asperaInstaller.showUpdate()
      break
    case status.RUNNING:
      asperaInstaller.connected()
      break
    case status.RETRYING:
      break
    default:
      break
  }
}

const asperaTransferEvents = (apiURL, asperaObject, uuid, updates) => {
  console.log("How often is this called...")
  asperaObject.transfers
    .filter(transfer => transfer.aspera_connect_settings.app_id === `nodeConnect-${uuid}`)
    .forEach(transfer => {
      console.log(transfer)
      const uploadDetails = transfer.aspera_connect_settings.upload_details
      apiURL += `/${uploadDetails._id}`
      updates.onStatusChange(transfer.status === 'removed' ? null : transfer.status)
      switch (transfer.status) {
        case AW4.Connect.TRANSFER_STATUS.INITIALIZE:
          put(apiURL, { status: "Initializing" })
          break
        case AW4.Connect.TRANSFER_STATUS.QUEUED:
          break
        case AW4.Connect.TRANSFER_STATUS.RUNNING:
          updates.onProgress({
            current_bytes: transfer.bytes_written,
            total_bytes: transfer.bytes_expected,
            transfer_rate_in_mbps: transfer.calculated_rate_kbps / 1024,
            remaining_time_in_ms: transfer.remaining_usec / 1000
          })
          put(apiURL, {
            status: "Running",
            current_size: transfer.bytes_written,
            total_size: transfer.bytes_expected,
            time_remaining: transfer.remaining_usec / 1000,
            progress: `${Math.floor((transfer.bytes_written / transfer.bytes_expected) * 100)}%`
          })
          break
        case AW4.Connect.TRANSFER_STATUS.COMPLETED:
          updates.onProgress({
            current_bytes: transfer.bytes_written,
            total_bytes: transfer.bytes_expected
          })
          put(apiURL, { status: "Complete" }, updates.onComplete)
          break
        case AW4.Connect.TRANSFER_STATUS.REMOVED:
          break
        case AW4.Connect.TRANSFER_STATUS.CANCELLED:
          put(apiURL, { status: "Cancelled" })
          break
        case AW4.Connect.TRANSFER_STATUS.FAILED:
          put(apiURL, { status: "Failed" })
          console.error("Failed", uploadDetails)
          break
        default:
          break
      }
    })
}

export const stopAsperaUpload = (updates) => {

  const stopOptions = {
    success: () => {
      updates.onStatusChange(null)
      updates.onProgress(null)
    },
    error: res => {
      console.error(res)
      window.alert('Failed to cancel upload, please try again')
    },
  }

  if (typeof asperaWebInstance.removeTransfer(uuidInstance, stopOptions) === "function") {
    asperaWebInstance.removeTransfer(uuidInstance, stopOptions)
  }
}

var asperaWebInstance = null
var uuidInstance = null
