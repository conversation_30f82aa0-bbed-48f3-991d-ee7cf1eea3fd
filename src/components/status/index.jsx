import React from "react"
import { Loader, AlertCircle } from "../icons"

const Status = ({
  pending = false,
  pendingMessage = "",
  error = false,
  errorMessage = "An error occurred. Please contact customer support.",
  empty = false,
  emptyMessage = {},
  type = "primary",
  buttons = [],
  height = "h-full",
  children,
  className = "",
}) => {
  const EmptyIcon = emptyMessage.icon
  if (!pending && !error && !empty) {
    return children
  }
  return (
    <div className={`${height} ${className} text-gray-900 dark:text-gray-200 flex flex-col items-center justify-center space-y-4`}>
      {pending 
        ? <>
            <Loader className='animate-spin h-10 w-10 text-primary-500'/>
            {pendingMessage && <div className='text-sm animate-pulse'>{pendingMessage}</div>}
          </>
        : error
          ? <>
              <AlertCircle className='h-10 w-10 text-error-500'/>
              {errorMessage && <div className={`text-sm`}>{errorMessage}</div>}
              <Buttons buttons={buttons}/>
            </>
          : empty
            ? <>
                {emptyMessage.icon && <EmptyIcon className='h-10 w-10 text-primary-500'/>}
                {emptyMessage.title && <div className='text-lg font-extrabold'>{emptyMessage.title}</div>}
                {emptyMessage.message && <div className='text-sm'>{emptyMessage.message}</div>}
                <Buttons buttons={buttons}/>
              </>
            : null}
    </div>
  )
}

const Buttons = ({ buttons }) => buttons.length === 0 ? null :
  <div className='flex space-x-2'>
    {buttons}
  </div>

export default Status