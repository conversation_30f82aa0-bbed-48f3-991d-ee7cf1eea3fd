import React from "react"
import { Cross } from "../icons"

const dimensionsBySize = {
  small: "h-6 px-2",
  medium: "h-8 px-3"
}

const Chip = ({
  label = "",
  size = "medium",
  icon = null,
  close = false,
  onDelete = null,
  selected = false,
  disabled = false
}) => {
  const Icon = icon
  return (
    <div className={`
      ${dimensionsBySize[size]}
      ${disabled
        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
        : selected
          ? 'bg-primary-100 text-primary-600 cursor-pointer'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 active:bg-gray-300 cursor-pointer'}
      text-xs inline-flex items-center justify-center rounded`}>
      {icon &&
        <Icon className={'mr-1 h-3 w-3'}/>}
      <div>{label}</div>
      {close &&
        <span className={'ml-1'} onClick={onDelete}>
          <Cross className={'h-3 w-3'}/>
        </span>}
    </div>
  )
}

export default Chip
