import React, { forwardRef } from 'react'
import DatePicker from 'react-datepicker'
import dayjs from 'dayjs'
import { ChevronLeft, ChevronRight, Calendar } from '../icons'
import Icon from '../icon'
import 'react-datepicker/dist/react-datepicker.css'
import { registerLocale } from  "react-datepicker";
import en from 'date-fns/locale/en-CA';

registerLocale('en-CA', en)


const range = (start, stop, step = 1) => {
  const length = Math.ceil((stop - start) / step)
  return Array.from({ length }, (_, i) => (i * step) + start)
}

// handle both string and date objects
const handleDate = (d) => {
   if (d instanceof Date && !isNaN(d)) {
    return d
  }

  if (typeof d === 'string') {
    const dateObj = new Date(d)
    if (!isNaN(dateObj)) {
      return dateObj
    }
  }

  return null
}

const years = range(1940, dayjs().year + 1, 1)
const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
]

const DateSelector = ({
  label = null,
  date = null,
  onChange = () => {},
  timeInterval = 5,
  disabled = false,
  isClearable = true,
  showTimeSelect = true,
  showTimeSelectOnly = false,
  showJumpToToday = false,
  showJumpToYear = false,
  icon = null,
  minDate = null,
  maxDate = null,
  minTime = null,
  maxTime = null,
  buttonText = 'Click to set',
  dateFormat = 'MMMM d, yyyy h:mm aa',
  message = null,
  width = 'w-full',
  className = null,
}) => {
  return (
    <div className={`${className} ${width}`}>
      <div className={`text-primary-900 flex flex-col`}>
        <label className="text-xs mb-1">{label}</label>
        <DatePicker
          locale={"en-CA"}
          selected={handleDate(date)}
          minDate={handleDate(minDate)}
          maxDate={handleDate(maxDate)}
          minTime={handleDate(minTime)}
          maxTime={handleDate(maxTime)}
          onChange={date => onChange(date)}
          isClearable={isClearable && !disabled}
          showTimeSelect={showTimeSelect}
          showTimeSelectOnly={showTimeSelectOnly}
          timeIntervals={timeInterval}
          disabled={disabled}
          timeFormat="h:mm a"
          timeCaption="Time"
          dateFormat={dateFormat}
          customInput={<CustomDatePicker
            icon={icon}
            disabled={disabled}
            buttonText={buttonText}
            isClearable={isClearable}
            showTimeSelect={showTimeSelect}
            showTimeSelectOnly={showTimeSelectOnly}
          />}
          renderCustomHeader={props => <CustomHeader
            showJumpToToday={showJumpToToday}
            showJumpToYear={showJumpToYear}
            onChange={onChange}
            {...props}/>}
        />
      </div>
      {message &&
        <span className={`text-gray-500 text-xs leading-4 ${message.className}`}>
          {message.text}
        </span>
      }
    </div>
  )
}

const CustomHeader = ({
  date,
  onChange,
  changeMonth,
  changeYear,
  decreaseMonth,
  increaseMonth,
  prevMonthButtonDisabled,
  nextMonthButtonDisabled,
  showJumpToYear,
  showJumpToToday,
}) =>
  <div className="flex items-center justify-between px-4 py-2">
    <button
      onClick={decreaseMonth}
      disabled={prevMonthButtonDisabled}
      type="button"
      className={`text-gray-700 rounded 
      ${prevMonthButtonDisabled && 'cursor-not-allowed opacity-50'}`}>
      <ChevronLeft className="w-4 h-4 text-gray-800"/>
    </button>
    {showJumpToYear ? <div className={'flex flex-row space-x-4 px-2'}>
        <select
          className={'text-center w-1/2'}
          value={months[dayjs(date).month()]}
          onChange={({ target: { value } }) =>
            changeMonth(months.indexOf(value))}>
          {months.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
        <input type={'number'}
               max={4000}
               min={0}
               className={'w-1/2 text-center'}
               value={dayjs(date).year()}
               onChange={({ target: { value } }) =>
                 changeYear(value)}/>
      </div> :
      <span className="text-sm text-gray-900 font-semibold">
        {dayjs(date).format('MMMM YYYY')}
      </span>}
    {showJumpToToday && <button onClick={() => {
      let today = dayjs()
      changeMonth(today.month())
      changeYear(today.year())
      onChange(today)
    }}><Icon icon={'today'}
    tooltip={{text:'today'}}
             className={`h-4 w-4 text-gray-900'}`}/></button>}
    <button
      onClick={increaseMonth}
      disabled={nextMonthButtonDisabled}
      type="button"
      className={`text-gray-700 rounded 
      ${nextMonthButtonDisabled && 'cursor-not-allowed opacity-50'}`}>
      <ChevronRight className="w-4 h-4 text-gray-800"/>
    </button>
  </div>

const CustomDatePicker = forwardRef(({
    value,
    buttonText,
    onClick,
    icon,
    isClearable,
    showTimeSelect,
    showTimeSelectOnly,
    disabled,
  }, ref) =>
    <button
      ref={ref}
      onClick={onClick}
      className={`flex items-center w-full rounded-lg text-sm px-2 h-10
      ${isClearable ? 'mr-6' : ''}
      ${disabled
        ? 'bg-gray-150 text-gray-400 border-none cursor-not-allowed'
        : 'bg-white text-gray-900 border border-gray-300 hover:border-gray-500'}`}>
      {icon ?
        <Icon icon={icon} className={`h-6 w-6 ${disabled ? 'text-gray-400' : 'text-gray-900'}`}/> :
        <Calendar className={`h-6 w-6 ${disabled ? 'text-gray-400' : 'text-gray-900'}`}/>}
      <span className={`ml-1 ${disabled ? 'text-gray-400' : 'text-gray-900'}`}>
      {showTimeSelectOnly
        ? (value ? dayjs(value).format('h:mm a') : buttonText)
        : !showTimeSelect
          ? (value ? dayjs(value).format('MMMM D, YYYY') : buttonText)
          : (value ? value : buttonText)}
    </span>
    </button>,
)

export default DateSelector
