import React, { useCallback, useMemo, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import Button from '../button'
import Status from '../status'
import Icon from "../icon"
import { Aspera } from "../icons"
import { initializeAsperaUpload } from '../helpers/aspera'
import { initializeUppyUpload, startUppyUpload } from '../helpers/uppy'
import { isValidFileType } from '../helpers/file_type'

const uploadDefaults = {
  icon: "upload_file",
  message: "Drag and drop or click here to upload",
  dropMessage: "Drop file here!",
  accept: {},
  multiple: false,
  publicURL: null,
  onFileSelected: null,
  apiURL: null,
  onProgress: () => {},
  onComplete: () => {},
  onRemove: () => {},
  onError: () => {},
  asperaEnabled: false,
  specifications: {}
}

const backgroundImageDefaults = {
  url: null,
  contain: true,
  cover: false
}

const FileUpload = ({
  upload,
  button = {
    text: "Upload file(s)",
  },
  className = null,
  file = null,
  backgroundImage = null,
  includeRemoveButton = true,
}) => {

  const {
    message,
    icon,
    dropMessage,
    accept,
    multiple,
    publicURL,
    onFileSelected,
    onProgress,
    onComplete,
    onRemove,
    onError,
    apiURL,
    asperaEnabled,
    specifications
  } = { ...uploadDefaults, ...upload }

  const [status, setStatus] = useState()
  const [pending, setPending] = useState(false)
  const [progress, setProgress] = useState(null)
  const [uploadingFile, setUploadingFile] = useState()


  const callbacks = useMemo(() => ({
    onFileSelected: file => {
      setUploadingFile(file)
      if (typeof onFileSelected === "function") {
        onFileSelected(file)
      }
    },
    onStatusChange: status => {
      setStatus(status)
    },
    onProgress: progress => {
      setPending(false)
      setProgress(progress)
      onProgress(progress)
    },
    onComplete: (file = null, destinationURL = null, uploadId = null) => {
      onComplete(file, destinationURL, uploadId)
      setProgress()
      setStatus(null)
    },
    onError: error => {
      onError(error)
    }
  }), [onFileSelected, onComplete, onProgress, onError])

  // Once the file is dropped...
  const onDrop = useCallback(acceptedFiles => {

    if (acceptedFiles.length === 0) {
      return
    }

    const acceptedFile = acceptedFiles[0]

    //Check file type validity
    isValidFileType(acceptedFile).then((isValid) => {
      if(isValid) {

        // If there is a public destination URL present, we can upload directly to that URL.
        if (publicURL) {
          startUppyUpload(acceptedFile, `${publicURL}/${acceptedFile.name}`, callbacks)
          return
        }

        // If there is a onFileSelected function defined, we use that which may trigger the upload after returning a signed URL.
        if (typeof onFileSelected === "function") {
          
          onFileSelected(acceptedFile, res => startUppyUpload(acceptedFile, res.destinationURL, callbacks))
          return
        }

        // Otherwise, we use the API URL to create an upload and handle S3 signatures, etc.
        setPending(true)
        initializeUppyUpload(acceptedFile, specifications, callbacks, apiURL)
      } else {
        console.error("Invalid file type")

        if (typeof onError === "function") {
          onError("Invalid file type")
        }
         
      }
    })


  }, [publicURL, callbacks, specifications, apiURL, onFileSelected])

  const { getRootProps, getInputProps, open, isDragActive } = useDropzone({ onDrop, multiple, accept })

  // Handling background images:
  backgroundImage = { ...backgroundImageDefaults, ...backgroundImage }
  const backgroundImageCover = backgroundImage?.cover
  const backgroundImageContain = backgroundImage?.contain
  const backgroundImageURL = backgroundImage?.url

  // Handling upload options and Aspera callbacks
  const uploadOptions = {
    asperaEnabled,
    initializeHttpUpload: () => open(),
    initializeAsperaUpload: () => {
      setPending(true)
      initializeAsperaUpload(
        apiURL,
        specifications,
        callbacks
      )
    }
  }

  return (
    <div {...getRootProps()} className={`${className} relative group`}>
      <input {...getInputProps()} />
      {isDragActive ?
        <div className={`absolute bg-black/80 inset-0 text-white flex justify-center items-center p-4`}>
          {dropMessage}
        </div> : null}
      {status
        ? <Progress file={uploadingFile} pending={pending} status={status} progress={progress}/>
        : backgroundImageURL
          ? <div
            className={`h-full w-full bg-gray-200 bg-center bg-no-repeat ${backgroundImageCover ? 'bg-cover' : backgroundImageContain ? 'bg-contain' : ''}`}
            style={{ backgroundImage: `url("${backgroundImageURL}")` }}>
            <div className='relative bg-black/80 flex flex-col items-center justify-center text-white h-full invisible group-hover:visible'>
              <Instructions
                icon={icon}
                message={message}
                button={button}
                type={"white"}
                uploadOptions={uploadOptions}/>
              {includeRemoveButton && <Remove onClick={() => {
                onRemove()
                setUploadingFile(null)
              }}/>}
            </div>
          </div>
          :   file
            ? <div className={`w-full h-full flex flex-col justify-center items-center p-4 border-primary-500 border-2 rounded text-sm space-y-2`}>
              <Icon icon="check_circle_outline" className="text-success-500"/>
              <span className='font-semibold text-xs w-full truncate text-center'>{file.name}</span>
              {Number.isInteger(file.size) && <span className='text-xs'>({humanFileSize(file.size)})</span>}
            </div>
            : <div className={`w-full h-full flex flex-col justify-center items-center p-4 bg-gray-50 hover:bg-gray-100
              border-2 border-primary-500 text-primary-500 rounded border-dashed
              group-hover:text-primary-600 group-hover:border-primary-600`}>
              <Instructions
                icon={icon}
                message={message}
                button={button}
                uploadOptions={uploadOptions}/>
            </div>}
    </div>
  )
}

const Instructions = ({ icon, message, button, type = "primary", uploadOptions }) =>
  <>
    {icon && <Icon icon={icon} className='pb-2'/>}
    {message && <p className={`text-center text-sm`}>{message}</p>}
    <div className='flex items-center space-x-2 mt-4'>
      {button && <Button
        tertiary
        type={type}
        icon={`upload`}
        iconPosition={`left`}
        {...button}>
        {button.text}
      </Button>}
      {uploadOptions.asperaEnabled && <Button
        tertiary
        type={type}
        icon={Aspera}
        iconPosition={`left`}
        onClick={() => uploadOptions.initializeAsperaUpload()}>
        Upload with Aspera
      </Button>}
    </div>
  </>

const Progress = ({ file, pending, status, progress }) => {
  const percentage = progress && progress.current_bytes ? parseInt(progress.current_bytes / progress.total_bytes * 100) : 0
  const transferRate = progress && progress.transfer_rate_in_mbps ? parseInt(progress.transfer_rate_in_mbps) : null
  const timeRemaining = progress && progress.remaining_time_in_ms ? convertToTimecode(progress.remaining_time_in_ms) : null
  return (
    <div className={`w-full h-full flex flex-col justify-center items-center p-4 border-primary-500 border-2 rounded text-xs space-y-2`}>
      <Status pending={pending} pendingMessage={status}>
        {file ? <span className='w-full truncate text-center font-semibold'>{file.name}</span> : null}
        <progress className='w-full h-4' max={100} value={percentage}/>
        <div className='flex items-center space-x-2'>
          <span className='capitalize'>{status}</span>
          {percentage > 0 && percentage < 100 ? <span>({percentage}%)</span> : null}
        </div>
        {progress && progress.current_bytes ? <div className='flex items-center space-x-2'>
          <span>{humanFileSize(progress.current_bytes)} / {humanFileSize(progress.total_bytes)}</span>
          {transferRate ? <span>({transferRate} mbps)</span> : null}
        </div> : null}
        {timeRemaining ? <div>Time remaining: {timeRemaining}</div> : null}
      </Status>
    </div>
  )
}

const Remove = ({ onClick }) =>
  <div className='absolute right-0 bottom-0 m-2 z-10'>
    <Button
      tertiary={true}
      type="error"
      size={"small"}
      icon="delete_forever"
      onClick={onClick}/>
  </div>

const convertToTimecode = duration => {
  var seconds = Math.floor((duration / 1000) % 60),
    minutes = Math.floor((duration / (1000 * 60)) % 60),
    hours = Math.floor((duration / (1000 * 60 * 60)) % 24)

  hours = (hours < 10) ? "0" + hours : hours
  minutes = (minutes < 10) ? "0" + minutes : minutes
  seconds = (seconds < 10) ? "0" + seconds : seconds

  return hours + ":" + minutes + ":" + seconds
}

const humanFileSize = (bytes, si=true, dp=1) => {
  const thresh = si ? 1000 : 1024;

  if (Math.abs(bytes) < thresh) {
    return bytes + ' B';
  }

  const units = si
    ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];
  let u = -1;
  const r = 10**dp;

  do {
    bytes /= thresh;
    ++u;
  } while (Math.round(Math.abs(bytes) * r) / r >= thresh && u < units.length - 1);

  return bytes.toFixed(dp) + ' ' + units[u];
}

export default FileUpload
