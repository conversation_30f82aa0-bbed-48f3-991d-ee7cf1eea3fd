import React, { useLayoutEffect, useState } from "react";
import { mobileBreakpoint } from "../constants";

const useIsMobile = (customBreakpoint) => {
  const breakpoint = customBreakpoint ? customBreakpoint : mobileBreakpoint
  const [isMobile, setIsMobile] = useState(window.innerWidth < breakpoint);

  useLayoutEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < breakpoint);
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return isMobile;
};

export default useIsMobile;