import React from 'react'
import useIsMobile from '../hooks/use_is_mobile'
import Button from '../button'

export default function ToggleViewType({
  type,
  onViewChange
}) {
  return (
    <Button
      minimal
      type='neutral'
      icon={type === "grid" ? "view_list" : "grid_view"}
      tooltip={{
        text: type === "grid" ? "Show List View" : "Show Grid View"
      }}
      onClick={() => onViewChange(type === "grid" ? "table" : "grid")}/>
  )
}
