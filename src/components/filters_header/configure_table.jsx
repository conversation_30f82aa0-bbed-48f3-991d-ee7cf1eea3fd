import React from 'react'
import ButtonDropdown from '../button_dropdown'
import Icon from '../icon'
import Toggle from '../toggle'
import useIsMobile from '../hooks/use_is_mobile'

export default function ConfigureColumns({
  swapOrder = () => {},
  orderedColumns,
  activeColumns,
  setActiveColumns,
  orderArr,
  spaced,
  isCompact = false,
  toggleIsCompact = () => {},
  isSpaced = false,
  toggleIsSpaced = () => {}
}) {

  const isMobile = useIsMobile()
  const validColumns = orderedColumns.filter(column => !!column.text)

  const ColumnVisibility = () => {
    return (
      <div className='flex-col p-4'>
        <div className='font-semibold border-b mb-2 pb-2'>Column visibility</div>
        {validColumns.map((column, index) => {
          const checked = activeColumns.includes(index)
          return (
            <div key={column.text} className='flex items-center justify-between space-x-8'>
              <Toggle
                label={column.text}
                size='small'
                disabled={activeColumns.length === 1 && checked}
                checked={checked}
                onChange={() => setActiveColumns(checked
                  ? activeColumns.filter(opt => opt !== index)
                  : [...activeColumns, index])}/>
              <div className='space-x-1'>
                <Icon
                  className={`text-lg ${index === 0 ? 'pointer-events-none text-gray-300' : ''}`}
                  icon='keyboard_arrow_up'
                  tooltip={index === 0 ? null : { text: 'Shift this column left' }}
                  onClick={() => swapOrder(index, index - 1, orderArr)} />
                <Icon
                  className={`text-lg ${index === validColumns.length - 1 ? 'pointer-events-none text-gray-300' : ''}`}
                  icon='keyboard_arrow_down'
                  tooltip={{ text: 'Shift this column right' }}
                  onClick={() => swapOrder(index, index + 1, orderArr)} />
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  const RowDensity = () => {
    return (
      <div className='flex flex-col border-l p-4'>
        <div className='font-semibold border-b mb-2 pb-2'>Row density</div>
        <div className='h-7 flex items-center'>
          <Toggle label='Spaced Rows' size='small' checked={isSpaced} onChange={() => toggleIsSpaced()}/>
        </div>
        <div className='h-7 flex items-center'>
          <Toggle label='Compact Rows' size='small' checked={isCompact} onChange={() => toggleIsCompact()}/>
        </div>
      </div>
    )
  }

  return (
    <>
      <ButtonDropdown
        button={{
          type: 'neutral',
          minimal: true,
          icon: 'view_column',
          outlinedIcon: true,
          text: !isMobile && 'View',
          tooltip: {
            direction: 'top',
            text: 'Modify columns and rows'
          }
        }}
        dropdown={{
          clickCloses: false,
          anchor: "left",
          content:
            <div className='min-w-[200px] flex space-x-4 text-sm text-gray-600 mr-4'>
              <ColumnVisibility/>
              <RowDensity/>
            </div>
        }}/>
    </>
  )
}
