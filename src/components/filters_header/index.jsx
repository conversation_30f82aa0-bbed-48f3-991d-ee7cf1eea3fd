import React from "react";
import Searching from "./searching";
import FilterButton from "../filter_button";
import SortButton from "../sort_button";
import ConfigureTable from "./configure_table";
import ToggleViewType from "./toggle_view_type";

export default function FiltersHeader({
  title = null,
  searching,
  filtering,
  sorting = {},
  customElement,
  isGrid = false,
  view,
  swapOrder = () => {},
  ...props
}) {
  return (
    title ||
    searching ||
    filtering ||
    customElement ||
    (sorting && sorting.options) ? (
      <div className="flex space-x-2 flex-wrap w-full items-center justify-between md:space-y-0 space-y-2">
        {title ? <div className="flex pr-4">{title}</div> : null}
        {searching && <Searching {...searching} hasTitle={!!title} />}
        <div className="flex flex-wrap justify-end space-x-2">
          {filtering && <FilterButton {...filtering} />}
          {sorting && sorting.options && <SortButton {...sorting} />}
          {props.columns && !isGrid ? <ConfigureTable swapOrder={swapOrder} {...props} /> : null}
          {view ? <ToggleViewType {...view}/> : null}
          {customElement}
        </div>
      </div>
    ) : null
  )
}
  