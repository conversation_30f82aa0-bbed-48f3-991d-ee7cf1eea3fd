import React from 'react'
import { Search } from '../icons'
import Input from '../input'

const Searching = ({
  hasTitle = false,
  search,
  searchPlaceholder = 'Search...',
  onSearch,
  rightIcon
}) => <div className='flex-auto items-center text-gray-500 mr-4'>
      {typeof onSearch === 'function' &&
        <Input
          placeholder={searchPlaceholder}
          input={{ leftIcon: { icon: Search }, rightIcon }}
          value={search}
          onChange={e => onSearch(e.target.value)}
          />}

  </div>

export default Searching
