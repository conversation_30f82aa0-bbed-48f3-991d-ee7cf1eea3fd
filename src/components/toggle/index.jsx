import React from 'react'

const Toggle = ({
  id,
  checked = false,
  onChange = () => {},
  size = "medium",
  disabled = false,
  label = false,
  className = "",
}) =>
  <label id={id} className={`inline-flex relative ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'} items-center group ${className}`}>
    <input
      type="checkbox"
      checked={checked}
      onChange={onChange}
      className={`absolute left-1/2 -translate-x-1/2 w-full h-full peer appearance-none rounded-md
        ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
      disabled={disabled}
    />
    <span
      className={`flex items-center flex-shrink-0 p-1 rounded-full duration-300 ease-in-out
      ${size === 'small'
        ? 'w-8 h-4 after:w-3 after:h-3 peer-checked:after:translate-x-3'
        : size === 'medium'
        ? 'w-10 h-5 after:w-4 after:h-4 peer-checked:after:translate-x-4'
        : 'w-12 h-6 after:w-5 after:h-5 peer-checked:after:translate-x-5'}
      ${disabled
        ? 'bg-gray-200 cursor-not-allowed'
        : `cursor-pointer 
        bg-gray-300 group-hover:bg-gray-400 group-active:bg-gray-500 
        peer-checked:bg-blue-500 group-hover:peer-checked:bg-blue-600 group-active:peer-checked:bg-blue-700`}  
      after:bg-white after:rounded-full after:shadow-md after:duration-300`}/>
    {label && <span
      className={`
        ${disabled ? 'text-gray-400' : 'text-gray-700 dark:text-gray-200'}
        ${size === 'small' ? 'ml-2 text-xs' : size === 'medium' ? 'ml-3 text-sm' : 'ml-3 text-base'}`}>
        {label}
      </span>}
  </label>

export default Toggle
