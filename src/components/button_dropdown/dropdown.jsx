import React, { useEffect } from "react"
import Row from "./row"



const Dropdown = ({ dropdown, innerRef, parentTetherClass, onClose }) => {

  const isMenu = Array.isArray(dropdown.content)

  const onMouseDown = e => {

    if (doesNodeHaveClass(e.target, 'tether-parent-row')) {
      return 
    }
    
    // If the parent node is the button, we won't close as the button onClick will handle closing itself.
    if (doesNodeHaveClass(e.target, parentTetherClass)) {
      return 
    }
    
    // If this is a button from the dropdown menu, close the menu after a split second.
    if (doesNodeHaveClass(e.target, 'tether-button')) {
      setTimeout(() => {
        onClose()
      }, 200)
      return
    }

    // Handle mouse events when it's a custom dropdown -- if it has classToClose, close it.
    if (doesNodeHaveClass(e.target, dropdown.classToClose)) {
      setTimeout(() => {
        onClose()
      }, 200)
      return
    }

    // If this is the custom dropdown menu, close if dropdown.clickCloses is true
    if (doesNodeHaveClass(e.target, 'tether-menu') && !dropdown.clickCloses) {
      return
    }

    // Otherwise, close the menu.
    onClose()

  }

  // Checking for whether any element that is the parent of a click is the tether button.
  const doesNodeHaveClass = (node, className) => {
    if (!node || !node.classList) {
      return false
    }
    if (node.classList.contains(className)) {
      return true
    }
    if (!node.parentNode) {
      return false
    }
    return doesNodeHaveClass(node.parentNode, className)
  }

  const onKeyDown = e => {
    if (e.keyCode === 27) {
			onClose(e)
		}
  }

  useEffect(() => {
    document.addEventListener("mousedown", onMouseDown, false)
    document.addEventListener("keydown", onKeyDown, false)
    return () => {
      document.removeEventListener("mousedown", onMouseDown, false)
      document.removeEventListener("keydown", onKeyDown, false)
    }
  })

  if (!dropdown.content) {
    return null
  }

  return (
    <div
      ref={innerRef}
      style={dropdown.style}
      className={`
        whitespace-nowrap flex flex-col rounded-lg drop-shadow-md
        bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-200
        animate-scale-accordion origin-top ${isMenu ? 'min-w-[220px]' : ''}
      `}>
      {isMenu
        ? dropdown.content.map((row, index) => <Row row={row} key={index} first={index === 0} last={index === dropdown.content.length - 1}/>)
        : <div className='tether-menu'>{dropdown.content}</div>
      }
    </div>
  )
}

export default Dropdown