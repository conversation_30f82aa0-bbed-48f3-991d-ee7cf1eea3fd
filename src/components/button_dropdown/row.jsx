import React, { useState } from "react"
import TetherComponent from "react-tether"
import { ChevronRight } from "../icons"
import Icon from "../icon"
import useIsMobile from "../hooks/use_is_mobile"

const Row = ({ row, first, last }) => {
  const [parentHover, setParentHover] = useState(false)
  const [innerHover, setInnerHover] = useState(false)

  const hasChildren = row.children && row.children.length > 0
  const text = row.text?.title || row.text
  const description = row.text?.description

  const isHovering = () => (parentHover || innerHover)
  const isMobile = useIsMobile(768)

  return (
    <TetherComponent
      attachment={`top left`}
      targetAttachment="top right"
      constraints={[
        {
          to: 'window',
          attachment: 'together',
          pin: true
        }
      ]}
      renderTarget={(targetRef) => (
        <>
          {row.component ? row.component : <div
            ref={targetRef}
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              if (row.disabled) {
                return 
              }
              row.onClick?.()
            }}
            className={`
              text-sm font-medium whitespace-nowrap tether-button min-w-[160px]
              flex items-center justify-between w-full h-12 px-4 transition duration-200 shrink-0
              ${first ? 'mt-1' : ''} ${last ? 'mb-1' : ''}
              ${hasChildren ? 'tether-parent-row ' : ''}
              dark:bg-gray-800 dark:text-gray-200
              ${row.disabled ?
                  `cursor-not-allowed text-gray-400` :
                  `cursor-pointer hover:bg-primary-100 text-gray-700 dark:hover:bg-gray-700`}
              ${row.className}
            `}
            onMouseOver={() => hasChildren && setParentHover(true)}
            onMouseLeave={() => hasChildren && setParentHover(false)}>
            <span className={`flex items-center space-x-3`}>
              {row.icon &&
                <Icon icon={row.icon} className={typeof row.icon !== 'string' ? 'w-5 h-5' : 'w-5'}/>
              }
              <div className='flex flex-col tether-menu-row'>
                {text}
                {description && <span className='text-xs text-gray-400'>{description}</span>}
              </div>
            </span>
            {hasChildren && <ChevronRight className={'w-4 h-4 ml-4 text-gray-600'}/>}
          </div>}
          {row.breakAfter && <div className="border-t border-gray-200"/>}
        </>
      )}
      renderElement={(elementRef) => isHovering() && (
        <div
          onMouseEnter={() => isHovering() && setInnerHover(true)}
          onMouseLeave={() => isHovering() && setInnerHover(false)}
          ref={elementRef}
          className={`
            bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-600
            text-left drop-shadow-md overflow-hidden rounded-lg origin-top
            ${isMobile ? '' : 'animate-scale-accordion'}`}>
          {isHovering() && row?.children?.map((childRow, index) => <Row row={childRow} key={index}/>)}
        </div>
      )}
    />
  )
}

export default Row
