import React, { useEffect, useState } from 'react'
import Dropdown from "./dropdown"
import TetherComponent from "react-tether"
import uuid from "react-uuid"
import Button from '../button'

const ButtonDropdown = ({
  button = {},
  dropdown = {
    content: [],
    anchor: "right",
    clickCloses: true,
    style: {}
  },
  kebab = false,
}) => {
  const {
    text = "",
    menu = false,
    className = "",
    ...remainingButtonProps
  } = button

  const [open, setOpen] = useState(false)
  const parentTetherClass = "tether-button-" + uuid()
  const onClick = () => {
    setOpen(!open)
  }

  const buttonProps = kebab ? {
    icon: 'more_vert',
    minimal: true,
    type: 'neutral',
    ...remainingButtonProps
  } : remainingButtonProps

  return (
    <TetherComponent
      attachment={`top ${dropdown.anchor || "right"}`}
      targetAttachment={`bottom ${dropdown.anchor || "right"}`}
      constraints={[
        {
          to: 'window',
          attachment: 'together',
          pin: true
        }
      ]}
      renderTarget={ref =>
        <Button
          {...buttonProps}
          innerRef={ref}
          onClick={onClick}
          iconClassName={menu ? "h-6 w-6" : "h-4 w-4"}
          className={`${className} ${parentTetherClass}`}>
          {text}
        </Button>
      }
      renderElement={elementRef => open &&
        <Dropdown
          dropdown={dropdown}
          innerRef={elementRef}
          parentTetherClass={parentTetherClass}
          onClose={() => setOpen(false)}/>
      }
    />
  )
}

export default ButtonDropdown