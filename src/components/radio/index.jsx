import React from 'react'

const Radio = ({
  id = null,
  checked = false,
  small = false,
  size = "medium",
  label = null,
  disabled = false,
  onChange = () => {}
}) => {
  small = small || size === "small"
  let medium = size === "medium"
  return (
    <label className={`inline-flex relative ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'} group items-center`}>
      <input
        id={id}
        type='radio'
        checked={checked}
        disabled={disabled}
        onChange={onChange}
        className={`opacity-0 absolute top-0 left-0 right-0 bottom-0 ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'} `}/>
      <span
        className={`${small ? 'w-4 h-4' : medium ? 'w-5 h-5' : 'w-6 h-6'} flex rounded-full justify-center items-center
        ${disabled && checked
            ? 'bg-gray-200'
            : disabled
              ? 'border border-gray-200'
              : checked
                ? 'bg-blue-500 group-hover:bg-blue-600 group-active:bg-blue-700'
                : 'border border-gray-400 group-hover:border-gray-500 group-active:border-gray-600'}`}>
        {checked
          ? <div className={`bg-white rounded-full ${small ? 'w-1.5 h-1.5' : medium ? 'w-2 h-2' : 'w-2.5 h-2.5'}`}/>
          : <div className={small ? 'w-1.5 h-1.5' : medium ? 'w-2 h-2' : 'w-2.5 h-2.5'}/>}
      </span>
      {label && <span className={`${disabled && small
        ? 'text-gray-400'
        : disabled
          ? 'text-gray-200'
          : 'text-gray-700'} ${small ? 'ml-2 text-xs' : medium ? 'ml-2 text-sm' : 'ml-3'}`}>{label}</span>}
    </label>
  )
}

export default Radio
