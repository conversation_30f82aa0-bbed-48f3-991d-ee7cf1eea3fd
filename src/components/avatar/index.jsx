import React from "react"

const Avatar = ({ url = null, name }) =>
  <div className='rounded-full w-10 h-10 border-gray-300 box-border border border-solid flex items-center justify-center p-0.5'>
    {url
      ? <img src={url} alt={name} className="rounded-full object-cover h-full w-full"/>
      : <span className='text-gray-600 text-sm font-medium'>{name.split(" ").map((n)=>n[0]).join("")}</span>}
  </div>

export default Avatar
