import React, { useState } from 'react'
import ButtonDropdown from '../button_dropdown'
import Button from '../button'
import Checkboxes from './checkboxes'
import RadioButtons from './radio_buttons'
import DateRange from './date_range'
import ColorBoxes from './color_boxes'
import Custom from './custom'
import useIsMobile from '../hooks/use_is_mobile'
import { Input } from '..'

const Filtering = ({ options = [], ...props }) => {
  const [searchValues, setSearchValues] = useState({})

  const filterCount = () => {
    if (props.filters) {
      Object.keys(props.filters).forEach(
        (k) =>
          (props.filters[k] == null || props.filters[k].length === 0) &&
          delete props.filters[k]
      )

      return Object.keys(props.filters).length
    }
    return false
  }
  const isMobile = useIsMobile()

  const filterOptions = options.map((opt, index) => ({
    ...opt,
    options: searchValues[index] ? opt.options.filter(
      (option) => option.label.toLowerCase().includes(searchValues[index].toLowerCase())
    ) : opt.options
  }))

  return (
    <ButtonDropdown
      button={{
        type: filterCount() ? "primary" : "neutral",
        secondary: filterCount(),
        minimal: !filterCount(),
        text:
          !isMobile && "Filter" + (filterCount() ? ` (${filterCount()})` : ""),
        icon: "filter_list",
      }}
      dropdown={{
        anchor: "left",
        classToClose: "close-dropdown",
        content: (
          <div className="p-4 max-w-screen max-h-screen overflow-auto">
            <div
              className={`block ${
                isMobile ? "space-y-2" : ""
              } md:flex items-stretch text-sm text-gray-600`}
            >
              {filterOptions.map((filterOption, index) => (
                <div
                  key={filterOption.key}
                  className={`max-h-60 pr-4 flex flex-col ${
                    index !== 0 && !isMobile
                      ? "border-l border-gray-200 pl-4"
                      : ""
                  }`}
                >
                  <span className="font-semibold border-b border-gray-200 mb-2 pb-2">
                    {filterOption.label}
                  </span>
                  <div className="flex flex-col overflow-x-hidden overflow-y-auto space-y-1">
                    {filterOption?.searchable &&
                      ["checkboxes", "radio_buttons"].includes(
                        filterOption.type
                      ) && (
                        <Input
                          value={searchValues[index]}
                          placeholder='Search Filters'
                          onChange={(e) =>
                            setSearchValues({ ...searchValues, [index]: e.target.value })
                          }
                        />
                      )}
                    {filterOption.type === "checkboxes" ? (
                      <Checkboxes filterOption={filterOption} {...props} />
                    ) : filterOption.type === "radio_buttons" ? (
                      <RadioButtons filterOption={filterOption} {...props} />
                    ) : filterOption.type === "date_range" ? (
                      <DateRange filterOption={filterOption} {...props} />
                    ) : filterOption.type === "color_boxes" ? (
                      <ColorBoxes filterOption={filterOption} {...props} />
                    ) : filterOption.type === "custom" ? (
                      <Custom filterOption={filterOption} {...props} />
                    ) : null}
                  </div>
                </div>
              ))}
            </div>
            <div className="flex space-x-4 justify-end mt-4">
              {isMobile && (
                <Button
                  size="small"
                  className="close-dropdown"
                  onClick={() => {}}
                >
                  Close
                </Button>
              )}
              <Button
                className="close-dropdown"
                size="small"
                onClick={() => props.onFiltersChange(null)}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        ),
      }}
    />
  )
}

export default Filtering
