import React from 'react'
import Icon from '../icon'

const ColorBoxes = ({
  filterOption,
  filters,
  onFiltersChange
}) => 
  <div className='grid grid-cols-3 gap-y-3'>
    {filterOption.options.map(color => {      
      let selected = filters && filters[filterOption.key] ? filters[filterOption.key] : []
      let checked = selected.includes(color)
      return (
        <div
          className='border rounded-full w-6 h-6 cursor-pointer'
          style={{ backgroundColor: color }}
          onClick={() => onFiltersChange({
            ...filters,
            [filterOption.key]: checked
              ? selected.filter(opt => opt !== color)
              : [...selected, color]
          })}>
          {checked && <Icon icon='check' className='text-white text-sm pl-1'/>}
        </div>
      )
    })}
  </div>

export default ColorBoxes
