import React from 'react'
import DatePicker from '../date_picker'

const DateRangeFilter = ({
  filterOption,
  filters,
  onFiltersChange
}) => {
  const startDate = filters && filters[filterOption.key] ? filters[filterOption.key].start_date : null
  const endDate = filters && filters[filterOption.key] ? filters[filterOption.key].end_date : null
  const showTime = filterOption?.options?.show_time ?? false
  return (
    <div className='space-y-2'>
      <DatePicker
        width="w-64"
        buttonText="Start date"
        date={startDate}
        dateFormat={"MMM d, yyyy h:mm aa"}
        onChange={value => onFiltersChange({
          ...filters,
          [filterOption.key]: {
            start_date: value,
            end_date: endDate
          }
        })}
        showTimeSelect={showTime}/>
      <DatePicker
        width="w-64"
        buttonText="End date"
        date={endDate}
        dateFormat={"MMM d, yyyy h:mm aa"}
        onChange={value => onFiltersChange({
          ...filters,
          [filterOption.key]: {
            start_date: startDate,
            end_date: value
          }
        })}
        showTimeSelect={showTime}/>
    </div>
  )
}

export default DateRangeFilter
