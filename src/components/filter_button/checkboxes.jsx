import React from 'react'
import Checkbox from '../checkbox'

const Checkboxes = ({
  filterOption,
  filters,
  onFiltersChange
}) => 
  filterOption.options.map(option => {
    let selected = filters && filters[filterOption.key] ? filters[filterOption.key] : []
    const checked = selected.includes(option.key)
    return (
      <Checkbox
        className='inline-flex'
        key={option.key}
        label={option.label}
        size={'small'}
        checked={checked}
        onChange={() => onFiltersChange({
          ...filters, 
          [filterOption.key]: !checked
            ? [...selected, option.key] // Add the selected checkbox
            : selected.filter(opt => opt !== option.key) // Remove the selected checkbox
        })}
      />
    )
  })

export default Checkboxes
