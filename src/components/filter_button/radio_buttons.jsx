import React from 'react'
import Radio from '../radio'

const RadioButtons = ({
  filterOption,
  filters,
  onFiltersChange
}) => 
  filterOption.options.map(option => {
    const checked = filters && filters[filterOption.key] ? filters[filterOption.key] === option.key : false
    return (
      <Radio
        key={option.key}
        label={option.label}
        small
        checked={checked}
        onChange={() => onFiltersChange({
          ...filters, 
          [filterOption.key]: option.key
        })}
      />
    )
  })

export default RadioButtons
