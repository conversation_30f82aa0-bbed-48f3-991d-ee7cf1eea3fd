import React from 'react'
import Icon from '../icon'
import ReactTooltip from 'react-tooltip'
import uuid from "react-uuid"

const classBySize = {
  small: "h-8 rounded-md px-4 py-1 leading-6 text-sm", 
  medium: "h-10 rounded-md px-6 py-2 leading-6 text-sm",
  large: "h-12 rounded-lg px-8 py-3 leading-6 text-base"
}

const classBySizeNoText = {
  small: "h-8 w-8 rounded-md", 
  medium: "h-10 w-10 rounded-md",
  large: "h-12 w-12 rounded-lg"
}

const primaryClassByType = {
  primary: "bg-primary-500 text-white hover:bg-primary-600 active:bg-primary-700",
  info: "bg-info-500 text-white hover:bg-info-600 active:bg-info-700",
  success: "bg-success-500 text-white hover:bg-success-600 active:bg-success-700",
  warning: "bg-warning-500 text-white hover:bg-warning-600 active:bg-warning-700",
  error: "bg-error-500 text-white hover:bg-error-600 active:bg-error-700",
  neutral: "bg-gray-500 text-white hover:bg-gray-600 active:bg-gray-700",
}

const minimalClassByType = {
  primary: "text-primary-600 hover:bg-primary-100 active:bg-primary-200",
  info: "text-info-600 hover:bg-info-100 active:bg-info-200",
  success: "text-success-600 hover:bg-success-100 active:bg-success-200",
  warning: "text-warning-600 hover:bg-warning-100 active:bg-warning-200",
  error: "text-error-600 hover:bg-error-100 active:bg-error-200",
  neutral: "text-neutral-600 hover:bg-neutral-100 active:bg-neutral-200",
}

const secondaryClassByType = {
  primary: "border-primary-100 hover:border-primary-200 bg-primary-100 text-primary-600 hover:bg-primary-200 active:bg-primary-300",
  info: "border-info-100 hover:border-info-200 bg-info-100 text-info-600 hover:bg-info-200 active:bg-info-300",
  success: "border-success-100 hover:border-success-200 bg-success-100 text-success-600 hover:bg-success-200 active:bg-success-300",
  warning: "border-warning-100 hover:border-warning-200 bg-warning-100 text-warning-600 hover:bg-warning-200 active:bg-warning-300",
  error: "border-error-100 hover:border-error-200 bg-error-100 text-error-600 hover:bg-error-200 active:bg-error-300",
  neutral: "border-neutral-100 hover:border-neutral-200 bg-neutral-100 text-neutral-600 hover:bg-neutral-200 active:bg-neutral-300",
}

const tertiaryClassByType = {
  primary: "border-primary-500",
  info: "border-info-500",
  success: "border-success-500",
  warning: "border-warning-500",
  error: "border-error-500",
  neutral: "border-neutral-500",
  white: "border-white"
}

const colorByType = {
  primary: 'text-primary-500 hover:text-primary-600 hover:border-primary-600 active:text-primary-700 active:border-primary-700',
  info: 'text-info-500 hover:border-info-600 hover:text-info-600 active:border-info-700 active:text-info-700',
  success: 'text-success-500 hover:border-success-600 hover:text-success-600 active:border-success-700 active:text-success-700',
  warning: 'text-warning-500 hover:border-warning-600 hover:text-warning-600 active:border-warning-700 active:text-warning-700',
  error: 'text-error-500 hover:border-error-600 hover:text-error-600 active:border-error-700 active:text-error-700',
  neutral: 'text-neutral-500 hover:border-neutral-600 hover:text-neutral-600 active:border-neutral-700 active:text-neutral-700',
  white: 'text-white'
}

const Button = ({
  size = "medium", // small, medium, large
  type = "primary", // primary, neutral, info, success, warning, error, white
  icon = null,
  outlinedIcon = false,
  secondary = false, // handles the "faded" buttons
  tertiary = false,  // handles the "outline" buttons
  minimal = false,   // handles the "text-only" buttons
  link = false,      // handles the "text-only" with underline, and no-outline buttons
  iconPosition = "left", // left, right
  disabled = false,
  onClick = null,
  className = "",
  iconClassName = "w-4 h-4",
  tooltip = {},
  innerRef = null,
  component = null,
  children,
}) => {
  const tooltipID = uuid()
  const tooltipComponent = tooltip.text ? <ReactTooltip id={tooltipID} effect='solid' place={tooltip.direction || 'bottom'}>{tooltip.text}</ReactTooltip> : null

  // If this is just a "component" button, return the content without the button.
  if (component) {
    return (<div ref={innerRef} className={`inline-flex cursor-pointer ${className}`} data-tip data-for={tooltipID} onClick={onClick}>
      {tooltipComponent}
      {component}
    </div>)
  }
  
  return (
    <button
      ref={innerRef}
      data-tip
      data-for={tooltipID} 
      className={`
        font-subheader
        ${className}
        ${children ? classBySize[size] : classBySizeNoText[size]}
        ${link
            ? `px-0 underline ${colorByType[type]}`
            : minimal
              ? `border border-transparent ${disabled ? 'cursor-not-allowed' : minimalClassByType[type]}`
              : secondary
                ? `border ${disabled ? 'border-gray-200 text-gray-400 cursor-not-allowed' : secondaryClassByType[type]}`
                : tertiary
                    ? `border ${disabled ? 'border-gray-200 text-gray-400 cursor-not-allowed' : `${colorByType[type]} ${tertiaryClassByType[type]}`}`
                    : (disabled ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : primaryClassByType[type])}
      `}
      disabled={disabled}
      onClick={(e) => {
        if (typeof onClick === "function") {
          e.preventDefault()
          e.stopPropagation()
          onClick()
        }
      }}>
      {tooltipComponent}
      <div className={`pointer-events-none whitespace-nowrap h-full flex justify-center items-center
        ${iconPosition === 'right' ? 'flex-row-reverse' : ''}`}>
        {icon &&
          <Icon
            size={size}
            icon={icon}
            outlined={outlinedIcon}
            iconClassName={iconClassName}
            className={`
              ${children
                ? (iconPosition === 'right' ? 'ml-2' : 'mr-2')
                : ''}
              ${size === "small"
                ? "text-base"
                : (size === "large"
                  ? "text-2xl"
                  : "text-xl")
              }
            `}/>}
        {children}
      </div>
    </button>
  )
}

export default Button