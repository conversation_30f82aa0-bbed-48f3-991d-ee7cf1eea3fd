import React from 'react'
import FileUpload from '../file_upload'
import VideoThumbnail from './video_thumbnail'

const uploadDefaults = {
  icon: "movie",
  message: "Drag and drop or click here to upload",
  accept: "video/*",
  dropMessage: "Drop video here!",
  button: {
    text: "Upload a video",
  },
  publicURL: null,
  includeRemoveButton: true,
  onFileSelected: null,
  onProgress: (progress) => {},
  onComplete: () => {}
}

const buttonDefaults = {
  text: "Upload video"
}

const VideoUpload = ({
  upload = null,
  video = null,
  button,
  className = null,
}) =>
  video ?
    <VideoThumbnail {...video} className={className}/> :
    <FileUpload
      upload={{
        ...uploadDefaults,
        ...upload
      }}
      button={{
        ...buttonDefaults,
        ...button
      }}
      className={className}/>

export default VideoUpload