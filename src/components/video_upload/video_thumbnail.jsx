import React from 'react'
import Icon from "../icon"
import Status from "../status"

const VideoPlayer = ({
  name = null,
  backgroundImageURL = null,
  onClick = () => {},
  loading = false,
  encoding = false,
  error = false,
  errorMessage = "",
  className = ""
}) => {
  return (
    <div
      className={`${className} relative h-full w-full flex justify-center items-center bg-black group bg-center bg-no-repeat bg-cover cursor-pointer p-8`}
      style={{ backgroundImage: `url("${backgroundImageURL}")` }}
      onClick={onClick}>
      <div className='bg-black/20 absolute inset-0'/>
      {(encoding || error) ? 
        <Status dark error={error} errorMessage={errorMessage} pending={encoding} pendingMessage="Your file is encoding, check back later!"/>
        : loading
          ? <Status dark pending={loading}/>
          : <Icon icon="play_arrow" className="text-white z-10 text-6xl group-hover:text-7xl transition-all pointer-events-none"/>}
      {name
        ? <div className={`z-10 absolute bottom-0 left-0 right-0 font-semibold text-xs p-1 truncate text-white`}>
            {name}
          </div>
        : null}
    </div>
  )
}

export default VideoPlayer