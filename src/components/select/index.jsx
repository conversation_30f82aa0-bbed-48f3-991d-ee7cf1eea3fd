import React from 'react'
import ReactSelect, { components } from 'react-select'
import CreateableSelect from 'react-select/creatable'
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc'
import { ChevronDown } from "../icons"

function arrayMove(array, from, to) {
  array.splice(to < 0 ? array.length + to : to, 0, array.splice(from, 1)[0])
  return array
}

const multiStyles = {
  multiValue: (styles) => {
    return {
      ...styles,
      backgroundColor: '#E9F2FF',
    }
  },
  multiValueLabel: (styles) => ({
    ...styles,
    color: '#1551C5',
  }),
  multiValueRemove: (styles) => ({
    ...styles,
    color: '#1551C5',
    ':hover': {
      backgroundColor: '#B4CCFF',
    },
  }),
}

const borderByState = {
  enabled: (styles) => ({
    ...styles,
    border: '1px solid rgb(209 213 219)',
    ':hover': {
      border: '1px solid #575A71',
    }
  }),
  success: (styles) => ({
    ...styles,
    border: '1px solid #00B64C',
    ':hover': {
      border: '1px solid #00B64C',
    }
  }),
  error: (styles) => ({
    ...styles,
    border: '1px solid #FF4134',
    ':hover': {
      border: '1px solid #FF4134',
    }
  }),
  disabled: (styles) => ({
    ...styles,
    border: '1px solid #CCCCE1',
    backgroundColor: '#FBFBFE',
    ':hover': {
      border: '1px solid #CCCCE1',
    }
  })
}

const SortableMultiValue = SortableElement( props => {
    const onMouseDown = (e) => {
      e.preventDefault()
      e.stopPropagation()
    }
    const innerProps = { ...props.innerProps, onMouseDown }
    return <components.MultiValue {...props} innerProps={innerProps} className='cursor-pointer'/>
  }
)

const SortableMultiValueLabel = SortableHandle(
  (props) => <components.MultiValueLabel {...props} />
)

const SortableSelect = SortableContainer(ReactSelect)

const SortableCreateableSelect = SortableContainer(CreateableSelect)

const Select = ({
  id,
  options = [],
  value = null,
  placeholder = '',
  onChange = null,
  onCreateOption = null,
  label = '',
  description = '',
  message = '',
  disabled = false,
  success = false,
  error = false,
  width = 'w-full',
  icon = null,
  topIcon = null,
  bottomIcon = null,
  topIconPosition = 'right',
  bottomIconPosition = 'left',
  isClearable = false,
  isMulti = false,
  isCreatable = false,
  formatCreateLabel = value => `Create ${value}`,
  isSortable = false,
  hideDropdown = false,
  className = null,
}) => {

  const onSortEnd = ({ oldIndex, newIndex }) => {
    const newValue = arrayMove(value, oldIndex, newIndex)
    onChange(newValue)
  }

  const Icon = icon
  const TopIcon = topIcon
  const BottomIcon = bottomIcon
  const state = disabled
    ? 'disabled'
    : success
      ? 'success'
      : error
        ? 'error'
        : 'enabled'

  const placeholderStyles = {
    placeholder: (styles) => {
      return {
        ...styles,
        color: '#A5A5B9',
      }
    },
    valueContainer: (styles) => {
      return {
        ...styles,
        paddingLeft: icon ? 36 : 16,
        minHeight: '38px',
      }
    }
  }

  const customStyles = (isMulti || isCreatable)
    ? { ...placeholderStyles, ...multiStyles, control: borderByState[state]}
    : { ...placeholderStyles, control: borderByState[state] }

  const ValueContainer = ({ children, ...props }) => {
      return (
        components.ValueContainer && (
          <components.ValueContainer {...props}>
            {(!!children && icon) && (
              <Icon className={'absolute left-3 h-5 w-5 text-gray-400'}/>
            )}
            {children}
          </components.ValueContainer>
        )
      )
    }

  const DropdownIndicator = props => {
    return (
      components.DropdownIndicator && (
        <components.DropdownIndicator {...props} className='flex items-center'>
          <ChevronDown className={`h-5 w-5 text-gray-900 mr-1`}/>
        </components.DropdownIndicator>
      )
    )
  }

  const dropdownComponents = {
    ...hideDropdown
      ? { Menu: () => null, DropdownIndicator: () => null }
      : { DropdownIndicator },
    ...isSortable
      ? { MultiValue: SortableMultiValue, MultiValueLabel: SortableMultiValueLabel }
      : {},
    IndicatorSeparator: () => null,
    ValueContainer,
  }

  const dropdownTheme = theme => ({
    ...theme,
    borderRadius: 8
  })

  const commonOptions = {
    options,
    value,
    placeholder,
    onChange,
    isDisabled: disabled,
    isClearable,
    isMulti,
    isOptionDisabled: opt => opt.disabled,
    styles: customStyles,
    theme: dropdownTheme,
    components: dropdownComponents,
    ...isCreatable ? {
      onCreateOption,
      formatCreateLabel
    } : null,
    ...isSortable ? {
      useDragHandle: true,
      axis: "xy",
      onSortEnd
    } : null
  }

  return (

      <div className={`space-y-1 text-sm flex flex-col ${width} ${className}`}>
        {label &&
          <div className={`flex items-center text-xs leading-4 text-primary-900 dark:text-gray-200`}>
            <div className={`flex items-center ${topIcon && topIconPosition === 'right' && 'flex-row-reverse'}`}>
              {topIcon && <TopIcon className={`${topIconPosition === 'left' ? 'pr-1' : 'pl-1'} h-5 w-5`}/>}
              {label}
            </div>
          </div>
        }
        {description &&
          <div className={'text-xs leading-4 text-gray-500 dark:text-gray-300 flex items-center'}>
            {description}
          </div>
        }
        {(isCreatable && isSortable)
          ? <SortableCreateableSelect {...commonOptions} />
          : isCreatable
            ? <CreateableSelect {...commonOptions} />
            : isSortable
              ? <SortableSelect {...commonOptions} />
              : <ReactSelect {...commonOptions} />
        }
        {message &&
          <div className={`flex items-center text-xs leading-4 text-gray-500 dark:text-gray-300`}>
            <div className={`flex items-center ${bottomIconPosition === 'right' && 'flex-row-reverse'}`}>
              {bottomIcon && <BottomIcon className={`${bottomIconPosition === 'left' ? 'pr-1' : 'pl-1'} h-5 w-5`} />}
              {message}
            </div>
          </div>
        }
      </div>

  )
}

export default Select
