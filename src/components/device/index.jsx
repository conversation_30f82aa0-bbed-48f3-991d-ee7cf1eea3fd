import React, { useState } from 'react'
import Library from './library'
import Drives from './drives'
import Button from '../button'
import Settings from './settings'
import useIsMobile from '../hooks/use_is_mobile'

const defaultHeight = 136 // default height offset for cinesend-theme, overridable via property.

const Device = ({
  header = null,
  library = {},
  drives = [],
  downloads = [],
  className = '',
  topOffsetPx = defaultHeight,
  logo = null,
  allowedSettings = true,
  printLabel = true,
  headerOn = true,
  // Device settings
  settings = {},
  updateSettings = (data) => {
    Object.entries(data).map(([key, value]) => {
      window.alert(`About to set ${key} to ${value}`)
    })
  },
  toggleNetworkShare = (data) => {
    window.alert(`Toggling network share ${data.type} to ${data.value}`)
  },
  onReboot = () => {
    window.alert('This should trigger a reboot device job')
  },

  // Selected items
  selectedLibraryCPLs = [],
  setSelectedLibraryCPLs = () => {},
  selectedDriveCPLs = [],
  setSelectedDriveCPLs = () => {},

  // Library actions:
  onCopyFromLibraryToDrive = (cpls, drive) => {
    const cplIDs = cpls.map((cpl) => cpl.uuid)
    window.alert(`Copying ${cplIDs.join(',')} to ${drive.device}`)
  },
  onDeleteFromLibrary = (cpls) => {
    const cplIDs = cpls.map((cpl) => cpl.uuid)
    window.alert(`Deleting ${cplIDs.join(',')} from library`)
  },
  onLibraryColorChange = null,
  onCopyFromLibraryToCloud = null,
  onDeleteDownloadsFolder = (path) => {
    window.alert(`Trigger delete on ${path} from downloads folder`)
  },

  // Drive actions:
  onFormatDrive = (drive) => {
    window.alert(`Format drive: ${drive.location_name}`)
  },
  onPrintDriveLabel = (drive) => {
    window.alert(`Print a drive label for ${drive.device}`)
  },
  onValidateOnDrive = (cpls, drive) => {
    const cplIDs = cpls.map((cpl) => cpl.uuid)
    window.alert(`Validating ${cplIDs.join(',')} from ${drive.device}`)
  },
  onDeleteFromDrive = (cpls, drive) => {
    const cplIDs = cpls.map((cpl) => cpl.uuid)
    window.alert(`Deleting ${cplIDs.join(',')} from ${drive.device}`)
  },
  onCopyFromDriveToLibrary = (cpls, drive) => {
    const cplIDs = cpls.map((cpl) => cpl.uuid)
    window.alert(`Copying ${cplIDs.join(',')} from ${drive.device} to library`)
  },
  onCopyFromDriveToDrive = (cpls, fromDrive, toDrive) => {
    const cplIDs = cpls.map((cpl) => cpl.uuid)
    window.alert(
      `Copying ${cplIDs.join(',')} from ${fromDrive.device} to ${
        toDrive.device
      }`,
    )
  },
}) => {
  const [showSettings, setShowSettings] = useState(false)
  const [librarySelected, setLibrarySelected] = useState(true)
  const isMobile = useIsMobile(1201)
  return (
    <div className={`${className}`}>
      {(headerOn || allowedSettings) &&
        <div className="flex flex-wrap justify-between p-4">
          {headerOn ? header : null}
          {allowedSettings && !showSettings ? (
            <div className={'float-right'}>
              <Button
                icon="settings"
                onClick={() => setShowSettings(true)}
                tooltip={isMobile ? {
                  text: 'Settings',
                } : {}}>
                {!isMobile ? 'View Settings' : ''}
              </Button>
            </div>
          ) : null}
          {showSettings ? (
            <div className={`flex flex-wrap items-center space-x-2`}>
              <Button
                tertiary
                type="warning"
                icon="refresh"
                tooltip={isMobile ? {
                  text: 'Reboot Device',
                } : {}}
                onClick={() => {
                  if (
                    window.confirm('Are you sure you want to reboot this device?')
                  ) {
                    onReboot()
                  }
                }}
              >
                {!isMobile ? 'Reboot Device' : ''}
              </Button>
              <Button
                icon="storage"
                onClick={() => setShowSettings(false)}
                tooltip={isMobile ? {
                  text: 'Return to Device',
                } : {}}>
                {!isMobile ? 'Return to Device' : ''}
              </Button>
            </div>
          ) : null}
        </div>}
      {!showSettings && isMobile && drives.length ?
        <div className="grid grid-cols-2 gap-4 w-full h-10 items-center justify-between border-b">
          <div
            onClick={() => setLibrarySelected(true)}
            className={`${
              librarySelected
                ? 'border-b-4 border-primary-500 text-primary-500'
                : 'border-b-4 border-white cursor-pointer'
            } h-full flex items-center justify-center`}
          >
            <p>Library</p>
          </div>
          <div
            onClick={() => setLibrarySelected(false)}
            className={`${
              !librarySelected
                ? 'border-b-4 border-primary-500 text-primary-500'
                : 'border-b-4 border-white cursor-pointer'
            } h-full flex items-center justify-center`}
          >
            <p>Drives</p>
          </div>
        </div> : null}
      <div>
        {showSettings ? (
          <Settings
            settings={settings}
            updateSettings={updateSettings}
            toggleNetworkShare={toggleNetworkShare}
          />
        ) : (
          <div className="flex grow min-h-fit"
               style={{ height: `calc(100vh - ${isMobile ? topOffsetPx + 40 : topOffsetPx}px)`, 'minHeight': '400px' }}>
            <div className="flex w-full">
              {!isMobile || (isMobile && librarySelected) ? (
                <Library
                  cpls={library.cpls}
                  diskUsage={library.disk_usage}
                  drives={drives}
                  downloads={downloads}
                  onCopyToDrive={onCopyFromLibraryToDrive}
                  onColorChange={onLibraryColorChange}
                  onCopyToCloud={onCopyFromLibraryToCloud}
                  onDelete={onDeleteFromLibrary}
                  onDeleteDownloadsFolder={onDeleteDownloadsFolder}
                  selectedCPLs={selectedLibraryCPLs}
                  setSelectedCPLs={setSelectedLibraryCPLs}
                />
              ) : null}
              {drives && drives.length ? (
                !isMobile || (isMobile && !librarySelected) ? (
                  <Drives
                    printLabel={printLabel}
                    drives={drives}
                    logo={logo}
                    onCopyToLibrary={onCopyFromDriveToLibrary}
                    onCopyToDrive={onCopyFromDriveToDrive}
                    onDelete={onDeleteFromDrive}
                    onValidate={onValidateOnDrive}
                    onFormatDrive={onFormatDrive}
                    onPrintLabel={onPrintDriveLabel}
                    selectedCPLs={selectedDriveCPLs}
                    setSelectedCPLs={setSelectedDriveCPLs}
                  />
                ) : null
              ) : null}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Device
