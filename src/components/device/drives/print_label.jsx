import React from 'react'
import humanFileSize from "../../helpers/human_file_size"
import Icon from "../../icon"
import dayjs from 'dayjs'
import Button from '../../button'

function sort(list) {

  return list.sort((a, b) => {
    const titleA = a.title.toUpperCase(); // ignore upper and lowercase
    const titleB = b.title.toUpperCase(); // ignore upper and lowercase
    return (titleA < titleB) ? -1 : (titleA > titleB) ? 1 : 0;
  })

}

export default function PrintLabel({ title, drive, logo, includeDetails, includeLogo, includeDate, onClose }) {
  let cpls = []
  Object.values(drive.partitions).forEach(partition => {
    let partitionCPLs = partition.cpls ? Object.values(partition.cpls) : []
    cpls = [...cpls, ...partitionCPLs]
  })
  return (
    <div className="fixed inset-0 z-50 bg-white w-full overflow-y-scroll flex justify-center">
      <div className="absolute top-0 left-0 p-4 print:hidden">
        <Button size='small' tertiary icon='west' onClick={onClose}>Back</Button>
      </div>
      <div className='flex flex-col max-w-md w-full'>
        <div className={'flex flex-col my-4'}>
          {includeLogo ? <div className='flex w-full justify-center mb-4'>{logo}</div> : null}
          <div className={'text-[0.9rem] text-bold mx-auto'}>{title}</div>
          {includeDate && <div className={'text-[0.5rem] mx-auto'}>{dayjs().format('YYYY-MM-DD')}</div>}
        </div>
        <ul>
          {sort(cpls).map((cpl, index) =>
            <li className={'border-t-black border-t whitespace-nowrap font-black font-sans-serif'} key={index}>
              <div className={'flex flex-row justify-start'}>
                {cpl.is_encrypted && <Icon icon="lock" className="mt-1 w-4 text-sm text-gray-800"/>}
                <div className={'overflow-clip my-1'}>
                  <div className={'text-[0.5rem]'}>{cpl.title}</div>
                  {includeDetails && <div className={'text-[0.5rem]'}>{humanFileSize(cpl.size)} | {cpl.timecode}&nbsp;
                    | {cpl.kind} | {cpl.frame_rate}fps | {cpl.width}x{cpl.height}</div>}
                </div>
              </div>
            </li>
          )}
        </ul>
      </div>
    </div>
  )
}