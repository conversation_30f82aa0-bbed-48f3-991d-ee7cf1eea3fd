import React from 'react'
import sort from '../../helpers/sort'
import CPL from '../cpl'

const CPLs = ({
  cpls,
  drives,
  drive,
  onCopyToDrive = () => {},
  onCopyToLibrary = () => {},
  onValidate = () => {},
  onDelete = () => {},
  selectedCPLs,
  setSelectedCPLs
}) => {
  const selectedIDs = selectedCPLs.map(cpl => cpl.uuid)
  const list = sort(Object.values(cpls), 'title', 'ASC')
  return (
    <div className='flex flex-col space-y-1'>
      {list.map((cpl, index) =>
        <CPL
          {...cpl}
          key={index}
          isSelected={selectedIDs.includes(cpl.uuid)}
          onCheckboxToggle={() => setSelectedCPLs(
            selectedIDs.includes(cpl.uuid)
              ? selectedCPLs.filter(selectedCPL => selectedCPL.uuid !== cpl.uuid)
              : [...selectedCPLs, cpl]
          )}
          dropdownActions={[
            {
              text: 'Copy to Local Library',
              icon: 'arrow_backward',
              onClick: () => onCopyToLibrary([cpl], drive)
            },
            ...drives.filter(d => d.device !== drive.device).map((destinationDrive, index) => ({
              text: `Copy to ${destinationDrive.location_name}`,
              icon: 'arrow_forward',
              breakAfter: index + 1 === drives.length - 1,
              onClick: () => onCopyToDrive([cpl], drive, destinationDrive)
            })),
            {
              text: 'Validate DCP',
              icon: 'check_circle_outline',
              onClick: () => onValidate([cpl], drive),
              breakAfter: true,
              hide: cpl.is_valid
            },
            {
              text: 'Delete',
              className: "text-red-600",
              icon: 'delete_forever',
              onClick: () => {
                if (window.confirm("Are you sure you want to delete this DCP?")) {
                  onDelete([cpl], drive)
                }
              }
            }
          ].filter(opt => !opt.hide)}
        />)}
    </div>
  )
}

export default CPLs
