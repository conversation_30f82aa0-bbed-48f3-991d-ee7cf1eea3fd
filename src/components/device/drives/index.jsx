import React, { useState } from 'react'
import Header from './header'
import Drive from './drive'
import Footer from '../footer'
import Button from '../../button'
import PrintLabelButton from './print_label_button'

const Drives = ({
  drives = [],
  selectedCPLs = [],
  setSelectedCPLs = () => {},
  onFormatDrive = () => {},
  printLabel = true,
  ...props
}) => {
  const [activeDriveIndex, setActiveDriveIndex] = useState(0)
  const activeDrive = drives[activeDriveIndex]

  const activeButtons = [
    <Button key='format-drive' size='small' tertiary type='neutral' onClick={() => onFormatDrive(activeDrive)}>Format</Button>
  ]
  if (printLabel) {
    activeButtons.push(<PrintLabelButton key='print-label' drive={activeDrive} {...props}/>)
  }
  return (
    <div className='w-full flex flex-col h-full'>
      <Header
        drives={drives}
        activeDrive={activeDrive}
        setActiveDriveIndex={setActiveDriveIndex}
        selectedCPLs={selectedCPLs}
        {...props}/>
      {activeDrive &&
        <Drive
          drives={drives}
          drive={activeDrive}
          selectedCPLs={selectedCPLs}
          setSelectedCPLs={setSelectedCPLs}
          {...props}/>}
      <Footer
        count={activeDrive.partitions
          ? Object.values(activeDrive.partitions)
            .reduce((total, partition) => total + (partition.cpls ? Object.keys(partition.cpls).length : 0), 0)
          : 0}
        title={`${activeDrive.location_name} Storage`}
        diskUsed={activeDrive.used}
        diskSize={activeDrive.size}
        buttons={activeButtons}/>

    </div>
  )
}

export default Drives
