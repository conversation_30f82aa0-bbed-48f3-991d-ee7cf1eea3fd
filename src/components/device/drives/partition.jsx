import React from 'react'
import NoneFound from '../none_found'
import CPLs from './cpls'
import Tag from '../../tag'

const Partition = ({
  index,
  mount,
  partition,
  ...props
}) =>
  <div key={mount} className='py-2 flex flex-col space-y-2 '>
    <div className='flex items-center justify-between'>
      <div className='text-gray-600 text-xs font-normal'>
        PARTITION {index + 1} {partition.fs_type ? `(${partition.fs_type})` : ''}
      </div>
      {partition.format_required && <Tag type='warning' outline label='FORMATTING REQUIRED' size='small'/>}
    </div>
    {partition.cpls && Object.keys(partition.cpls).length > 0 ?
      <CPLs {...props} cpls={partition.cpls} partition={partition}/> :
      <NoneFound/>}
  </div>

export default Partition
