import React from 'react'
import ReactTooltip from 'react-tooltip'
import uuid from "react-uuid"
import Icon from '../../icon'

const DriveName = ({ index, drive, activeDrive, setActiveDriveIndex }) => {
  const tooltipID = uuid()
  return (
    <div
      key={drive.device}
      data-tip data-for={tooltipID}
      className={`flex pt-4 pb-4 px-2 text-sm
        ${activeDrive.device === drive.device
          ? 'border-b-4 border-primary-500 text-primary-500'
          : 'border-b-4 border-white cursor-pointer'}`}
      onClick={() => setActiveDriveIndex(index)}>
      {(drive.is_formatting || drive.has_formatting_job)
        ? <Icon icon='refresh' className='text-warning-500 animate-spin-slow text-sm'/>
        : drive.is_busy
        ? <Icon icon='circle' className='text-warning-500 animate-pulse text-sm'/>
        : <Icon icon='circle' className='text-success-500 text-sm'/>}
      <div className={'ml-2'}>{drive.location_name}</div>
      {(drive.is_formatting || drive.has_formatting_job || drive.is_busy) && <ReactTooltip id={tooltipID} effect='solid' place={'bottom'}>
        {drive.is_formatting ? 'Formatting' : drive.has_formatting_job ? 'Formatting triggered' : 'Busy'}
      </ReactTooltip>}
    </div>
  )
}

export default DriveName
