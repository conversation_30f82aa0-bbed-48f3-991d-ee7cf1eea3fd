import React from 'react'
import ButtonDropdown from '../../button_dropdown'
import Button from '../../button'
import DriveName from './drive_name'

const Actions = ({
  selectedCPLs = [],
  drives = [],
  activeDrive = null,
  onCopyToLibrary = () => {},
  onCopyToDrive = () => {},
  onValidate = () => {},
  onDelete = () => {}
}) =>
  <div className='w-full flex items-center h-full'>
    <DriveName drive={activeDrive} activeDrive={activeDrive}/>
    <div className='pl-2 flex flex-wrap items-center space-x-2'>
      <ButtonDropdown
        button={{
          size: 'small',
          text: `Copy ${selectedCPLs.length} DCP${selectedCPLs.length === 1 ? '' : 's'}`
        }}
        dropdown={{
          content: [
            {
              text: 'Copy to Local Library',
              icon: 'arrow_backward',
              onClick: () => onCopyToLibrary(selectedCPLs, activeDrive)
            },
            ...drives.filter(drive => drive.device !== activeDrive.device).map(destinationDrive => ({
              text: `Copy to ${destinationDrive.location_name}`,
              icon: 'arrow_forward',
              onClick: () => onCopyToDrive(selectedCPLs, activeDrive, destinationDrive)
            }))
          ]
        }}/>
      <Button
        secondary
        size='small'
        onClick={() => onValidate(selectedCPLs, activeDrive)}>
        Validate {selectedCPLs.length} DCP{selectedCPLs.length === 1 ? '' : 's'}
      </Button>
      <Button
        type='error'
        tertiary
        size='small'
        onClick={() => {
          if (window.confirm(`Are you sure you want to delete ${selectedCPLs.length} DCP${selectedCPLs.length === 1 ? '' : 's'}?`)) {
            onDelete(selectedCPLs, activeDrive)
          }
        }}>
        Delete {selectedCPLs.length} DCP{selectedCPLs.length === 1 ? '' : 's'}
      </Button>
    </div>
  </div>

export default Actions
