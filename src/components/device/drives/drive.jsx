import React from 'react'
import Partition from './partition'
import Formatting from './formatting'

const Drive = ({
  drive,
  onFormatDrive,
  ...props
}) =>
    <div className='bg-gray-50 px-4 grow overflow-y-auto'>
      {(drive.is_formatting || drive.has_formatting_job)
        ? <Formatting drive={drive}/>
        : drive.partitions && Object.entries(drive.partitions).map(([mount, partition], index) =>
            <Partition index={index} key={index} drive={drive} mount={mount} partition={partition} {...props} />
          )
      }
  </div>

export default Drive
