import React from 'react'
import Button from '../../button'
import Toggle from '../../toggle'
import Input from '../../input'
import PrintLabel from './print_label'
import { useState } from 'react'
import Modal from '../../modal'

const PrintLabelButton = ({ drive, logo }) => {
  const [showPrintModal, setShowPrintModal] = useState(false)
  const [showPrintLabel, setShowPrintLabel] = useState(false)
  const [options, setOptions] = useState({ includeLogo: true, includeDate: true, includeDetails: true })
  const [name, setName] = useState("")
  return (
    <>
      <Button key='print-label' size='small' secondary onClick={() => setShowPrintModal(true)}>Print Label</Button>
      {showPrintModal && <Modal
        header='Print Label Options'
        className='w-full'
        minWidth={false}
        onClose={() => setShowPrintModal(false)}
        confirmButton={{
          text: 'Print Label',
          onClick: () => {
            setShowPrintModal(false)
            setShowPrintLabel(true)
            setTimeout(() => {
              // window.print()
            }, 500)
          }
        }}>
          <div className='flex flex-col max-w-screen space-y-2'>
            <Input
              label='Name of Drive'
              value={name}
              onChange={e => setName(e.target.value)}/>
            <Toggle
              size='small'
              label='Include Logo'
              checked={options.includeLogo}
              onChange={() => setOptions({ ...options, includeLogo: !options.includeLogo })}/>
            <Toggle
              size='small'
              label='Include Drive Date'
              checked={options.includeDate}
              onChange={() => setOptions({ ...options, includeDate: !options.includeDate })}/>
            <Toggle
              size='small'
              label='Include CPL Details'
              checked={options.includeDetails}
              onChange={() => setOptions({ ...options, includeDetails: !options.includeDetails })}/>
          </div>
        </Modal>}
      {showPrintLabel && <PrintLabel
        title={name}
        drive={drive}
        logo={logo}
        includeDate={options.includeDate}
        includeLogo={options.includeLogo}
        includeDetails={options.includeDetails}
        onClose={() => setShowPrintLabel(false)}/>}
    </>
  )
}

export default PrintLabelButton