import React from 'react'
import Icon from '../../icon'

const Formatting = ({ drive }) => {
  const header = drive.is_formatting ? 'Formatting' : 'Formatting triggered'
  const description = drive.is_formatting
    ? 'This drive is currently formatting.'
    : 'A formatting job was triggered on this drive but has not been picked up yet.'
  return (
    <div className='my-4 border bg-white w-full justify-center items-center flex flex-col space-y-2 text-gray-600
      min-h-[128px]'>
      <Icon icon='refresh' className='text-gray-800 animate-spin-slow'/>
      <span className='font-subheader text-sm'>{header}</span>
      <span className='text-xs'>{description}</span>
    </div>
  )
}

export default Formatting
