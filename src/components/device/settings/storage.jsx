import React from 'react'
import { ProgressBar, Table, Tag } from "../.."
import humanFileSize from "../../helpers/human_file_size"

const diskKeys = ['location', 'storage_pool', 'estimated_lifespan', 'bad_record_count', 'model', 'serial_number', 'firmware_version', 'temperature', 'health_status']

export default function Storage({ settings }) {
  return (
    <div className='flex flex-col space-y-4'>
      <div className='border rounded p-4 space-y-4'>
        <div className='text-base font-semibold'>Storage Pool</div>
        <div>
          <div className='flex items-center space-x-2'>
            <div className='font-semibold'>Media Cache</div>
            <Tag type={settings.health_status === 'Normal' ? 'success' : 'error'} label={settings.health_status}/>
          </div>
          <div className='text-xs'>{settings.raid_name}, {settings.format_type}</div>
        </div>
        <div>
          <div className='font-semibold'>Storage Used</div>
          <ProgressBar completed={settings.storage.used / settings.storage.total * 100}/>
          <div className='text-xs'>{humanFileSize(settings.storage.used)} / {humanFileSize(settings.storage.total)}</div>
        </div>
      </div>
      <div className='border rounded p-4 space-y-4'>
        <div className='text-base font-semibold'>SDD / HDD</div>
        <Table
          widths={[100, 100, 200, 100]}
          header={{
            columns: [{ text: 'Name' }, { text: 'Status' }, { text: 'Model' }, { text: 'Size' }]
          }}
          body={{
            data: settings.disks,
            row: {
              compact: true,
              render: [
                data => data.title,
                data => <Tag type={data.health_status === 'Normal' ? 'success' : 'error'} label={data.health_status}/>,
                data => data.model,
                data => humanFileSize(data.size)
              ],
              expandableComponent: (data) => <div className='flex flex-col divide-y p-4 text-xs'>
                {diskKeys.map(property => <div key={property} className='flex items-center justify-between'>
                  <div className='capitalize'>{property.replaceAll('_', ' ')}</div>
                  <div className='font-semibold'>{data[property]}</div>
                </div>)}
              </div>
            }
          }}/>
      </div>
    </div>
  )
}