import React, { useState } from "react"
import { Button, Input, Select, Toggle } from "../.."
import timezoneOptions from "./timezone_options"
import timeOptions from "./time_options"

export default function System({ settings, updateSettings }) {
  const [targetRate, setTargetRate] = useState(settings.target_rate_in_mbps)
  const [startTime, setStartTime] = useState(settings.download_window_start_time)
  const [endTime, setEndTime] = useState(settings.download_window_end_time)
  const [timezone, setTimezone] = useState(settings.download_window_tz)
  return (
    <div className='flex flex-col divide-y'>
      <div className='flex-col space-y-4 py-4'>
        <h5>Aspera Bandwidth</h5>
        <Toggle
          label='Limit Aspera bandwith?'
          size='small'
          checked={settings.target_rate_in_mbps !== null}
          onChange={() => updateSettings({ target_rate_in_mbps: null})}/>
        <Input
          className='w-48'
          value={targetRate}
          onChange={e => setTargetRate(e.target.value)}
          onBlur={() => updateSettings({ target_rate_in_mbps: targetRate })}
          label='Download rate in mbps'/>
      </div>
      <div className='flex-col space-y-4 py-4'>
        <h5>Download Window</h5>
        <div className='flex items-center space-x-2'>
          <Select
            options={timeOptions}
            label='Start time'
            value={timeOptions.find(opt => opt.value === startTime)}
            onChange={opt => setStartTime(opt.value)}
          />
          <Select
            options={timeOptions}
            label='End time'
            value={timeOptions.find(opt => opt.value === endTime)}
            onChange={opt => setEndTime(opt.value)}
          />
          <Select
            options={timezoneOptions}
            label='Timezone'
            value={timezoneOptions.find(opt => opt.value === timezone)}
            onChange={opt => setTimezone(opt.value)}
          />
        </div>
        <div className='flex space-x-2 justify-between'>
          <Button
            size='small'
            secondary
            onClick={() => {
              setStartTime(null)
              setEndTime(null)
              setTimezone(null)
              updateSettings({
                download_window_start_time: null,
                download_window_end_time: null,
                download_window_tz: null
              })
            }}>
            Clear
          </Button>
          <Button
            size='small'
            onClick={() => {
              updateSettings({
                download_window_start_time: startTime,
                download_window_end_time: endTime,
                download_window_tz: timezone
              })
            }}>
            Save
          </Button>
        </div>
      </div>
    </div>
  )
}