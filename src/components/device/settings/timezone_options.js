const timezoneOptions = [
  { key: "GMT", name: "Greenwich Mean Time", rel: "GMT+00:00" },
  { key: "ECT", name: "European Central Time", rel: "GMT+1:00" },
  { key: "EET", name: "Eastern European Time", rel: "GMT+2:00" },
  { key: "ART", name: "(Arabic) Egypt Standard Time", rel: "GMT+2:00" },
  { key: "EAT", name: "Eastern African Time", rel: "GMT+3:00" },
  { key: "MET", name: "Middle East Time", rel: "GMT+3:30" },
  { key: "NET", name: "Near East Time", rel: "GMT+4:00" },
  { key: "PLT", name: "Pakistan Lahore Time", rel: "GMT+5:00" },
  { key: "IST", name: "India Standard Time", rel: "GMT+5:30" },
  { key: "BST", name: "Bangladesh Standard Time", rel: "GMT+6:00" },
  { key: "VST", name: "Vietnam Standard Time", rel: "GMT+7:00" },
  { key: "CTT", name: "China Taiwan Time", rel: "GMT+8:00" },
  { key: "JST", name: "Japan Standard Time", rel: "GMT+9:00" },
  { key: "ACT", name: "Australia Central Time", rel: "GMT+9:30" },
  { key: "AET", name: "Australia Eastern Time", rel: "GMT+10:00" },
  { key: "SST", name: "Solomon Standard Time", rel: "GMT+11:00" },
  { key: "NST", name: "New Zealand Standard Time", rel: "GMT+12:00" },
  { key: "MIT", name: "Midway Islands Time", rel: "GMT-11:00" },
  { key: "HST", name: "Hawaii Standard Time", rel: "GMT-10:00" },
  { key: "AST", name: "Alaska Standard Time", rel: "GMT-9:00" },
  { key: "PST", name: "Pacific Standard Time", rel: "GMT-8:00" },
  { key: "PNT", name: "Phoenix Standard Time", rel: "GMT-7:00" },
  { key: "MST", name: "Mountain Standard Time", rel: "GMT-7:00" },
  { key: "CST", name: "Central Standard Time", rel: "GMT-6:00" },
  { key: "EST", name: "Eastern Standard Time", rel: "GMT-5:00" },
  { key: "IET", name: "Indiana Eastern Standard Time", rel: "GMT-5:00" },
  { key: "PRT", name: "Puerto Rico and US Virgin Islands Time", rel: "GMT-4:00" },
  { key: "CNT", name: "Canada Newfoundland Time", rel: "GMT-3:30" },
  { key: "AGT", name: "Argentina Standard Time", rel: "GMT-3:00" },
  { key: "BET", name: "Brazil Eastern Time", rel: "GMT-3:00" },
  { key: "CAT", name: "Central African Time", rel: "GMT-1:00" }
].map(opt => ({ label: `${opt.key} (${opt.rel})`, value: opt.key }))

export default timezoneOptions
