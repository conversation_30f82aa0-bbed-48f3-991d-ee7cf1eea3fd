import React, { useEffect, useState } from "react"
import { Input } from "../.."
import Button from "../../button"
import Checkbox from "../../checkbox"
import Table from "../../table"

export default function Emails({ settings, updateSettings }) {
  const emptyEmailObject = {
    email: "",
    download_complete: false,
    download_started: false,
    hard_drive_failure: false,
    device_offline: false,
  }

  const [emails, setEmails] = useState(() =>
    settings?.length !== 0 ? settings : [emptyEmailObject]
  )

  const updateEmails = (index, key, value) => {
    setEmails((prevEmails) =>
      prevEmails.map((email, i) =>
        i === index ? { ...email, [key]: value } : email
      )
    )
  }

  useEffect(() => {
    if (settings?.length) {
      setEmails(settings)
    }
  }, [settings])

  return (
    <div className="space-y-4">
      <div>… to receive notifications and alerts.</div>
      <Table
        widths={[200, 100, 100, 100, 100, 80]}
        header={{
          columns: [
            { text: "Email" },
            { text: "New Content Available" },
            { text: "New Download Started" },
            { text: "When a hard drive fails" },
            { text: "When your device goes offline" },
            { text: "" },
          ],
        }}
        body={{
          data: emails,
          row: {
            render: [
              (data, index) => (
                <Input
                  value={data.email}
                  onChange={(e) => updateEmails(index, "email", e.target.value)}
                />
              ),
              (data, index) => (
                <div className="flex w-full justify-center items-center">
                  {" "}
                  <Checkbox
                    checked={data.download_complete}
                    onChange={(e) =>
                      updateEmails(index, "download_complete", e.target.checked)
                    }
                  />
                </div>
              ),
              (data, index) => (
                <div className="flex w-full justify-center items-center">
                  {" "}
                  <Checkbox
                    checked={data.download_started}
                    onChange={(e) =>
                      updateEmails(index, "download_started", e.target.checked)
                    }
                  />
                </div>
              ),
              (data, index) => (
                <div className="flex w-full justify-center items-center">
                  {" "}
                  <Checkbox
                    checked={data.hard_drive_failure}
                    onChange={(e) =>
                      updateEmails(
                        index,
                        "hard_drive_failure",
                        e.target.checked
                      )
                    }
                  />
                </div>
              ),
              (data, index) => (
                <div className="flex w-full justify-center items-center">
                  {" "}
                  <Checkbox
                    checked={data.device_offline}
                    onChange={(e) =>
                      updateEmails(index, "device_offline", e.target.checked)
                    }
                  />
                </div>
              ),
              (data, index) => (
                <Button
                  minimal
                  type="error"
                  icon="delete"
                  onClick={() =>
                    setEmails(emails.filter((email, i) => index !== i))
                  }
                />
              ),
            ],
          },
        }}
      />
      <div className="flex items-center justify-between">
        <Button
          size="small"
          secondary
          onClick={() => {
            setEmails([...emails, emptyEmailObject])
          }}
        >
          Add New Email
        </Button>
        <Button
          size="small"
          onClick={() => updateSettings({ email_notifications: emails })}
        >
          Save
        </Button>
      </div>
    </div>
  )
}
