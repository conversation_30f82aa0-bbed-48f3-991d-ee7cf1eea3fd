import React, { useState } from "react"
import Emails from "./emails"
import Network from "./network"
import Storage from "./storage"
import System from "./system"

export default function Settings({ settings, updateSettings, toggleNetworkShare }) {

  const [activeTab, setActiveTab] = useState(0)
  const tabs = [
    // {
    //   name: 'Device Overview',
    //   component: null
    // },
    {
      name: 'System Settings',
      component: <System settings={settings.system} updateSettings={updateSettings}/>
    },
    {
      name: 'Network Shares',
      component: <Network settings={settings.network} toggleNetworkShare={toggleNetworkShare}/>
    },
    {
      name: 'Email Notifications',
      component: <Emails settings={settings.email_notifications} updateSettings={updateSettings}/>
    },
    {
      name: 'Storage Settings',
      component: <Storage settings={settings.storage}/>
    },
    // {
    //   name: 'Trusted Device List',
    //   component: null
    // }
  ]

  return (
    <div className='text-sm'>
      <div className='flex flex-wrap items-center space-x-2 border-y'>
        {
          tabs.map((tab, index) =>
            <div
              key={index}
              onClick={() => setActiveTab(index)}
              className={`p-4 border-b-2
                ${activeTab === index
                  ? 'border-b-4 border-primary-500 text-primary-500'
                  : 'border-b-4 border-white cursor-pointer hover:text-primary-500 hover:border-primary-500'}`}>
              {tab.name}
            </div>)
        }
      </div>
      <div className='p-4 h-full max-w-3xl'>{tabs[activeTab].component}</div>
    </div>
  )
}