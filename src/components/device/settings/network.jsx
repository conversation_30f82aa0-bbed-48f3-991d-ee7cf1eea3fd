import React from 'react'
import <PERSON>ton from "../../button"

export default function Network({
  settings,
  toggleNetworkShare = () => {}
}) {

  const Connection = ({ name, host, username, password }) => <div className='flex w-full border rounded p-4 bg-white'>
    <div className='flex w-full flex-col space-y-2'>
      <div className='font-semibold'>{name}</div>
      <div className='flex items-center justify-between'>
        <div>Connect To:</div>
        <div className='font-semibold'>{host}</div>
      </div>
      <div className='flex items-center justify-between'>
        <div>Username:</div>
        <div className='font-semibold'>{username}</div>
      </div>
      <div className='flex items-center justify-between'>
        <div>Password:</div>
        <div className='font-semibold'>{password}</div>
      </div>
    </div>
  </div>

  return (
    <div className='flex flex-wrap flex-col space-y-2'>
      <div className='font-semibold'>IP Address: {settings.ip_address}</div>
      {settings.ftp &&
        <div className='border rounded p-4 space-y-2 bg-primary-100'>
          <div className='flex items-center justify-between'>
            <div className='text-base font-semibold'>FTP Network Share</div>
            <Button size='small' onClick={() => toggleNetworkShare({ type: 'ftp', value: !settings.ftp.active})}>
              {settings.ftp.active ? 'Disable FTP Sharing' : 'Enable FTP Sharing'}
            </Button>
          </div>
          {settings.ftp.active && <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
            <Connection
              name='Import'
              host={settings.ftp.shares.ingest.connection}
              username={settings.ftp.shares.ingest.username}
              password={settings.ftp.shares.ingest.password}/>
            <Connection
              name='Export'
              host={settings.ftp.shares.export.connection}
              username={settings.ftp.shares.export.username}
              password={settings.ftp.shares.export.password}/>
          </div>}
        </div>
      }
      {settings.samba &&
        <div className='border rounded p-4 space-y-2 bg-primary-100'>
          <div className='flex items-center flex-wrap justify-between'>
            <div className='text-base font-semibold'>Samba Network Share</div>
            <Button size='small' onClick={() => toggleNetworkShare({ type: 'samba', value: !settings.samba.active})}>
              {settings.samba.active ? 'Disable Samba Sharing' : 'Enable Samba Sharing'}
            </Button>
          </div>
          {settings.samba.active && <div className='flex space-x-2'>
            <Connection
              name='Import'
              host={settings.samba.shares.ingest.connection}
              username={settings.samba.shares.ingest.username}
              password={settings.samba.shares.ingest.password}/>
            <Connection
              name='Export'
              host={settings.samba.shares.export.connection}
              username={settings.samba.shares.export.username}
              password={settings.samba.shares.export.password}/>
          </div>}
        </div>
      }
    </div>
  )
}