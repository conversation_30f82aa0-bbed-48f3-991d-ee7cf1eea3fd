import React, { useState } from 'react'
import Header from "./header"
import Footer from '../footer'
import CPLList from './cpl_list'
import DownloadsList from './downloads_list'

const Library = ({
  cpls = [],
  diskUsage,
  drives = [],
  downloads = [],
  onCopyToDrive = () => {},
  onDelete = () => {},
  selectedCPLs = [],
  setSelectedCPLs = () => {},
  ...props
}) => {
  const [groupByKind, setGroupByKind] = useState(false)
  const [showStrandedAssets, setShowStrandedAssets] = useState(false)
  const [search, setSearch] = useState("")
  const [sorting, setSorting] = useState({ direction: 'ASC', key: "title" })
  const [filters, setFilters] = useState(null)
  const selectedIDs = selectedCPLs.map(cpl => cpl.uuid)

  return (
    <div className={`w-full flex flex-col h-full overflow-auto`}>
      <Header
        search={search}
        onSearchChange={setSearch}
        filters={filters}
        onFiltersChange={setFilters}
        sorting={sorting}
        onSortChange={setSorting}
        groupByKind={groupByKind}
        toggleGroupByKind={() => setGroupByKind(!groupByKind)}
        selectedCPLs={selectedCPLs}
        drives={drives}
        onCopyToDrive={onCopyToDrive}
        onDelete={onDelete}
        includeButtons={!showStrandedAssets}
        {...props}/>
      {!showStrandedAssets ?
        <CPLList
          cpls={cpls}
          drives={drives}
          selectedCPLs={selectedCPLs}
          setSelectedCPLs={setSelectedCPLs}
          selectedIDs={selectedIDs}
          groupByKind={groupByKind}
          search={search}
          sorting={sorting}
          filters={filters}
          onCopyToDrive={onCopyToDrive}
          onDelete={onDelete}
          {...props}/> :
        <DownloadsList
          downloads={downloads}
          drives={drives}
          search={search}
          {...props}/>}
      <Footer
        isLibrary
        title='Internal Storage'
        count={cpls.length}
        diskUsed={diskUsage.size - diskUsage.free}
        diskSize={diskUsage.size}
        showStrandedAssets={showStrandedAssets}
        toggleShowStrandedAssets={() => setShowStrandedAssets(!showStrandedAssets)}
        {...props}/>
    </div>
  )
}

export default Library
