import React from 'react'
import CPL from "../cpl"
import COLORS from '../colors'
import Icon from '../../icon'

const LibraryCPL = ({
  cpl,
  selectedIDs = [],
  selectedCPLs = [],
  setSelectedCPLs = () => {},
  drives = [],
  onCopyToDrive = () => {},
  onCopyToCloud = null,
  onColorChange = null,
  onDelete = () => {},
}) => {
  return (
    <CPL
      {...cpl}
      key={cpl.uuid}
      isSelected={selectedIDs.includes(cpl.uuid)}
      onCheckboxToggle={() => {
        setSelectedCPLs(
          selectedIDs.includes(cpl.uuid)
            ? selectedCPLs.filter(selectedCPL => selectedCPL.uuid !== cpl.uuid)
            : [...selectedCPLs, cpl]
        )}
      }
      dropdownActions={[
        ...drives.map((drive, index) => ({
          text: `Copy to ${drive.location_name}`,
          icon: 'arrow_forward',
          onClick: () => onCopyToDrive([cpl], drive),
          disabled: drive.is_formatting,
          breakAfter: index + 1 === drives.length
        })),
        {
          text: 'Upload to Cloud',
          icon: 'cloud_upload',
          onClick: () => onCopyToCloud([cpl]),
          breakAfter: true,
          hide: typeof onCopyToCloud !== "function"
        },
        {
          component: <div className='p-4 flex items-center space-x-1'>
            {COLORS.map(color =>
              <div
                key={color}
                className='cursor-pointer'
                onClick={e => {
                  e.preventDefault()
                  e.stopPropagation()
                  onColorChange(cpl, color)
                }}>
              <Icon style={{ color }} icon='circle'/>
            </div>
            )}
          </div>,
          breakAfter: true,
          hide: typeof onColorChange !== "function"
        },
        {
          text: 'Delete',
          className: "text-red-600",
          icon: 'delete_forever',
          onClick: () => {
            if (window.confirm("Are you sure you want to delete this DCP?")) {
              onDelete([cpl])
            }
          }
        }
      ].filter(opt => !opt.hide)}/>
  )
}

export default LibraryCPL