import React from "react"
import Input from "../../input"
import { Search } from "../../icons"
import FilterButton from "../../filter_button"
import SortButton from "../../sort_button"
import COLORS from "../colors"
import ButtonDropdown from "../../button_dropdown"
import useIsMobile from "../../hooks/use_is_mobile"

const Filters = ({
  search = "",
  onSearchChange,
  filters,
  onFiltersChange,
  sorting,
  onSortChange,
  groupByKind,
  toggleGroupByKind,
  includeButtons = true,
  onColorChange = null,
}) => {
  const isMobile = useIsMobile()
  return (
    <>
      <Input
        input={{
          leftIcon: {
            icon: Search,
          },
        }}
        placeholder="Search..."
        value={search}
        onChange={(e) => onSearchChange(e.target.value)}
      />
      {includeButtons ? (
        <>
          <FilterButton
            options={[
              {
                label: "Filter by Content Kind",
                key: "kind",
                type: "checkboxes",
                options: [
                  { key: "feature", label: "Feature" },
                  { key: "trailer", label: "Trailer / Teaser" },
                  { key: "short", label: "Short" },
                  { key: "advertisement", label: "Advertisement" },
                  { key: "transitional", label: "Transitional" },
                  { key: "policy", label: "Policy" },
                  { key: "test", label: "Test" },
                  { key: "other", label: "Other" },
                ],
              },
              {
                label: "Filter by Color",
                key: "color",
                type: "color_boxes",
                options: COLORS,
                hide: typeof onColorChange !== "function",
              },
            ].filter((opt) => !opt.hide)}
            filters={filters}
            onFiltersChange={onFiltersChange}
          />
          <SortButton
            sortingKey={sorting.key}
            direction={sorting.direction}
            onSortChange={onSortChange}
            options={[
              { key: "title", label: "Title" },
              { key: "size", label: "Size" },
              { key: "timecode", label: "Duration " },
            ]}
          />
          <ButtonDropdown
            button={{
              text: isMobile ? "" : "Group",
              icon: "segment",
              minimal: !groupByKind,
              secondary: groupByKind,
              type: groupByKind ? "primary" : "neutral",
            }}
            dropdown={{
              content: [
                {
                  text: groupByKind ? "Remove group by kind" : "Group by kind",
                  icon: groupByKind
                    ? "remove_circle_outline"
                    : "check_circle_outline",
                  onClick: () => toggleGroupByKind(),
                },
              ],
            }}
          />
        </>
      ) : null}
    </>
  )
}
export default Filters
