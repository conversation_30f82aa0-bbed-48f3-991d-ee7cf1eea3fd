import React from 'react'
import NoneFound from "../none_found"
import Download from "./download"

export default function DownloadsList({
  downloads = {},
  search,
  height,
  drives,
  onDeleteDownloadsFolder,
  ...props
}) {
  const list = downloads && downloads.children ? downloads.children.filter(download => {
    if (search) {
      return download.name.toLowerCase().includes(search.toLowerCase())
    }
    return true
  }) : []
  return (
    <div
      className={`overflow-auto bg-gray-50 px-4 py-2 space-y-1 ${drives.length ? 'border-r' : ''}`}
      style={{ height }}>
      {list.map(download => 
        <Download 
          download={download}
          onDeleteFolder={onDeleteDownloadsFolder}
          {...props}/>
      )}
      {list.length === 0
        && <NoneFound header='No stranded assets' description='There are no stranded assets in your downloads folder.'/>}
    </div>
  )
}