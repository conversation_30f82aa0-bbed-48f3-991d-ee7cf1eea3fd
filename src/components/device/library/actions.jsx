import React from 'react'
import Button<PERSON>ropdown from '../../button_dropdown'
import Button from '../../button'

const Actions = ({
  selectedCPLs = [],
  drives = [],
  onCopyToDrive = () => {},
  onDelete = () => {}
}) =>
  <div className='w-full flex items-center justify-end space-x-2 py-1'>
    {drives && drives.length ? <ButtonDropdown
      button={{
        size: 'small',
        text: `Copy ${selectedCPLs.length} DCP${selectedCPLs.length === 1 ? '' : 's'}`
      }}
      dropdown={{
        content: drives.map(drive => ({
          text: `Copy to ${drive.location_name}`,
          icon: 'arrow_forward',
          disabled: drive.is_formatting,
          onClick: () => onCopyToDrive(selectedCPLs, drive),
        }))
      }}/> : null}
    <Button
      type='error'
      tertiary
      size='small'
      onClick={() => {
        if (window.confirm(`Are you sure you want to delete ${selectedCPLs.length} DCP${selectedCPLs.length === 1 ? '' : 's'}?`)) {
          onDelete(selectedCPLs)
        }
      }}>
      Delete {selectedCPLs.length} DCP{selectedCPLs.length === 1 ? '' : 's'}
    </Button>
  </div>

export default Actions