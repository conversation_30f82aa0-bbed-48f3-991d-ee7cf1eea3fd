import React, { useState } from 'react'
import Button from '../../button'
import humanFileSize from '../../helpers/human_file_size'
import Icon from '../../icon'

function File({ file, child }) {
  return (
    <div className={`flex items-center w-full space-x-4 justify-between my-1`}>
      <div className='flex items-center space-x-2 truncate'>
        {child ? <Icon icon='subdirectory_arrow_right' className='text-gray-600'/> : null}
        <Icon icon='insert_drive_file' className='text-gray-600'/>
        <div>{file.name}</div>
      </div>
      <div>{humanFileSize(file.size)}</div>
    </div>
  )
}

function Folder({ folder, onDeleteFolder, child }) {
  const [collapsed, setCollapsed] = useState(true)
  return (
    <div>
      <div
        className='flex items-center justify-between space-x-4 cursor-pointer w-full'
        onClick={() => setCollapsed(!collapsed)}>
        <div className='flex items-center space-x-2 truncate'>
          {child ? <Icon icon='subdirectory_arrow_right' className='text-gray-600'/> : null}
          <Icon icon='folder_open' className='text-gray-600'/>
          <div className='pr-4'>{folder.name} ({humanFileSize(folder.size)})</div>
        </div>
        <div className='flex items-center space-x-4'>
          {!child ?
            <Button
              size='small'
              type='neutral'
              tertiary
              onClick={() => {
                if (window.confirm(`Are you sure you want to delete ${folder.name}?`)) {
                  onDeleteFolder(folder.path)
                }
              }}>
              Delete
            </Button> :
            null}
          <Icon icon={collapsed ? 'expand_more' : 'expand_less'}/>
        </div>
      </div>
      {!collapsed ? <div className='pl-4 divide-y'>
        {folder.children.map(download =>
          <Download download={download} child={true}/>
        )}
      </div> : null}
    </div>
  )
}

export default function Download({ download, onDeleteFolder, child = false }) {
  return (
    <div className={`text-xs ${!child ? 'bg-white border my-1 py-2 px-4' : ''}`}>
      {download.type === 'file' ? 
        <File file={download} child={child}/> :
        <Folder folder={download} onDeleteFolder={onDeleteFolder} child={child}/>        
      }
    </div>
  )
}