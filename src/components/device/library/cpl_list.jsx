import React, { useState } from 'react'
import sort from '../../helpers/sort'
import NoneFound from '../none_found'
import LibraryCPL from './cpl'
import Collapse from '../../collapse'

const CPLList = ({
  cpls = [],
  drives = [],
  height,
  search,
  sorting,
  filters,
  groupByKind,
  ...props
}) => {
  const [selectedCPLs, setSelectedCPLs] = useState([])
  const list = searchSortAndFilterList(cpls, search, sorting, filters)
  const selectedIDs = selectedCPLs.map(cpl => cpl.uuid)
  const kinds = [...new Set(list.map(item => item.kind))]
  return (
    <div className={`overflow-y-scroll bg-gray-50 px-4 py-2 space-y-1 grow ${drives.length ? 'border-r' : ''}`}>
      {
        list.length ?
          groupByKind ?
            <div>
              {kinds.map(kind => (
                <Collapse title={kind} className='capitalize'>
                  {list.filter(cpl => cpl.kind === kind).map(cpl =>
                    <LibraryCPL
                      cpl={cpl}
                      drives={drives}
                      selectedIDs={selectedIDs}
                      selectedCPLs={selectedCPLs}
                      setSelectedCPLs={setSelectedCPLs}
                      {...props}/>
                  )}
                </Collapse>
              ))}
            </div>
          : list.map(cpl =>
            <LibraryCPL
              cpl={cpl}
              drives={drives}
              selectedIDs={selectedIDs}
              selectedCPLs={selectedCPLs}
              setSelectedCPLs={setSelectedCPLs}
              {...props}/>
        ) :
          <NoneFound
            header='No content found'
            description={
              (filters && Object.keys(filters).length > 0)
                ? 'Check your filters.'
                  : search
                  ? 'Check your search term.'
                    : 'No DCPs found on your local media library.'}/>
      }
    </div>
  )
}

const searchSortAndFilterList = (list, search, sorting, filters) => {
  list = list.filter(cpl => {
    let searched = !search
    let filtered = !filters
    if (search) {
      searched = cpl.title.toLowerCase().includes(search.toLowerCase())
    }
    if (filters) {
      Object.entries(filters).forEach(([key, values]) => {
        filtered = values.length === 0 || values.includes(cpl[key])
      })
    }
    return searched && filtered
  })
  return sort(list, sorting.key, sorting.direction)
}

export default CPLList
