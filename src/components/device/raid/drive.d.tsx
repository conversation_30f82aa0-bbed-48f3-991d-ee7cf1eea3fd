import * as React from "react";
import Raid from "./health.d";

interface Props {
  raid: Raid
}

interface PropType {
  disk: {
    title: string;
    size: number;
    location: string;
    storage_pool: string;
    estimated_lifespan: string;
    bad_record_count: number;
    model: string;
    serial_number: string;
    firmware_version: string;
    temperature: string;
    health_status: string;
  };
}

declare const Drive: React.FunctionComponent<Props>

export default Drive
