import React from 'react'
import Tag from "../../tag"
import ProgressBar from "../../progress_bar"
import humanFileSize from '../../helpers/human_file_size'
import Drive from './drive'

const RaidHealth = ({ raid }) => {

  return (
    <>
      {raid &&
        <div className='space-y-4'>
          <div className="w-full bg-white rounded shadow p-4">
            <p className="font-size-2xl font-bold ">Storage Pool</p>
            <div className="flex items-center space-x-2">
              <p>Media Cache</p>
              <Tag
                label={"Normal"}
                type={"success"}
                outline={false}
                size={"small"}
              />
            </div>
            <p className="text-2xs">
              {raid.raid_name}, {raid.format_type}
            </p>
            <ProgressBar
              displayPercentage={true}
              completed={(raid.storage.used / raid.storage.total) * 100}
            />
            <p className="text-2xs">
              {humanFileSize(raid.storage.used)} of{" "}
              {humanFileSize(raid.storage.total)}
            </p>
          </div>
          <div className='space-y-2 bg-white rounded shadow p-4'>
            <p className="font-size-2xl font-bold">HDD / SDD</p>
            <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {raid.disks.map(disk => {
                return <Drive disk={disk} />;
              })}
            </div>
          </div>
        </div>}
    </>
  );
};

export default RaidHealth;
