import * as React from "react";

interface Props {
  raid: Raid
}

interface Raid {
  version: string;
  creation_time: string;
  raid_level: string;
  array_size: string;
  used_dev_size: string;
  raid_devices: string;
  total_devices: string;
  persistence: string;
  intent_bitmap: string;
  state: string;
  active_devices: string;
  working_devices: string;
  failed_devices: string;
  spare_devices: string;
  layout: string;
  chunk_size: string;
  consistency_policy: string;
  name: string;
  uuid: string;
  format_type: string;
  raid_name: string;
  storage: any;
  disks: any;
}

declare const Raid: React.FunctionComponent<Props>

export default Raid
