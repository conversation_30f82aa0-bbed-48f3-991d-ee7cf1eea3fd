import React from 'react'
import Tag from "../../tag"
import humanFileSize from '../../helpers/human_file_size'

const RaidDrive = ({ disk }) => {
  return (
    <div className='border rounded p-2'>
      <div className='flex items-center justify-between'>
        <p className="font-bold">{disk.serial_number}</p>
        <Tag
          label={disk.health_status}
          type={"success"}
          outline={false}
          size={"small"}
        />
      </div>
      <div className="text-xs">
        <div className="flex justify-between">
          <p>Size</p>
          <p>{humanFileSize(disk.size)}</p>
        </div>
        <div className="flex justify-between">
          <p>Location</p>
          <p>{disk.location}</p>
        </div>
        <div className="flex justify-between">
          <p>Storage Pool</p>
          <p>{disk.storage_pool}</p>
        </div>
        <div className="flex justify-between">
          <p>Health Status</p>
          <p>{disk.health_status}</p>
        </div>
        <div className="flex justify-between">
          <p>Estimated Lifespan</p>
          <p>{disk.estimated_lifespan}</p>
        </div>
        <div className="flex justify-between">
          <p>Bad Record Count</p>
          <p>{disk.bad_record_count}</p>
        </div>
        <div className="flex justify-between">
          <p>Temperature</p>
          <p>{disk.temperature}</p>
        </div>
        <div className="flex justify-between">
          <p>Version Number</p>
          <p>{disk.firmware_version}</p>
        </div>
      </div>
    </div>
  );
};

export default RaidDrive;
