import React from 'react'
import humanFileSize from "../../helpers/human_file_size"
import Toggle from '../../toggle'

const Footer = ({
  isLibrary = false,
  title,
  count = null,
  diskUsed,
  diskSize,
  buttons = [],
  showStrandedAssets,
  toggleShowStrandedAssets = () => {}
}) =>
  <div className={`bg-white p-4 flex flex-wrap items-center justify-between text-xs ${buttons ? 'border-y border-r' : 'border'}`}>
    <div>
      <span className='text-gray-600 font-normal'>{title}</span>
      <div className='flex items-center space-x-2 text-primary-500 font-medium'>
        <div className="w-28 bg-gray-200 h-1">
          <div
            className="bg-primary-500 h-1"
            style={{ width: (diskUsed / diskSize) * 100 + '%' }}/>
        </div>
        <span>{humanFileSize(diskUsed)} / {humanFileSize(diskSize)}</span>
        {count ? <>
          <span>•</span>
          <span>{count} CPL{count === 1 ? '' : 's'}</span>
        </> : null}
      </div>
    </div>
    {buttons ? <div className='flex items-center space-x-4'>
      {buttons}
    </div> : null}
    {isLibrary
      ? <Toggle size='small' checked={showStrandedAssets} label='Show Stranded Assets' onChange={() => toggleShowStrandedAssets()}/>
      : null}
  </div>

export default Footer
