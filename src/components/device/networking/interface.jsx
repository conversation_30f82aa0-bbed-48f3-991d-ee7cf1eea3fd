import React, {useState} from "react";
import NetworkModal from "./network_modal";
import Tag from "../../tag"
import Icon from "../../icon"
import Button from "../../button"
import Input from "../../input"
import Select from "../../select"
import {ArrowTop, ArrowBottom} from "../../icons"

import humanFileSize from '../../helpers/human_file_size'

const data = [
  {label: 'Interface', property: 'name'},
  {label: 'Address', property: 'address'},
  {label: 'Gateway', property: 'gateway'},
  {label: 'Netmask', property: 'netmask'},
  {label: 'DNS', property: 'dns'},
  {label: 'Mac Address', property: 'mac'},
  {label: 'Family', property: 'family'},
  {label: 'CIDR', property: 'cidr'}
]

const Interface = ({
    interfaces,
    networkInterface,
    isRefetching,
    updateLabel,
    updateDescription,
    updateInterface,
  }) => {
  const [interfaceState, setInterface] = useState(networkInterface)
  const [modalOpen, setModalOpen] = useState(false)
  const labelOptions = interfaces.map((opt, index) => {
    const label = `Eth ${index + 1}`
    return ({value: label, label})
  })
  return (
    <div className='bg-white rounded shadow p-4 space-y-2 w-full'>
      <div className='flex items-center justify-between border-b pb-2'>
        <div className='items-center flex-col md:flex-row md:flex md:space-y-0 space-y-2 space-x-0 md:space-x-4'>
          <div className='text-sm font-semibold'>
            {networkInterface.label || networkInterface.name}
          </div>
          {networkInterface.method &&
            <div className='flex items-center'>
              <Tag
                outline
                label={networkInterface.method === 'auto' ? 'DHCP' : 'STATIC'}/>
            </div>}
          {networkInterface.is_internet_connected &&
            <Icon
              icon='public'
              className='text-success-500 opacity-70'
              tooltip={{
                text: 'Connected to the internet!',
                direction: 'bottom'
              }}/>}
          {networkInterface.bytes_received !== null &&
            <div className='flex items-center'>
              <Tag
                icon={ArrowBottom}
                outline
                type={networkInterface.bytes_received ? 'success' : 'warning'}
                label={humanFileSize(networkInterface.bytes_received)}/>
            </div>}
          {networkInterface.bytes_transmitted !== null &&
            <div className='flex items-center'>
              <Tag
                icon={ArrowTop}
                outline
                type={networkInterface.bytes_transmitted ? 'success' : 'warning'}
                label={humanFileSize(networkInterface.bytes_transmitted)}/>
            </div>}
        </div>
        <div className='flex items-center space-x-2'>
          {isRefetching && <Icon icon='refresh' className='text-sm animate-spin-slow'/>}
          <Button size='small' secondary onClick={() => setModalOpen(true)}>Edit</Button>
        </div>
      </div>
      <div className='border rounded'>
        {data.map(({property, label}, index) => (
          <div
            key={index}
            className={`p-1 flex items-center justify-between text-xs ${index % 2 === 0 ? 'bg-primary-100' : 'bg-neutral-100'}`}>
            <div>{label}</div>
            <div className='font-semibold'>{interfaceState[property]}</div>
          </div>
        ))}
      </div>
      <div className='pt-2 flex-col md:flex md:flex-row space-x-0 md:space-x-4 space-y-2 md:space-y-0'>
        <Select
          label='Physical Label'
          message={' (once set, this label should not be modified)'}
          options={labelOptions}
          value={labelOptions.find(opt => opt.value === interfaceState.label)}
          onChange={(opt) => {
            if (!interfaceState.label || (interfaceState.label && window.confirm("Are you sure you want to change the label for this interface?"))) {
              setInterface({...interfaceState, label: opt.value});
              updateLabel()
            }
          }} id={''}/>
        <Input
          label='Description'
          value={interfaceState.description}
          onChange={(e) => setInterface({...interfaceState, description: e.target.value})}
          id={undefined}
          onBlur={() => updateDescription()}
        />
      </div>
      {modalOpen &&
        <NetworkModal
          updateInterface={updateInterface}
          onClose={() => setModalOpen(false)}
          networkInterface={networkInterface} />}
    </div>
  )
}

export default Interface
