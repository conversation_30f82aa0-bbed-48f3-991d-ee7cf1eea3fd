import React from 'react'
import Interface from "./interface";

const NetworkInterfaces = ({
  networkInterfaces,
  isRefetching, updateLabel = () => {
    alert('Please implement updateLabel()')
  },
  updateDescription = () => {
    alert('Please implement updateDescription()')
  },
  updateInterface = (data) => {
    alert('Please implement updateInterface() ');
    console.log(data)
  },
  }) => {
  return (
    <>
      {networkInterfaces &&
        <div className={`w-full flex-col space-y-4 md:flex md:flex-row md:space-x-4 md:space-y-0`}>
          {networkInterfaces.map((value, index) =>
            <Interface
              key={index}
              isRefetching={isRefetching}
              interfaces={networkInterfaces}
              networkInterface={value}
              updateDescription={updateDescription}
              updateInterface={updateInterface}
              updateLabel={updateLabel}/>
          )}
        </div>
      }
    </>
  );
};

export default NetworkInterfaces
