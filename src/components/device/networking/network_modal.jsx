import React, {BaseSyntheticEvent, useState} from "react";
import Modal from "../../modal"
import SegmentedControl from "../../segmented_control"
import Input from "../../input"
import Select from "../../select"

const NetworkModal = ({onClose, networkInterface, updateInterface = (data) => {}}) => {
  // const { addMessage } = useSnackbarStore()
  const [useDHCP, setDhcp] = useState(false)
  const [ip, setIp] = useState("")
  const [gateway, setGateway] = useState("")
  const [subnet, setSubnet] = useState("")
  const [dnsOne, setDnsOne] = useState("")
  const [dnsTwo, setDnsTwo] = useState("")

  const subnetMapping = subnetMaskOptions.reduce((acc, curr) => (acc[curr.subnet] = curr.suffix, acc), {})
  const subnetSelectOptions = subnetMaskOptions.map(opt => ({label: opt.subnet, value: opt.subnet}))

  const ipError = !/^(?!0)(?!.*\.$)((1?\d?\d|25[0-5]|2[0-4]\d)(\.|$)){4}$/.test(ip)

  return (
    <Modal
      header={`Update ${networkInterface.label}`}
      onClose={onClose}
      pending={false}
      confirmButton={{
        text: useDHCP ? 'Set DHCP interface' : 'Set static values',
        onClick: () => {
          let data = useDHCP ? { useDHCP: true } : {
            ip: `${ip}${subnetMapping[subnet]}`,
            gateway,
            dnsOne,
            dnsTwo
          }
          updateInterface(data)
        },
        disabled: !useDHCP && (!ip || !subnet || ipError)
      }}>
      <div className='space-y-2'>
        <SegmentedControl
          options={[
            {
              label: 'Static',
              value: 'static'
            },
            {
              label: 'DHCP',
              value: 'dhcp'
            }
          ]}
          value={useDHCP ? 'dhcp' : 'static'}
          onChange={() => setDhcp(!useDHCP)}
        />
        {!useDHCP && <>
          <div className='flex items-center space-x-2'>
            <Input
              className='w-5/6'
              label="IP Address"
              value={ip}
              onChange={(e) => setIp(e.target.value)}
              error={ipError} id={undefined}/>
            <Input
              className='w-1/6'
              disabled
              label="Suffix"
              value={subnetMapping[subnet]} id={undefined}/>
          </div>
          <div className='flex space-x-4'>
            <Input
              label="Gateway"
              value={gateway}
              onChange={(e) => setGateway(e.target.value)} id={undefined}/>
            <Select
              label='Subnet Mask'
              options={subnetSelectOptions}
              value={subnetSelectOptions.find(opt => opt.value === subnet)}
              onChange={(opt) => setSubnet(opt.value)} id={undefined}/>
          </div>
          <div className='flex space-x-4'>
            <Input
              label="DNS 1"
              value={dnsOne}
              onChange={(e) => setDnsOne(e.target.value)} id={undefined}/>
            <Input
              label="DNS 2"
              value={dnsTwo}
              onChange={(e) => setDnsTwo(e.target.value)} id={undefined}/>
          </div>
        </>}
      </div>
    </Modal>
  );
};

const subnetMaskOptions = [
  {suffix: "/8", subnet: "255.0.0.0"},
  {suffix: "/9", subnet: "255.128.0.0"},
  {suffix: "/10", subnet: "255.192.0.0"},
  {suffix: "/11", subnet: "255.224.0.0"},
  {suffix: "/12", subnet: "255.240.0.0"},
  {suffix: "/13", subnet: "255.248.0.0"},
  {suffix: "/14", subnet: "255.252.0.0"},
  {suffix: "/15", subnet: "255.254.0.0"},
  {suffix: "/16", subnet: "255.255.0.0"},
  {suffix: "/17", subnet: "255.255.128.0"},
  {suffix: "/18", subnet: "255.255.192.0"},
  {suffix: "/19", subnet: "255.255.224.0"},
  {suffix: "/20", subnet: "255.255.240.0"},
  {suffix: "/21", subnet: "255.255.248.0"},
  {suffix: "/22", subnet: "255.255.252.0"},
  {suffix: "/23", subnet: "255.255.254.0"},
  {suffix: "/24", subnet: "255.255.255.0"},
  {suffix: "/25", subnet: "255.255.255.128"},
  {suffix: "/26", subnet: "255.255.255.192"},
  {suffix: "/27", subnet: "255.255.255.224"},
  {suffix: "/28", subnet: "255.255.255.240"},
  {suffix: "/29", subnet: "255.255.255.248"},
  {suffix: "/30", subnet: "255.255.255.252"}
]

export default NetworkModal;
