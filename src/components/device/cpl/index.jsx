import React, { useState } from 'react'
import ButtonDropdown from "../../button_dropdown"
import Checkbox from "../../checkbox"
import Modal from "../../modal"
import humanFileSize from '../../helpers/human_file_size'
import Icon from '../../icon'
import StatusIcon from "./status_icon"
import useIsMobile from '../../hooks/use_is_mobile'

const CPL = ({
  id,
  title,
  is_smpte,
  is_encrypted,
  is_removing,
  validation_error,
  progress,
  transfer_speed,
  estimated_completion,
  isSelected = false,
  status = {},
  color = null,
  onCheckboxToggle = () => {},
  dropdownActions = null,
  ...props
}) => {
  const [showCPL, setShowCPL] = useState(false)
  const disabled = is_removing || progress
  const isMobile = useIsMobile()
  return (
    <div
      key={id}
      style={{
        borderLeftColor: color ? color : null,
        borderWidth: '1px 1px 1px 4px',
      }}
      className={`relative text-xs flex items-center justify-between rounded shadow-sm border p-4 bg-white
        ${disabled ? 'cursor-not-allowed pointer-events-none' : 'cursor-pointer'}`}
      onClick={e => {
        e.stopPropagation()
        e.preventDefault()
        onCheckboxToggle(props.uuid)
      }}>
      {is_removing && <div className='bg-white/50 absolute inset-0'/>}
      <div className='flex items-center overflow-clip w-[95%] space-x-4'>
        <Checkbox
          size='small'
          disabled={disabled}
          checked={isSelected}
          onChange={() => onCheckboxToggle(props.uuid)}/>
        <div className='flex flex-col space-y-1 overflow-hidden'>
          <div className='font-medium text-gray-900 flex items-center space-x-0.5'>
            {is_encrypted && <Icon icon='lock' className='text-xs text-gray-800'/>}
            <span>{title}</span>
          </div>
          {progress ? <div className="w-full bg-gray-200 h-0.5">
            <div
              className="bg-gray-400 h-0.5"
              style={{ width: progress + '%' }}/>
          </div> : null}
          <div className={`w-full flex flex-wrap items-center space-x-2 text-gray-600 font-normal pr-4`}>
            <StatusIcon {...status}/>
            {progress ?
              <>
                {transfer_speed ?
                  <>
                    <span>•</span>
                    <span className='whitespace-nowrap'>{transfer_speed}</span>
                  </> : null}
                <>
                  <span>•</span>
                  <span>{progress.toFixed()}%</span>
                  {props.size && <>
                    <span>•</span>
                    <span className='whitespace-nowrap'>{humanFileSize((progress / 100) * props.size)} / {humanFileSize(props.size)}</span>
                  </>}
                </>
                {estimated_completion ?
                  <>
                    <span>•</span>
                    <span className='whitespace-nowrap'>{estimated_completion}</span>
                  </> : null}
              </>
              : <>
                {props.size ? <>
                  <span>•</span>
                  <span>{humanFileSize(props.size)}</span>
                </> : null}
                {is_smpte !== null ? <>
                  <span>•</span>
                  <span>{is_smpte ? 'SMPTE' : 'Interop'}</span>
                </> : null}
                {props.timecode ? <>
                  <span>•</span>
                  <span>{props.timecode}</span>
                </> : null}
                {props.kind ? <>
                  <span>•</span>
                  <span className='capitalize'>{props.kind}</span>
                </> : null}
              </>}
          </div>
          {validation_error ? <span className='text-error-600 flex items-center space-x-1'>
              <Icon icon='error_outline' className='text-sm'/>
              <span>{validation_error}</span>
            </span> : null}
        </div>
      </div>
    {!disabled && <ButtonDropdown
        button={{ icon: 'more_vert', type: 'neutral', minimal: true }}
        dropdown={{
          content: [{ text: 'View CPL Info', icon: 'list', breakAfter: true, onClick: () => setShowCPL(true) }, ...dropdownActions]
        }}/>}
      {showCPL && <Modal minWidth={!isMobile} header='CPL Info' onClose={() => setShowCPL(false)}>
        <div className='divide-y border text-xs'>
          {[
            { label: "Playable", key: "playable" },
            { label: "ID", key: "uuid" },
            { label: "Kind", key: "kind" },
            { label: "Frame Rate", key: "frame_rate" },
            { label: "Duration", key: "timecode" },
            { label: "Size", key: "size", type: "size" },
            { label: "Encrypted", key: "encrypted" },
            { label: "Format", key: "format" },
            { label: "Issuer", key: "issuer" },
            { label: "Creator", key: "creator" },
            { label: "Compliance", key: "compliance" },
            { label: "Dimension", key: "dimension" },
            { label: "ATMOS", key: "atmos" },
            { label: "Reel Count", key: "reel_count" },
            { label: "Issue Date", key: "issue_date", type: "date" },
            { label: "Error", key: "validation_error" },
            { label: "Fox PVID", key: "fox_pvid" }
          ]
            .filter(row => !!props[row.key])
            .map((row, index) => (
              <div key={index} className='flex items-center justify-between px-2 py-1'>
                <div>{row.label}</div>
                <div className={`font-semibold ${row.key !== 'uuid' ? 'capitalize' : ''}`}>
                  {row.type === "size"
                    ? humanFileSize(props[row.key])
                    : props[row.key]}
                </div>
              </div>
            ))}
          </div>
        </Modal>}
    </div>
  )
}

export default CPL