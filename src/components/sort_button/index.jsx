import React from 'react'
import { <PERSON><PERSON>ott<PERSON>, ArrowTop } from '../icons'
import ButtonDropdown from '../button_dropdown'
import Tag from '../tag'
import useIsMobile from '../hooks/use_is_mobile'

const SortButton = ({
  options = [],
  sortingKey,
  direction,
  onSortChange
}) => {
  const isMobile = useIsMobile()
  return <>
    <ButtonDropdown
      button={{
        type: 'neutral',
        minimal: true,
        text: !isMobile && 'Sort',
        icon: "sort",
      }}
      dropdown={{
        clickCloses: false,
        anchor: "left",
        content:
        <div className='min-w-[200px] p-4 flex flex-col space-y-2 text-sm text-gray-600'>
            {options.map((sortOption, index) => {
              const isSorting = sortingKey === sortOption.key
              return ( 
                <div
                key={sortOption.key}
                className={
                  `flex items-center justify-between cursor-pointer ${index !== 0 ? 'border-t border-gray-200 pt-2' : ''}
                  ${isSorting ? 'text-black font-semibold' : 'text-gray-800'}`
                }
                onClick={() => onSortChange({
                  direction: isSorting ? (direction === 'ASC' ? 'DESC' : 'ASC') : 'ASC', // Swap the sort direction if we're already on this key
                  key: sortOption.key
                })}>
                  <span className='mr-2'>{sortOption.label}</span>
                  <div className='flex space-x-1 items-center'>
                    {isSorting ? <Tag label={direction} type='neutral' size='small'/>: null}
                    {isSorting && direction === 'ASC' && <ArrowTop className={`h-5 w-5`}/>}
                    {isSorting && direction === 'DESC' && <ArrowBottom className={`h-5 w-5`}/>}                    
                  </div>
                </div>
              )
            })}
          </div>
      }}/>
  </>
}
 
 export default SortButton
 