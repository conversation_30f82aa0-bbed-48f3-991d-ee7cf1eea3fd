import React from 'react'

const Wrapper = ({ className = 'w-6 h-6', viewBox = "0 0 24 24", onClick = null, children }) =>
  <svg
    onClick={onClick}
    className={`fill-current ${className}`}
    viewBox={viewBox}
    xmlns="http://www.w3.org/2000/svg">
    {children}
  </svg>

export const Check = props =>
  <Wrapper {...props}>
    <path d="M20.5067 5.44714C20.8121 5.72703 20.8327 6.20146 20.5528 6.5068L9.5528 18.5068C9.41458 18.6576 9.22075 18.7454 9.01623 18.7498C8.81172 18.7543 8.61426 18.675 8.46961 18.5303L3.46961 13.5303C3.17672 13.2374 3.17672 12.7626 3.46961 12.4697C3.7625 12.1768 4.23738 12.1768 4.53027 12.4697L8.97638 16.9158L19.4471 5.49321C19.727 5.18787 20.2014 5.16724 20.5067 5.44714Z"/>
  </Wrapper>

export const Minus = props =>
  <Wrapper {...props}>
    <path d="M5.25 12C5.25 11.5858 5.58579 11.25 6 11.25H18C18.4142 11.25 18.75 11.5858 18.75 12C18.75 12.4142 18.4142 12.75 18 12.75H6C5.58579 12.75 5.25 12.4142 5.25 12Z"/>
  </Wrapper>

export const ChevronLeft = props =>
  <Wrapper {...props}>
    <path d="M14.5303 5.46967C14.8232 5.76256 14.8232 6.23744 14.5303 6.53033L9.06066 12L14.5303 17.4697C14.8232 17.7626 14.8232 18.2374 14.5303 18.5303C14.2374 18.8232 13.7626 18.8232 13.4697 18.5303L7.46967 12.5303C7.17678 12.2374 7.17678 11.7626 7.46967 11.4697L13.4697 5.46967C13.7626 5.17678 14.2374 5.17678 14.5303 5.46967Z"/>
  </Wrapper>

export const ChevronRight = props =>
  <Wrapper {...props}>
    <path d="M9.46967 5.46967C9.76256 5.17678 10.2374 5.17678 10.5303 5.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L10.5303 18.5303C10.2374 18.8232 9.76256 18.8232 9.46967 18.5303C9.17678 18.2374 9.17678 17.7626 9.46967 17.4697L14.9393 12L9.46967 6.53033C9.17678 6.23744 9.17678 5.76256 9.46967 5.46967Z"/>
  </Wrapper>

export const ChevronDown = props =>
  <Wrapper {...props}>
    <path d="M5.46967 8.46967C5.76256 8.17678 6.23744 8.17678 6.53033 8.46967L12 13.9393L17.4697 8.46967C17.7626 8.17678 18.2374 8.17678 18.5303 8.46967C18.8232 8.76256 18.8232 9.23744 18.5303 9.53033L12.5303 15.5303C12.2374 15.8232 11.7626 15.8232 11.4697 15.5303L5.46967 9.53033C5.17678 9.23744 5.17678 8.76256 5.46967 8.46967Z"/>
  </Wrapper>

export const ChevronUp = props =>
  <Wrapper {...props}>
    <path d="M11.4697 8.46967C11.7626 8.17678 12.2374 8.17678 12.5303 8.46967L18.5303 14.4697C18.8232 14.7626 18.8232 15.2374 18.5303 15.5303C18.2374 15.8232 17.7626 15.8232 17.4697 15.5303L12 10.0607L6.53033 15.5303C6.23744 15.8232 5.76256 15.8232 5.46967 15.5303C5.17678 15.2374 5.17678 14.7626 5.46967 14.4697L11.4697 8.46967Z"/>
  </Wrapper>

export const Filter = props =>
  <Wrapper {...props}>
    <path d="M3.81818 5.77778C3.60119 5.77778 3.39308 5.68413 3.23964 5.51743C3.0862 5.35073 3 5.12464 3 4.88889C3 4.65314 3.0862 4.42705 3.23964 4.26035C3.39308 4.09365 3.60119 4 3.81818 4H20.1818C20.3988 4 20.6069 4.09365 20.7604 4.26035C20.9138 4.42705 21 4.65314 21 4.88889C21 5.12464 20.9138 5.35073 20.7604 5.51743C20.6069 5.68413 20.3988 5.77778 20.1818 5.77778H3.81818ZM6.27273 10.6667C6.05573 10.6667 5.84762 10.573 5.69419 10.4063C5.54075 10.2396 5.45455 10.0135 5.45455 9.77778C5.45455 9.54203 5.54075 9.31594 5.69419 9.14924C5.84762 8.98254 6.05573 8.88889 6.27273 8.88889H17.7273C17.9443 8.88889 18.1524 8.98254 18.3058 9.14924C18.4593 9.31594 18.5455 9.54203 18.5455 9.77778C18.5455 10.0135 18.4593 10.2396 18.3058 10.4063C18.1524 10.573 17.9443 10.6667 17.7273 10.6667H6.27273ZM8.72727 15.5556C8.51028 15.5556 8.30217 15.4619 8.14873 15.2952C7.99529 15.1285 7.90909 14.9024 7.90909 14.6667C7.90909 14.4309 7.99529 14.2048 8.14873 14.0381C8.30217 13.8714 8.51028 13.7778 8.72727 13.7778H15.2727C15.4897 13.7778 15.6978 13.8714 15.8513 14.0381C16.0047 14.2048 16.0909 14.4309 16.0909 14.6667C16.0909 14.9024 16.0047 15.1285 15.8513 15.2952C15.6978 15.4619 15.4897 15.5556 15.2727 15.5556H8.72727ZM11.1818 20C10.9648 20 10.7567 19.9064 10.6033 19.7397C10.4498 19.573 10.3636 19.3469 10.3636 19.1111C10.3636 18.8754 10.4498 18.6493 10.6033 18.4826C10.7567 18.3159 10.9648 18.2222 11.1818 18.2222H12.8182C13.0352 18.2222 13.2433 18.3159 13.3967 18.4826C13.5502 18.6493 13.6364 18.8754 13.6364 19.1111C13.6364 19.3469 13.5502 19.573 13.3967 19.7397C13.2433 19.9064 13.0352 20 12.8182 20H11.1818Z"/>
  </Wrapper>

export const Sort = props =>
  <Wrapper {...props}>
    <path d="M16.3928 4L21.1457 8.95087L20.28 9.78191L16.9928 6.35777V19.8664H15.7928V6.35777L12.5057 9.78191L11.64 8.95087L16.3928 4ZM8.35283 4.86639V18.375L11.64 14.9509L12.5057 15.7819L7.75283 20.7328L3 15.7819L3.86567 14.9509L7.15283 18.375V4.86639H8.35283Z"/>
  </Wrapper>

export const Search = props =>
  <Wrapper {...props}>
    <path d="M3.75 11C3.75 6.99594 6.99594 3.75 11 3.75C15.0041 3.75 18.25 6.99594 18.25 11C18.25 15.0041 15.0041 18.25 11 18.25C6.99594 18.25 3.75 15.0041 3.75 11ZM11 2.25C6.16751 2.25 2.25 6.16751 2.25 11C2.25 15.8325 6.16751 19.75 11 19.75C13.1462 19.75 15.112 18.9773 16.6342 17.6949L19.4697 20.5303C19.7626 20.8232 20.2374 20.8232 20.5303 20.5303C20.8232 20.2374 20.8232 19.7626 20.5303 19.4697L17.6949 16.6342C18.9773 15.112 19.75 13.1462 19.75 11C19.75 6.16751 15.8325 2.25 11 2.25Z"/>
  </Wrapper>

export const Star = props =>
  <Wrapper {...props}>
    <path d="M12 2C12.2913 2 12.5577 2.16462 12.688 2.42522L15.5897 8.22871L21.3572 9.18995C21.6414 9.23732 21.8753 9.43957 21.9633 9.71396C22.0512 9.98836 21.9784 10.2889 21.7747 10.4926L17.4608 14.8066L18.9034 21.0578C18.9703 21.348 18.8637 21.6509 18.6297 21.8351C18.3956 22.0194 18.0762 22.052 17.8098 21.9188L12 19.0139L6.19015 21.9188C5.92371 22.052 5.60432 22.0194 5.37028 21.8351C5.13624 21.6509 5.02962 21.348 5.0966 21.0578L6.5392 14.8066L2.22528 10.4926C2.02154 10.2889 1.94874 9.98836 2.03668 9.71396C2.12461 9.43957 2.35853 9.23732 2.64275 9.18995L8.41022 8.22871L11.312 2.42522C11.4423 2.16462 11.7086 2 12 2ZM12 4.48928L9.61108 9.26709C9.50102 9.48721 9.29227 9.64138 9.04952 9.68184L4.3701 10.4617L7.92852 14.0202C8.11607 14.2077 8.19377 14.4786 8.13413 14.7371L6.96291 19.8124L11.656 17.4658C11.8725 17.3575 12.1274 17.3575 12.344 17.4658L17.0371 19.8123L15.8658 14.7371C15.8062 14.4786 15.8839 14.2077 16.0714 14.0202L19.6299 10.4617L14.9504 9.68184C14.7077 9.64138 14.4989 9.48721 14.3889 9.26708L12 4.48928Z"/>
  </Wrapper>

export const Cross = props =>
  <Wrapper {...props}>
    <path d="M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L12 10.9393L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L13.0607 12L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L12 13.0607L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L10.9393 12L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"/>
  </Wrapper>

export const CrossCircle = props =>
  <Wrapper {...props}>
    <path d="M3.75 12C3.75 7.44365 7.44365 3.75 12 3.75C16.5563 3.75 20.25 7.44365 20.25 12C20.25 16.5563 16.5563 20.25 12 20.25C7.44365 20.25 3.75 16.5563 3.75 12ZM12 2.25C6.61522 2.25 2.25 6.61522 2.25 12C2.25 17.3848 6.61522 21.75 12 21.75C17.3848 21.75 21.75 17.3848 21.75 12C21.75 6.61522 17.3848 2.25 12 2.25Z"/>
    <path d="M15.2894 9.21967C15.5823 9.51256 15.5823 9.98744 15.2894 10.2803L10.2894 15.2803C10.1488 15.421 9.95796 15.5 9.75906 15.5C8.75906 15.5 8.92336 14.5857 9.25906 14.25L11.9939 11.4545L14.2288 9.21967C14.5217 8.92678 14.9965 8.92678 15.2894 9.21967Z"/>
    <path d="M9.21967 9.21967C8.92677 9.51256 8.92677 9.98744 9.21967 10.2803L14.2197 15.2803C14.3603 15.421 14.5511 15.5 14.75 15.5C15.75 15.5 15.5857 14.5857 15.25 14.25L12.5151 11.4545L10.2803 9.21967C9.98737 8.92678 9.51257 8.92678 9.21967 9.21967Z"/>
  </Wrapper>

export const Loader  = props =>
  <Wrapper {...props}>
    <path d="M11.25 3C11.25 2.58579 11.5858 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12C2.25 10.4448 2.61468 8.97258 3.26388 7.66623C3.44822 7.29529 3.89836 7.14403 4.26929 7.32836C4.64023 7.5127 4.79149 7.96284 4.60716 8.33377C4.05873 9.43734 3.75 10.6815 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75C11.5858 3.75 11.25 3.41421 11.25 3Z"/>
  </Wrapper>

export const AlertCircle = props =>
  <Wrapper {...props}>
    <path d="M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM12 7.25C12.4142 7.25 12.75 7.58579 12.75 8V13C12.75 13.4142 12.4142 13.75 12 13.75C11.5858 13.75 11.25 13.4142 11.25 13V8C11.25 7.58579 11.5858 7.25 12 7.25ZM12 17C12.5523 17 13 16.5523 13 16C13 15.4477 12.5523 15 12 15C11.4477 15 11 15.4477 11 16C11 16.5523 11.4477 17 12 17Z"/>
  </Wrapper>

export const Delete  = props =>
  <Wrapper {...props}>
    <path d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z"/>
  </Wrapper>

export const MoreVert = props =>
  <Wrapper {...props}>
    <path d="M12 7C13.1 7 14 6.1 14 5C14 3.9 13.1 3 12 3C10.9 3 10 3.9 10 5C10 6.1 10.9 7 12 7ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10ZM10 19C10 17.9 10.9 17 12 17C13.1 17 14 17.9 14 19C14 20.1 13.1 21 12 21C10.9 21 10 20.1 10 19Z"/>
  </Wrapper>

export const SolidInfoCircle = props =>
  <Wrapper {...props}>
    <path fillRule="evenodd" d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM12 10.25C12.4142 10.25 12.75 10.5858 12.75 11V16C12.75 16.4142 12.4142 16.75 12 16.75C11.5858 16.75 11.25 16.4142 11.25 16V11C11.25 10.5858 11.5858 10.25 12 10.25ZM12 9C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 11 8C11 8.55228 11.4477 9 12 9Z"/>
  </Wrapper>

export const InfoCircle = props =>
  <Wrapper {...props}>
    <path fillRule="evenodd" clipRule="evenodd" d="M12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM2.25 12C2.25 6.61522 6.61522 2.25 12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12ZM12 10.25C12.4142 10.25 12.75 10.5858 12.75 11V16C12.75 16.4142 12.4142 16.75 12 16.75C11.5858 16.75 11.25 16.4142 11.25 16V11C11.25 10.5858 11.5858 10.25 12 10.25ZM12 9C12.5523 9 13 8.55228 13 8C13 7.44772 12.5523 7 12 7C11.4477 7 11 7.44772 11 8C11 8.55228 11.4477 9 12 9Z"/>
  </Wrapper>

export const AlertTriangle = props =>
  <Wrapper {...props}>
    <path fillRule="evenodd" d="M12 2.24219C11.0768 2.24219 10.4033 2.79801 9.82703 3.52206C9.26342 4.23026 8.66365 5.26627 7.92192 6.54749L7.88922 6.60399L3.827 13.6206L3.79426 13.6771C3.04971 14.9631 2.44781 16.0027 2.11326 16.8466C1.77135 17.7091 1.62425 18.5723 2.08649 19.374C2.54873 20.1758 3.36946 20.481 4.2872 20.6172C5.1852 20.7505 6.38646 20.7505 7.87243 20.7505H7.93778H16.0622H16.1276C17.6135 20.7505 18.8148 20.7505 19.7128 20.6172C20.6305 20.481 21.4513 20.1758 21.9135 19.374C22.3757 18.5723 22.2286 17.7091 21.8867 16.8466C21.5522 16.0027 20.9503 14.9631 20.2058 13.6772L20.2057 13.6771L20.173 13.6206L16.1108 6.604L16.0781 6.54749C15.3363 5.26627 14.7366 4.23026 14.173 3.52206C13.5967 2.79801 12.9232 2.24219 12 2.24219ZM12.75 9.00049C12.75 8.58628 12.4142 8.25049 12 8.25049C11.5858 8.25049 11.25 8.58628 11.25 9.00049V13.0005C11.25 13.4147 11.5858 13.7505 12 13.7505C12.4142 13.7505 12.75 13.4147 12.75 13.0005V9.00049ZM13 16.0005C13 16.5528 12.5523 17.0005 12 17.0005C11.4477 17.0005 11 16.5528 11 16.0005C11 15.4482 11.4477 15.0005 12 15.0005C12.5523 15.0005 13 15.4482 13 16.0005Z"/>
  </Wrapper>

export const SolidAlertCircle = props =>
  <Wrapper {...props}>
    <path fillRule="evenodd" clipRule="evenodd" d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM12 7.25C12.4142 7.25 12.75 7.58579 12.75 8V13C12.75 13.4142 12.4142 13.75 12 13.75C11.5858 13.75 11.25 13.4142 11.25 13V8C11.25 7.58579 11.5858 7.25 12 7.25ZM12 17C12.5523 17 13 16.5523 13 16C13 15.4477 12.5523 15 12 15C11.4477 15 11 15.4477 11 16C11 16.5523 11.4477 17 12 17Z"/>
  </Wrapper>

export const CheckCircle = props =>
  <Wrapper {...props}>
    <path fillRule="evenodd" d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22ZM16.5303 9.53033C16.8232 9.23744 16.8232 8.76256 16.5303 8.46967C16.2374 8.17678 15.7626 8.17678 15.4697 8.46967L11 12.9393L9.53033 11.4697C9.23744 11.1768 8.76256 11.1768 8.46967 11.4697C8.17678 11.7626 8.17678 12.2374 8.46967 12.5303L10.4697 14.5303C10.6103 14.671 10.8011 14.75 11 14.75C11.1989 14.75 11.3897 14.671 11.5303 14.5303L16.5303 9.53033Z"/>
  </Wrapper>

export const Expand = props =>
  <Wrapper {...props} viewBox="0 0 16 16">
    <path fillRule="evenodd" clipRule="evenodd" d="M11.8073 6.43817C11.5504 6.69509 11.1338 6.69509 10.8769 6.43817L7.83336 3.39461L4.7898 6.43817C4.53288 6.69509 4.11632 6.69509 3.8594 6.43817C3.60248 6.18124 3.60248 5.76469 3.8594 5.50777L7.36816 1.99901C7.62508 1.74209 8.04163 1.74209 8.29856 1.99901L11.8073 5.50777C12.0642 5.76469 12.0642 6.18124 11.8073 6.43817Z"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M3.85938 9.89386C4.1163 9.63694 4.53286 9.63694 4.78978 9.89386L7.83333 12.9374L10.8769 9.89386C11.1338 9.63694 11.5504 9.63694 11.8073 9.89386C12.0642 10.1508 12.0642 10.5673 11.8073 10.8243L8.29853 14.333C8.04161 14.5899 7.62506 14.5899 7.36813 14.333L3.85938 10.8243C3.60246 10.5673 3.60246 10.1508 3.85938 9.89386Z"/>
  </Wrapper>

export const Calendar = props =>
  <Wrapper {...props}>
    <path d="M9 11H7V13H9V11ZM13 11H11V13H13V11ZM17 11H15V13H17V11ZM19 4H18V2H16V4H8V2H6V4H5C3.89 4 3.01 4.9 3.01 6L3 20C3 21.1 3.89 22 5 22H19C20.1 22 21 21.1 21 20V6C21 4.9 20.1 4 19 4ZM19 20H5V9H19V20Z"/>
  </Wrapper>

export const ArrowBottom = props =>
  <Wrapper {...props}>
    <path d="M12 4.25C12.4142 4.25 12.75 4.58579 12.75 5L12.75 17.1893L17.4697 12.4697C17.7626 12.1768 18.2374 12.1768 18.5303 12.4697C18.8232 12.7626 18.8232 13.2374 18.5303 13.5303L12.5303 19.5303C12.3897 19.671 12.1989 19.75 12 19.75C11.8011 19.75 11.6103 19.671 11.4697 19.5303L5.46967 13.5303C5.17678 13.2374 5.17678 12.7626 5.46967 12.4697C5.76256 12.1768 6.23744 12.1768 6.53033 12.4697L11.25 17.1893L11.25 5C11.25 4.58579 11.5858 4.25 12 4.25Z"/>
  </Wrapper>

export const ArrowTop = props =>
  <Wrapper {...props}>
    <path d="M11.4697 4.46967C11.7626 4.17678 12.2374 4.17678 12.5303 4.46967L18.5303 10.4697C18.8232 10.7626 18.8232 11.2374 18.5303 11.5303C18.2374 11.8232 17.7626 11.8232 17.4697 11.5303L12.75 6.81066L12.75 19C12.75 19.4142 12.4142 19.75 12 19.75C11.5858 19.75 11.25 19.4142 11.25 19L11.25 6.81066L6.53033 11.5303C6.23744 11.8232 5.76256 11.8232 5.46967 11.5303C5.17678 11.2374 5.17678 10.7626 5.46967 10.4697L11.4697 4.46967Z"/>
  </Wrapper>

export const OnDemandVideo = props =>
  <Wrapper {...props}>
    <path d="M21 3H3C1.89 3 1 3.89 1 5V17C1 18.1 1.89 19 3 19H8V21H16V19H21C22.1 19 22.99 18.1 22.99 17L23 5C23 3.89 22.1 3 21 3ZM21 17H3V5H21V17ZM16 11L9 15V7L16 11Z"/>
  </Wrapper>

export const File = props =>
  <Wrapper {...props}>
    <path d="M6 2C4.9 2 4.01 2.9 4.01 4L4 20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2H6ZM13 9V3.5L18.5 9H13Z"/>
  </Wrapper>

export const CloudUpload = props =>
  <Wrapper {...props}>
    <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4C9.11 4 6.6 5.64 5.35 8.04C2.34 8.36 0 10.91 0 14C0 17.31 2.69 20 6 20H19C21.76 20 24 17.76 24 15C24 12.36 21.95 10.22 19.35 10.04ZM14 13V17H10V13H7L12 8L17 13H14Z"/>
  </Wrapper>

export const Settings = props =>
  <Wrapper {...props}>
    <path d="M19.43 12.98C19.47 12.66 19.5 12.34 19.5 12C19.5 11.66 19.47 11.34 19.43 11.02L21.54 9.37C21.73 9.22 21.78 8.95 21.66 8.73L19.66 5.27C19.54 5.05 19.27 4.97 19.05 5.05L16.56 6.05C16.04 5.65 15.48 5.32 14.87 5.07L14.49 2.42C14.46 2.18 14.25 2 14 2H9.99999C9.74999 2 9.53999 2.18 9.50999 2.42L9.12999 5.07C8.51999 5.32 7.95999 5.66 7.43999 6.05L4.94999 5.05C4.71999 4.96 4.45999 5.05 4.33999 5.27L2.33999 8.73C2.20999 8.95 2.26999 9.22 2.45999 9.37L4.56999 11.02C4.52999 11.34 4.49999 11.67 4.49999 12C4.49999 12.33 4.52999 12.66 4.56999 12.98L2.45999 14.63C2.26999 14.78 2.21999 15.05 2.33999 15.27L4.33999 18.73C4.45999 18.95 4.72999 19.03 4.94999 18.95L7.43999 17.95C7.95999 18.35 8.51999 18.68 9.12999 18.93L9.50999 21.58C9.53999 21.82 9.74999 22 9.99999 22H14C14.25 22 14.46 21.82 14.49 21.58L14.87 18.93C15.48 18.68 16.04 18.34 16.56 17.95L19.05 18.95C19.28 19.04 19.54 18.95 19.66 18.73L21.66 15.27C21.78 15.05 21.73 14.78 21.54 14.63L19.43 12.98ZM12 15.5C10.07 15.5 8.49999 13.93 8.49999 12C8.49999 10.07 10.07 8.5 12 8.5C13.93 8.5 15.5 10.07 15.5 12C15.5 13.93 13.93 15.5 12 15.5Z"/>
  </Wrapper>

export const Copy = props =>
  <Wrapper {...props}>
    <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z"/>
  </Wrapper>

export const DragHandle = props =>
  <Wrapper {...props}>
    <path d="M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
  </Wrapper>

export const Visible = props =>
  <Wrapper {...props}>
    <path d="M2.2915 10.0003C2.2915 9.71832 2.40465 9.24651 2.7026 8.67445C2.99471 8.11359 3.44533 7.49416 4.0696 6.91791C5.31064 5.77233 7.24344 4.79199 9.99984 4.79199C12.7562 4.79199 14.689 5.77233 15.9301 6.91791C16.5543 7.49416 17.005 8.11359 17.2971 8.67445C17.595 9.24651 17.7082 9.71832 17.7082 10.0003C17.7082 10.2823 17.595 10.7541 17.2971 11.3262C17.005 11.8871 16.5543 12.5065 15.9301 13.0827C14.689 14.2283 12.7562 15.2087 9.99984 15.2087C7.24344 15.2087 5.31064 14.2283 4.0696 13.0827C3.44533 12.5065 2.99471 11.8871 2.7026 11.3262C2.40465 10.7541 2.2915 10.2823 2.2915 10.0003ZM9.99984 3.54199C6.92291 3.54199 4.68903 4.64499 3.22175 5.99941C2.49185 6.67316 1.95288 7.4079 1.59395 8.09703C1.24086 8.77497 1.0415 9.449 1.0415 10.0003C1.0415 10.5517 1.24086 11.2257 1.59395 11.9036C1.95288 12.5928 2.49185 13.3275 3.22175 14.0012C4.68903 15.3557 6.92291 16.4587 9.99984 16.4587C13.0768 16.4587 15.3106 15.3557 16.7779 14.0012C17.5078 13.3275 18.0468 12.5928 18.4057 11.9036C18.7588 11.2257 18.9582 10.5517 18.9582 10.0003C18.9582 9.449 18.7588 8.77497 18.4057 8.09703C18.0468 7.4079 17.5078 6.67316 16.7779 5.99941C15.3106 4.64499 13.0768 3.54199 9.99984 3.54199ZM8.12476 10.0003C8.12476 8.96481 8.96422 8.12535 9.99976 8.12535C11.0353 8.12535 11.8748 8.96481 11.8748 10.0003C11.8748 11.0359 11.0353 11.8753 9.99976 11.8753C8.96422 11.8753 8.12476 11.0359 8.12476 10.0003ZM9.99976 6.87535C8.27387 6.87535 6.87476 8.27446 6.87476 10.0003C6.87476 11.7262 8.27387 13.1253 9.99976 13.1253C11.7256 13.1253 13.1248 11.7262 13.1248 10.0003C13.1248 8.27446 11.7256 6.87535 9.99976 6.87535Z"/>
  </Wrapper>

export const Hidden = props =>
  <Wrapper {...props}>
    <path d="M13.25 10.75 12.25 9.75Q12.333 8.688 11.573 7.948Q10.812 7.208 9.812 7.312L8.812 6.312Q9.104 6.229 9.396 6.177Q9.688 6.125 10 6.125Q11.438 6.125 12.438 7.125Q13.438 8.125 13.438 9.562Q13.438 9.917 13.385 10.229Q13.333 10.542 13.25 10.75ZM15.875 13.333 14.917 12.417Q15.667 11.833 16.25 11.146Q16.833 10.458 17.25 9.562Q16.229 7.479 14.292 6.281Q12.354 5.083 10 5.083Q9.396 5.083 8.885 5.156Q8.375 5.229 7.875 5.375L6.812 4.312Q7.604 4 8.396 3.875Q9.188 3.75 10 3.75Q12.938 3.75 15.302 5.344Q17.667 6.938 18.708 9.562Q18.25 10.688 17.531 11.635Q16.812 12.583 15.875 13.333ZM16.354 18.25 13.021 14.896Q12.396 15.146 11.625 15.281Q10.854 15.417 10 15.417Q7.042 15.417 4.698 13.802Q2.354 12.188 1.292 9.562Q1.729 8.479 2.458 7.542Q3.188 6.604 4.042 5.917L1.729 3.604L2.688 2.667L17.312 17.312ZM4.938 6.812Q4.292 7.333 3.698 8.042Q3.104 8.75 2.729 9.562Q3.75 11.646 5.688 12.865Q7.625 14.083 10 14.083Q10.5 14.083 11.052 14.01Q11.604 13.938 11.938 13.812L10.979 12.854Q10.792 12.917 10.521 12.958Q10.25 13 10 13Q8.562 13 7.562 12Q6.562 11 6.562 9.562Q6.562 9.333 6.615 9.073Q6.667 8.812 6.729 8.604ZM11.375 8.875Q11.375 8.875 11.375 8.875Q11.375 8.875 11.375 8.875Q11.375 8.875 11.375 8.875Q11.375 8.875 11.375 8.875Q11.375 8.875 11.375 8.875Q11.375 8.875 11.375 8.875ZM8.438 10.312Q8.438 10.312 8.438 10.312Q8.438 10.312 8.438 10.312Q8.438 10.312 8.438 10.312Q8.438 10.312 8.438 10.312Q8.438 10.312 8.438 10.312Q8.438 10.312 8.438 10.312Z"/>
  </Wrapper>

export const Magnify = props =>
  <Wrapper {...props}>
    <path d="M26.5024 6.13892C24.8909 4.52485 22.9173 3.31881 20.7458 2.62109C21.4293 3.0978 22.0705 3.63237 22.6624 4.21892C24.9498 6.50374 26.4016 9.49202 26.7838 12.7024C27.1661 15.9128 26.4566 19.1584 24.7696 21.9165C23.0826 24.6745 20.5163 26.7844 17.4841 27.9063C14.4519 29.0282 11.1304 29.0968 8.05444 28.101C11.0027 30.1712 14.6201 31.0607 18.1923 30.5937C21.7644 30.1268 25.0316 28.3374 27.3487 25.5789C29.6658 22.8204 30.8644 19.2934 30.7077 15.6943C30.551 12.0951 29.0504 8.6856 26.5024 6.13892Z" fill="#1B69FF"/>
    <path d="M45.2366 41.164L35.7326 31.66C35.3187 31.2338 34.7792 30.9512 34.1932 30.8537C33.6071 30.7562 33.0052 30.8489 32.4757 31.1182L28.5043 27.1469C31.3354 23.9639 32.8147 19.803 32.6282 15.5472C32.4418 11.2915 30.6042 7.27586 27.5057 4.35267C24.4071 1.42949 20.2913 -0.171183 16.0319 -0.109616C11.7725 -0.0480486 7.70478 1.67091 4.69198 4.68243C1.67918 7.69395 -0.0415009 11.761 -0.104875 16.0204C-0.168249 20.2797 1.43067 24.3961 4.35255 27.496C7.27442 30.5958 11.2892 32.435 15.5449 32.6233C19.8006 32.8115 23.9621 31.334 27.1463 28.5043L31.1178 32.4756C30.8599 33.0078 30.7735 33.6069 30.8706 34.1902C30.9676 34.7736 31.2432 35.3125 31.6595 35.7325L41.1635 45.2365C41.7055 45.7672 42.4349 46.0625 43.1934 46.0585C43.9519 46.0545 44.6782 45.7515 45.2146 45.2151C45.751 44.6788 46.0541 43.9526 46.0582 43.1941C46.0623 42.4356 45.7669 41.7061 45.2364 41.164H45.2366ZM16.32 30.7202C13.472 30.7201 10.688 29.8755 8.32 28.2932C5.952 26.7109 4.10638 24.462 3.01651 21.8308C1.92664 19.1996 1.64147 16.3043 2.19707 13.511C2.75266 10.7177 4.12407 8.15191 6.13787 6.13803C8.8433 3.46137 12.4983 1.96453 16.3041 1.97469C20.1098 1.98485 23.7568 3.50118 26.4479 6.19226C29.139 8.88333 30.6554 12.5303 30.6656 16.3361C30.6758 20.1418 29.179 23.7969 26.5023 26.5023C25.1683 27.8437 23.5814 28.9071 21.8336 29.6311C20.0858 30.3551 18.2118 30.7253 16.32 30.7202ZM43.8787 43.8789C43.6987 44.0589 43.4546 44.16 43.2 44.16C42.9454 44.16 42.7013 44.0589 42.5213 43.8789L33.0173 34.3749C32.9275 34.2859 32.8562 34.18 32.8075 34.0634C32.7587 33.9467 32.7335 33.8216 32.7332 33.6952C32.733 33.5688 32.7577 33.4435 32.806 33.3267C32.8542 33.2098 32.9251 33.1037 33.0145 33.0143C33.1039 32.9249 33.2101 32.8541 33.327 32.8058C33.4438 32.7576 33.569 32.7329 33.6955 32.7332C33.8219 32.7335 33.947 32.7588 34.0636 32.8075C34.1803 32.8563 34.2861 32.9277 34.3751 33.0175L43.8791 42.5215C44.0591 42.7015 44.1602 42.9456 44.1602 43.2002C44.1602 43.4547 44.0591 43.6989 43.8791 43.8789H43.8787Z" fill="#00031A"/>
  </Wrapper>

export const Eye = props =>
  <Wrapper {...props}>
    <path d="M18.8915 21.2346C19.0041 21.4293 19.1659 21.591 19.3608 21.7033C19.5556 21.8156 19.7766 21.8747 20.0015 21.8746C20.2264 21.8752 20.4474 21.8156 20.6415 21.7021C20.7872 21.6182 20.9148 21.5065 21.0172 21.3732C21.1196 21.24 21.1947 21.0878 21.2382 20.9255C21.2818 20.7632 21.2929 20.5939 21.2709 20.4273C21.249 20.2607 21.1944 20.1001 21.1103 19.9546L17.7503 14.7222C17.5805 14.4282 17.3009 14.2136 16.973 14.1257C16.645 14.0378 16.2956 14.0838 16.0015 14.2535C15.7075 14.4232 15.4929 14.7028 15.405 15.0308C15.3171 15.3587 15.363 15.7082 15.5328 16.0022L18.8915 21.2346Z" fill="#00031A"/>
    <path d="M43.3594 21.7021C43.5535 21.8156 43.7745 21.8752 43.9994 21.8746C44.2243 21.8747 44.4453 21.8156 44.6401 21.7033C44.835 21.591 44.9968 21.4293 45.1094 21.2346L48.4694 16.0022C48.6391 15.7082 48.6851 15.3587 48.5972 15.0308C48.5093 14.7028 48.2947 14.4232 48.0007 14.2535C47.7066 14.0838 47.3572 14.0378 47.0292 14.1257C46.7013 14.2136 46.4217 14.4282 46.2519 14.7222L42.8919 19.9546C42.8078 20.1 42.7531 20.2606 42.731 20.4271C42.709 20.5937 42.72 20.763 42.7634 20.9252C42.8068 21.0875 42.8818 21.2397 42.9841 21.373C43.0863 21.5063 43.2139 21.6181 43.3594 21.7021Z" fill="#00031A"/>
    <path d="M32 19.2004C32.3394 19.2004 32.665 19.0655 32.9051 18.8255C33.1451 18.5854 33.28 18.2599 33.28 17.9204V7.68039C33.28 7.34091 33.1451 7.01534 32.9051 6.77529C32.665 6.53525 32.3394 6.40039 32 6.40039C31.6605 6.40039 31.3349 6.53525 31.0949 6.77529C30.8548 7.01534 30.72 7.34091 30.72 7.68039L30.72 17.9204C30.72 18.2599 30.8548 18.5854 31.0949 18.8255C31.3349 19.0655 31.6605 19.2004 32 19.2004Z" fill="#00031A"/>
    <path d="M64 40.6406C64 37.974 49.6738 24.6406 32 24.6406C14.3262 24.6406 0 37.974 0 40.6406C0 43.3073 14.3262 56.6406 32 56.6406C49.6738 56.6406 64 43.3073 64 40.6406Z" fill="#1B69FF"/>
    <path d="M32.0001 47.0402C30.7343 47.0402 29.4969 46.6649 28.4445 45.9616C27.392 45.2584 26.5717 44.2589 26.0873 43.0894C25.6029 41.92 25.4761 40.6331 25.7231 39.3917C25.97 38.1502 26.5796 37.0098 27.4746 36.1148C28.3697 35.2197 29.51 34.6102 30.7515 34.3632C31.993 34.1163 33.2798 34.243 34.4493 34.7274C35.6187 35.2118 36.6183 36.0321 37.3215 37.0846C38.0247 38.1371 38.4001 39.3744 38.4001 40.6402C38.3982 42.337 37.7233 43.9638 36.5235 45.1636C35.3236 46.3634 33.6969 47.0383 32.0001 47.0402Z" fill="white"/>
  </Wrapper>

export const Aspera = props =>
  <Wrapper {...props} viewBox = "0 0 32 32">
    <path d="M16 2.6665C8.63616 2.6665 2.66663 8.63604 2.66663 15.9998C2.66663 19.0341 3.68019 21.8317 5.38721 24.0724C5.50255 23.8294 5.61795 23.5896 5.73176 23.3561C5.88502 20.0867 7.20302 12.1684 11.249 6.65117C12.8429 5.67032 13.5479 6.03814 13.7011 6.34465L16.7662 13.2412C19.0618 12.0435 24.3311 9.58811 27.431 9.13226C25.0983 5.25798 20.8518 2.6665 16 2.6665Z"/>
    <path d="M28.0264 10.2357C25.5243 11.0472 21.5531 12.7704 17.5138 15.3868L22.7433 25.655H19.6781L15.4994 16.7661C12.4585 18.9678 9.5118 21.678 7.26433 24.8887C7.08761 25.1412 6.91825 25.381 6.756 25.6086C9.15293 27.915 12.4108 29.3332 16 29.3332H29.3333V15.9998C29.3333 13.9352 28.864 11.9802 28.0264 10.2357Z"/>
    <path d="M8.33712 20.1378C9.44057 18.7891 12.8837 15.8977 14.4674 14.6205C13.7318 12.5362 12.5772 9.92066 12.0153 9.10329C9.68578 12.6588 8.54147 18.0943 8.33712 20.1378Z"/>
  </Wrapper>

export const CineSend = props =>
  <Wrapper viewBox="0 0 32 32" {...props}>
    <g transform="translate(0.000000,32.000000) scale(0.100000,-0.100000)" fill="#FF0000" stroke="none">
      <path d="M223 229 c-90 -22 -106 -39 -73 -74 18 -19 19 -24 6 -34 -9 -7 -45 -17 -81 -21 -37 -5 -69 -11 -72 -14 -15 -15 13 -17 107 -8 116 11 129 20 100 69 -23 39 -15 48 58 64 31 7 52 18 52 26 0 16 4 16 -97 -8z"/>
    </g>
  </Wrapper>

export const Upload = props => 
  <Wrapper {...props}>
    <path d="M12.5 1.29346L11.5557 2.45576L14.8486 6.99996H13.5C13.3674 6.99996 13.2402 7.05264 13.1464 7.1464C13.0527 7.24017 13 7.36735 13 7.49996V17.5C13 17.6326 12.9473 17.7597 12.8536 17.8535C12.7598 17.9473 12.6326 18 12.5 18H10V20H15V7.99996H17.9489L12.5 1.29346Z" fill="#1B69FF"/>
    <path d="M15.5 23H9.5C9.36739 23 9.24021 23.0527 9.14645 23.1464C9.05268 23.2402 9 23.3674 9 23.5C9 23.6326 9.05268 23.7598 9.14645 23.8536C9.24021 23.9473 9.36739 24 9.5 24H15.5C15.6326 24 15.7598 23.9473 15.8536 23.8536C15.9473 23.7598 16 23.6326 16 23.5C16 23.3674 15.9473 23.2402 15.8536 23.1464C15.7598 23.0527 15.6326 23 15.5 23Z" fill="#1B69FF"/>
    <path d="M19.3832 8.18008L12.884 0.180079C12.8371 0.123791 12.7783 0.0785061 12.712 0.0474293C12.6456 0.0163526 12.5732 0.000244141 12.4999 0.000244141C12.4267 0.000244141 12.3543 0.0163526 12.2879 0.0474293C12.2215 0.0785061 12.1628 0.123791 12.1159 0.180079L5.61679 8.18008C5.55597 8.25309 5.51722 8.34193 5.50508 8.43617C5.49293 8.53042 5.50789 8.62617 5.54821 8.71222C5.58853 8.79826 5.65253 8.87104 5.73272 8.92202C5.81291 8.97301 5.90597 9.00008 6.00099 9.00008H8.99999V19.5001C8.99999 19.8979 9.15802 20.2794 9.43933 20.5607C9.72063 20.842 10.1022 21.0001 10.5 21.0001H14.5C14.8978 21.0001 15.2793 20.842 15.5606 20.5607C15.842 20.2794 16 19.8979 16 19.5001V9.00008H18.999C19.094 9.00008 19.1871 8.97301 19.2673 8.92202C19.3475 8.87104 19.4115 8.79826 19.4518 8.71222C19.4921 8.62617 19.507 8.53042 19.4949 8.43617C19.4828 8.34193 19.444 8.25309 19.3832 8.18008ZM15 8.00008V19.5001C15 19.6327 14.9473 19.7599 14.8535 19.8536C14.7598 19.9474 14.6326 20.0001 14.5 20.0001H10.5C10.3674 20.0001 10.2402 19.9474 10.1464 19.8536C10.0527 19.7599 9.99999 19.6327 9.99999 19.5001V8.00008H7.05129L12.5 1.29358L17.9489 8.00008H15Z" fill="#00000A"/>
  </Wrapper>

export const NoIcon = () => <Wrapper />
