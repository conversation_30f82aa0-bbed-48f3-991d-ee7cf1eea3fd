import React from "react"
import Icon from "../icon";
import { ChevronRight } from "../icons"
import Highlighter from "react-highlight-words"

const Highlight = ({ children }) => (
  <span className="text-primary-500">{children}</span>
);

const SearchRow = ({ item, key, search, onResultClick }) => {
  return (
    <div key={key} className='flex flex-row w-full py-2 px-2 border-t space-x-2 hover:bg-gray-100 cursor-pointer' onClick={() => onResultClick(item) }>
      <Icon icon={item.icon} fontSize='24px' className='text-gray-600'/>
      <div className='flex flex-row space-x-1 items-center'>
        {Array.isArray(item.name)
          ? item.name.map((name, index) =>
            <>
              <Highlighter
                highlightTag={Highlight}
                searchWords={search.split(" ")}
                autoEscape={true}
                textToHighlight={name}
               />
              {index !== item.name.length - 1 && <ChevronRight className='h-4 w-4'/>}
            </>
          )
          : <Highlighter
            highlightTag={Highlight}
            searchWords={search.split(" ")}
            autoEscape={true}
            textToHighlight={item.name}
          />
        }
      </div>
      <span className="text-gray-500">{item.type}</span>
      <span className="text-gray-500">{item.note}</span>
    </div>
  )
}

export default SearchRow
