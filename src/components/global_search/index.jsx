import React, { useEffect, useState } from "react"
import <PERSON>actD<PERSON> from "react-dom"
import { CrossCircle, NoIcon, Search } from "../icons"
import Input from "../input"
import Status from "../status"
import BeginSearch from "./begin_search"
import SearchRow from "./search_row"
import NoResults from "./no_results"

const GlobalSearch = ({
  value = "",
  onValueChange = () => {},
  searchMessage = "Begin typing to start",
  onClose = null,
  pending = false,
  results = [],
  onResultClick = () => {},
  className = "",
}) => {
  const [closing, setClosing] = useState(false)

  const close = e => {
    setClosing(true)
    setTimeout(() => {
      onClose(e)
    }, 200)
  }

  const onKeyDown = e => {
    if (e.keyCode === 27) {
      close(e)
    }
  }

  useEffect(() => {
    document.addEventListener("keydown", onKeyDown, false)
    return () => {
      document.removeEventListener("keydown", onKeyDown, false)
    }
  })

  const content = (
    <div className={`${closing ? 'animate-fade-out' : 'animate-fade-in'} overflow-auto fixed top-0 bottom-0 z-10 h-full w-full bg-overlay flex justify-center`} onClick={close}>
      <div className={`${className} min-w-[550px] max-w-5xl h-fit my-40 rounded bg-white shadow-lg`} onClick={e => e.stopPropagation() && e.preventDefault()}>
        <div className={`text-gray-900 border-gray-200 flex items-center ${results.length === 0 ? 'border-b' : ''}`}>
          <div className='flex flex-row w-full px-2 py-1'>
            <Input
              input={{
                leftIcon: {
                  icon: Search,
                },
                rightIcon: {
                  icon: value.length > 0 ? CrossCircle : NoIcon,
                  onClick: () => onValueChange(""),
                }
              }}
              border={false}
              value={value}
              onChange={(res) => onValueChange(res.target.value)}
              autoFocus
            />
          </div>
        </div>
        <div className='flex-auto max-h-96 overflow-y-scroll'>
          {pending
            ? <Status pending={pending} className='py-10'/>
            : value.length > 0
              ? results.length > 0
                ? results.map((item, index) => (<SearchRow item={item} key={index} search={value} onResultClick={onResultClick}/>))
                : <NoResults/>
              : <BeginSearch searchMessage={searchMessage}/>
          }
        </div>
      </div>
    </div>
  )

  return ReactDOM.createPortal(content, document.body)
}

export default GlobalSearch
