import React from "react"
import Status from "../status"
import Icon from "../icon"
import Button from "../button"
import { Loader } from "../icons"
import humanFileSize from "../helpers/human_file_size"
import iconMapper from "../helpers/icon_source_file_mapper"

const Complete = ({
  icon,
  file,
  pending,
  completionPending,
  status,
  onRemove,
  setUploadingFile,
  setUploadingMethod,
}) => {
  return (
    <div className={`w-full h-full flex flex-col justify-center items-center p-1 rounded text-xs`}>
      <Status pending={pending} pendingMessage={status}>
        <div className="flex items-center w-full space-x-2"> 

          <div className="w-full">
            {file ? (
              Array.isArray(file) ? (
                file.map((item, index) => (
                  <div
                    key={index}
                    className="w-full flex items-center space-x-2 truncate text-start text-sm font-normal text-gray-900"
                  > 
                    <Icon icon={icon || iconMapper(item?.extension || "")} /> <span>{item.name}</span> <span>{item?.size && `(${humanFileSize(item?.size)})`} </span>
                  </div>
                ))
              ) : typeof file === "object" ? (
                <span className="w-full flex items-center space-x-2 truncate text-start text-sm font-normal text-gray-900">
                  {completionPending && (
                    <Loader className="animate-spin h-4 w-4 text-primary-500" />
                  ) }
                  <Icon icon={icon || iconMapper(file?.extension || file?.name.split('.').pop() || "")} /> <span>{file.name}</span> <span>{file?.size && `(${humanFileSize(file?.size)})`}</span>
                </span>
              ) : null
            ) : null} 
          </div>
          <Button
            type="neutral"
            minimal={true}
            size={"small"}
            icon="clear"
            onClick={() => {
              if (window.confirm("Are you sure you want to delete this file?")) {
                onRemove()
                setUploadingFile(null)
                setUploadingMethod("default")
              }
            }}
          />
        </div>
      </Status>
    </div>
  )
}

export default Complete
