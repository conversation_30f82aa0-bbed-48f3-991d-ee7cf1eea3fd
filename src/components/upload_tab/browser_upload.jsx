import React, { useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import Status from '../status'
import Icon from "../icon"
import { Upload } from "../icons"
import { initializeUppyUpload, startUppyUpload } from '../helpers/uppy'
import { isValidFileType } from '../helpers/file_type'

const uploadDefaults = {
  icon: Upload,
  message: "Upload from disk",
  dropMessage: "Drop file here!",
  accept: {},
  multiple: false,
  publicURL: null,
  onFileSelected: null,
  specifications: {}
}

const BrowserUpload = ({
  upload,
  callbacks,
  pending,
  setPending,
  className = null,
}) => {
   
  const {
    message,
    icon,
    dropMessage,
    accept,
    multiple,
    publicURL,
    apiURL,
    specifications
  } = { ...uploadDefaults, ...upload }
  
  // Once the file is dropped...
  const onDrop = useCallback(acceptedFiles => {

    if (acceptedFiles.length === 0) {
      return
    }

    const acceptedFile = acceptedFiles[0]

    //Check file type validity
    isValidFileType(acceptedFile).then((isValid) => {
      if(isValid) {
         
        // If there is a public destination URL present, we can upload directly to that URL.
        if (publicURL) {
          startUppyUpload(acceptedFile, `${publicURL}/${acceptedFile.name}`, callbacks)
          return
        }

        // Otherwise, we use the API URL to create an upload and handle S3 signatures, etc.
        setPending(true)
        initializeUppyUpload(acceptedFile, specifications, callbacks, apiURL)

      } else {
        console.error("Invalid file type")

        if (typeof onError === "function") {
          onError("Invalid file type")
        }
        
      }
    })

  }, [publicURL, callbacks, specifications, apiURL, setPending])
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop, multiple, accept })

  return (
    <div {...getRootProps()} className={`${className} relative group`}>
      <input {...getInputProps()} />
      {isDragActive ?
        <div className={`absolute bg-black/80 inset-0 text-white flex justify-center items-center p-4`}>
          {dropMessage}
        </div> : null}
      <div className={`w-full h-full flex flex-col justify-center items-center p-4 bg-gray-50 hover:bg-gray-100
        border-2 border-primary-500 text-primary-500 rounded border-dashed
        group-hover:text-primary-600 group-hover:border-primary-600`}>
        <Status pending={pending}>
          <Instructions
            icon={icon}
            message={message}/>
        </Status>
      </div>
    </div>
  )
}

const Instructions = ({ icon, message }) =>
  <div className='my-10 flex flex-col items-center justify-center'>
    {icon && <Icon className={'h-8 w-8 mb-4'} icon={icon} />}
    {message && <p className={`text-center text-sm`}>{message}</p>}
  </div>

export default BrowserUpload
