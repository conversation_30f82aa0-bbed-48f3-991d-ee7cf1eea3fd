import React from 'react'
import Status from '../status'
import Icon from '../icon'
import Button from '../button'
import humanFileSize from '../helpers/human_file_size'
import ProgressBar from '../progress_bar'
import { cancelUppy } from '../helpers/uppy'
import { stopAsperaUpload } from '../helpers/aspera'

const convertToTimecode = duration => {
  var seconds = Math.floor((duration / 1000) % 60),
    minutes = Math.floor((duration / (1000 * 60)) % 60),
    hours = Math.floor((duration / (1000 * 60 * 60)) % 24)

  hours = (hours < 10) ? "0" + hours : hours
  minutes = (minutes < 10) ? "0" + minutes : minutes
  seconds = (seconds < 10) ? "0" + seconds : seconds

  return hours + ":" + minutes + ":" + seconds
}

const Progress = ({ icon, file, pending, status, progress, onCancel, callbacks, uploadingMethod, setUploadingFile, setUploadingMethod }) => {
  const percentage = progress && progress.current_bytes ? parseInt(progress.current_bytes / progress.total_bytes * 100) : 0
  const transferRate = progress && progress.transfer_rate_in_mbps ? parseInt(progress.transfer_rate_in_mbps) : null
  const timeRemaining = progress && progress.remaining_time_in_ms ? convertToTimecode(progress.remaining_time_in_ms) : null

  return (
    <div className={`w-full h-full flex flex-col justify-center items-center p-1 rounded text-xs`}>
      <Status pending={pending} pendingMessage={status}>
        <div className='flex items-center w-full space-x-2'>
          {icon && <Icon icon={icon} className="text-gray-600"/>}
          <div className='w-full'>
            {file ? <span className='w-full truncate text-start text-sm font-normal text-gray-900'>{file.name}</span> : null}
            {progress && <ProgressBar completed={percentage}/>}
            <div className='w-full text-start text-xs text-gray-600'>
              {(progress && progress.current_bytes) && <div className='flex items-center space-x-2'>
                <span>{`Transferred: ${humanFileSize(progress.current_bytes)} / ${humanFileSize(progress.total_bytes)}`}</span>
                {transferRate ? <span>({transferRate} mbps)</span> : null}
              </div> }
              {timeRemaining ? <div>Time remaining: {timeRemaining}</div> : null}
            </div>
          </div>
          <Button
            type="neutral"
            minimal={true}
            size={"small"}
            icon="clear"
            onClick={() => {
              if (window.confirm("Are you sure you want to cancel this upload?")) {
                (uploadingMethod === 'aspera') ? stopAsperaUpload(callbacks) : cancelUppy()
                onCancel()
                setUploadingFile(null)
                setUploadingMethod('default')
              }
            }}/>
        </div>
      </Status>
    </div>
  )
}

export default Progress
