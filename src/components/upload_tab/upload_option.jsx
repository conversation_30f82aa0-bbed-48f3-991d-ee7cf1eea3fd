import React from 'react'
import Icon from '../icon'

const UploadOption = ({ icon = null, text = "", onClick = () => {} }) =>
  <div 
    className={`cursor-pointer py-6 px-6 space-y-6 w-full h-fit flex flex-col items-center rounded-lg border shadow-sm border-gray-200 bg-white hover:bg-gray-100`}
    onClick={onClick}>
    {icon && 
      <div className='w-8 h-8'>
        <Icon icon={icon} iconClassName='w-full h-full' />
      </div>}
    <div className='text-sm font-subheader text-center'>{text}</div>
  </div>

export default UploadOption
