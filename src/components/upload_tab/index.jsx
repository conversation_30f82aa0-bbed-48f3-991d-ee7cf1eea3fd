import React, { useEffect, useMemo, useState } from "react"
import { Aspera, CineSend } from "../icons"
import BrowserUpload from "./browser_upload"
import UploadOption from "./upload_option"
import Progress from "./progress"
import Complete from "./complete"
import { initializeAsperaUpload } from "../helpers/aspera"
import SelectFilesModal from "../select_files_modal/select_files_modal"

const uploadDefaults = {
  onFileSelected: null,
  onProgress: () => {},
  onComplete: () => {},
  onCancel: () => {},
  onRemove: () => {},
  onError: () => {},
  specifications: {},
}

const UploadTab = ({
  aspera = false,
  cinesend = false,
  google = false,
  dropbox = false,
  multiple = false,
  extensions = null,
  upload,
  file = null,
  icon = null,
  completionPending = false,
}) => {
  const { onFileSelected, onProgress, onComplete, onCancel, onRemove, onError, apiURL, specifications, accept } = {
    ...uploadDefaults,
    ...upload,
  }

  const [status, setStatus] = useState()
  const [pending, setPending] = useState(false)
  const [progress, setProgress] = useState(null)
  const [uploadingFile, setUploadingFile] = useState(file)
  const [uploadingMethod, setUploadingMethod] = useState("default")
  const [showSelectFoldersModal, setShowSelectFoldersModal] = useState(false)
  const [completionPendingFlag, setCompletionPendingFlag] = useState(completionPending)

  const asperaUpload = () => {
    setPending(true)
    setUploadingMethod("aspera")
    initializeAsperaUpload(apiURL, specifications, callbacks, accept)
  }

  const cinesendUpload = (files) => {
    setUploadingMethod("cinesend")
    setPending(true)
    setStatus("Importing Files")
    cinesend.onImportFilesClick(files, onCineSendImportComplete, onCineSendImportError)
  }

  const onCineSendImportComplete = (files) => {
    setStatus(null)
    setPending(false)
    setCompletionPendingFlag(false)
    setShowSelectFoldersModal(false)
    setUploadingFile(files)
  }
  const onCineSendImportError = (error) => {
    console.error("Error importing: ", error)
    setPending(false)
    setStatus(null)
  }

  const callbacks = useMemo(
    () => ({
      onFileSelected: (file) => {
        setUploadingFile(file)
        if (typeof onFileSelected === "function") {
          onFileSelected(file)
        }
      },
      onStatusChange: (status) => {
        setStatus(status)
      },
      onProgress: (progress) => {
        setPending(false)
        setProgress(progress)
        onProgress(progress)
      },
      onComplete: (file = null, destinationURL = null, uploadId = null) => {
        onComplete(file, destinationURL, uploadId)
        setProgress()
        setStatus(null)
      },
      onError: (error) => {
        onError(error)
      },
    }),
    [onFileSelected, onComplete, onProgress, onError]
  )

  useEffect(() => {
    setCompletionPendingFlag(completionPending)
  }, [completionPending])

  return (
    <div className={`py-6 px-6 space-y-3 w-full h-fit rounded-lg border shadow-sm bg-white `}>
      {status ? (
        <Progress
          icon={icon}
          file={uploadingFile}
          pending={pending}
          status={status}
          progress={progress}
          onCancel={onCancel}
          callbacks={callbacks}
          uploadingMethod={uploadingMethod}
          setUploadingFile={setUploadingFile}
          setUploadingMethod={setUploadingMethod}
        />
      ) : uploadingFile ? (
        <>
          <Complete
            icon={icon}
            file={uploadingFile}
            pending={pending}
            completionPending={completionPendingFlag}
            status={status}
            onRemove={onRemove}
            setUploadingFile={setUploadingFile}
            setUploadingMethod={setUploadingMethod}
          />
          {cinesend && multiple && (
            <>
              <UploadOption
                icon={CineSend}
                text="Import additional files from CineSend"
                onClick={() => setShowSelectFoldersModal(true)}
              />
              {showSelectFoldersModal && (
                <SelectFilesModal
                  {...cinesend}
                  multiple={multiple}
                  extensions={extensions}
                  onImportFilesClick={cinesendUpload}
                  header="Import from CineSend Files"
                  onClose={() => setShowSelectFoldersModal(false)}
                />
              )}
            </>
          )}
        </>
      ) : (
        <div className="space-y-4">
          <BrowserUpload callbacks={callbacks} pending={pending} setPending={setPending} upload={upload} />
          <div className="flex space-x-4">
            {cinesend && (
              <>
                <UploadOption
                  icon={CineSend}
                  text="Import from CineSend"
                  onClick={() => {
                    if (typeof cinesend?.getRootFolderFileList === "function") {
                      cinesend.getRootFolderFileList(() => {
                        setShowSelectFoldersModal(true)
                      })
                    } else {
                      setShowSelectFoldersModal(true)
                    }
                  }}
                />
                {showSelectFoldersModal && (
                  <SelectFilesModal
                    {...cinesend}
                    multiple={multiple}
                    extensions={extensions}
                    onImportFilesClick={cinesendUpload}
                    header="Import from CineSend Files"
                    onClose={() => setShowSelectFoldersModal(false)}
                  />
                )}
              </>
            )}
            {google && <UploadOption icon={Aspera} text="Import from Google Drive" />}
            {dropbox && <UploadOption icon={Aspera} text="Import from Dropbox" />}
            {aspera && <UploadOption icon={Aspera} text="Upload with IBM Aspera" onClick={asperaUpload} />}
          </div>
        </div>
      )}
    </div>
  )
}

export default UploadTab
