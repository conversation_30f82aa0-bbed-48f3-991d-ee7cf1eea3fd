import React, {useEffect} from "react"
import DatePicker from '../date_picker'
import dayjs from "dayjs"

function DateRange({
  startDate = null,
  changeStartDate = () => {},
  endDate = null,
  changeEndDate = () => {},
  icon = null,
  label = null,
  disableStart = false,
  disableEnd = false,
}) {
  useEffect(() => {
    if (startDate && endDate && startDate > endDate) {
      changeStartDate(endDate)
      changeEndDate(startDate)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startDate, endDate])

  return (
    <div className='border-top border-gray-5 pt1'>
      <label className={`text-gray-900`}>
        {label}
        <div className='flex items-center'>
          <div>
            <DatePicker 
              label={'Start Date'}
              icon={icon}
              date={startDate}
              onChange={changeStartDate}
              startDate={startDate}
              endDate={endDate}
              disabled={disableStart}
            />
          </div>
          <div className='flex items-center flex-auto mt-3'>
            <div className='w-full h-1 bg-gray-300 mx-2 mt-3'/>
            <div className='inline-block mt-3 font-bold whitespace-nowrap text-gray-900'>
              {calculateDuration(startDate, endDate)}
            </div>
            <div className='w-full h-1 bg-gray-300 ml-2 mt-3'/>
            <div className='border-t-8 border-l-[12px] border-b-8 border-l-gray-300 border-b-transparent border-t-transparent mt-3 mr-2'/>
          </div>
          <div>
            <DatePicker
              label={'End Date'}
              icon={icon}
              date={endDate}
              onChange={changeEndDate}
              startDate={startDate}
              endDate={endDate}
              disabled={disableEnd}
            />
          </div>
        </div>
      </label>
    </div>
  )
}

function calculateDuration(start, end){
  return (start === null || end === null) ? "N/A" : <span>{`${dayjs(end).diff(start, 'hour')} hours`}</span>
}

export default DateRange
