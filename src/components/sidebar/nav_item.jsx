import React from "react"
import { ChevronDown } from "../icons"
import Icon from "../icon"

const NavItem = ({ link, isParent, selected, showChildren, onClick, small = false, collapsed = false }) =>
  <div
    onClick={onClick}
    className={
      `cursor-pointer flex items-center justify-between relative ${small ? 'h-10' : 'h-12'} w-full border-l-4 text-gray-700 px-4 font-normal dark:text-gray-400
      ${selected
          ? `dark:bg-gray-50/[.08] bg-gray-50 border-primary-500 `
          : `dark:border-primary-900 border-white`}`
    }>
    <div className='flex'>
      <div className="w-6">
        {link.icon && <Icon icon={link.icon} tooltip={{ text: link.text }}/>}
      </div>
      {!collapsed &&
      <span
        className={`mx-3 leading-6 ${selected
          ? `dark:text-gray-200 text-primary-500`
          : `dark:text-gray-400 text-gray-700`}`}>
        {link.text}
      </span>}
    </div>
    {isParent && !collapsed && <span className={`transition-all duration-500 ${showChildren ? 'rotate-180' : ''}`}>
      <ChevronDown className={`h-4 w-4 dark:text-gray-400 text-gray-700`}/>
    </span>}
  </div>

export default NavItem
