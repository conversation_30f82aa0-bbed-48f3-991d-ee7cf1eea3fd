import React from 'react'
import Avatar from "../avatar"
import ButtonDropdown from "../button_dropdown"
import { ChevronDown } from "../icons"

const Footer = ({ name, organization, photoURL, dropdownContent, collapsed = false}) =>
  <ButtonDropdown
    button={{
      className: 'w-full',
      component: (
        <div className={`flex items-center w-full ${collapsed ? 'justify-center' : 'justify-between'}`}>
          <div className="flex items-center space-x-3">
            <Avatar url={photoURL} name={name}/>
            {!collapsed && 
            <div className='flex flex-col max-w-[120px] break-words'>
              <span className='font-subheader text-sm text-gray-200 leading-4'>
                {name}
              </span>
              <span className='text-xs text-gray-400 font-light leading-4'>
                {organization}
              </span>
            </div>}
          </div>
          {!collapsed && <ChevronDown className='h-4 w-4 text-gray-400'/>}
        </div>
      )
    }}
    dropdown={{
      anchor: "left",
      content: dropdownContent,
      clickCloses: true,
    }}/>

export default Footer