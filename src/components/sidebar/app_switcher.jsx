import React from 'react'
import Icon from '../icon'

const AppSwitcher = ({ apps, appName }) =>
  <div className='flex flex-col m-2'>
    {apps.map((app, index) =>
      <div
        key={index}
        onClick={e => {
          if (app.name !== appName) {
            e.preventDefault()
            e.stopPropagation()
            app.onClick()
          }
        }}
        className={
          `w-[320px] p-4 rounded flex items-center
          ${index !== 0 ? 'border-t border-gray-200'  : ''}
          ${app.name === appName ? 'bg-gray-100' : 'hover:bg-gray-100 cursor-pointer '}`
        }>  
        <div className='rounded-full h-10 w-10 flex items-center justify-center bg-red-500'>
          <Icon icon={app.icon} className='w-6 h-6 text-white'/>
        </div>
        <div className='w-[232px] mx-4 whitespace-normal'>
          <div className='font-subheader text-sm font-medium text-gray-900'>{app.name}</div>
          <div className='text-xs text-gray-600'>{app.description}</div>
        </div>
      </div>)}
  </div>

export default AppSwitcher