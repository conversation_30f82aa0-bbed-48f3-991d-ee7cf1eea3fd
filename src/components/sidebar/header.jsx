import React from "react"
import { CineSendLogo } from "../logos"
import { CineSend } from "../icons"
import AppSwitcher from "./app_switcher"
import ButtonDropdown from "../button_dropdown"
import { Expand } from "../icons"

const Switcher = ({
  appName,
  apps,
  logo = null,
  collapsedLogo = null,
  onLogoClick = null,
  collapsed = false,
}) => {
  const Label = () => <span className={`dark:text-gray-400 text-gray-800 font-subheader text-sm mr-1`}>{appName}</span>
  return (
    <div className={`flex items-center ${collapsed ? 'pl-4 py-4' : 'pl-6 py-6'}`}>
      <div onClick={onLogoClick}>
        {collapsed ? collapsedLogo ? collapsedLogo : <CineSend className={`h-5 ${typeof onLogoClick === 'function' ? 'cursor-pointer' : null}`} />
          : logo ? logo : <CineSendLogo className={`h-5 ${typeof onLogoClick === 'function' ? 'cursor-pointer' : null}`} />
        }
      </div>
      {appName && apps && <ButtonDropdown
        button={{
          component:
            <div className='flex items-center ml-2'>
              {!collapsed && <Label />}
              <Expand className={`h-3 w-3 dark:text-gray-200 text-gray-600`} />
            </div>
        }}
        dropdown={{
          anchor: "left",
          content: <AppSwitcher apps={apps} appName={appName} />,
          clickCloses: false,
          classToClose: 'dropdown-wrapper'
        }} />}
      {appName && !apps ? <Label /> : null}
    </div>
  )
}

export default Switcher