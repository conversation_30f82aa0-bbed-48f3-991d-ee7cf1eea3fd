import React, { useEffect, useState } from "react";
import NavItem from './nav_item'
import Header from './header'
import Footer from './footer'
import Icon from '../icon'
import useIsMobile from "../hooks/use_is_mobile";

const Sidebar = ({
  links = [],
  header = null,
  footer = null,
  secondaryHeader = null,
  pathname = "",
  topMargin = "top-0",
  small = false,
  toggleKey = null,
  lockExpanded = null,
}) => {
  const isMobile = useIsMobile()
  const savedIsCollapsed = isMobile ? true : () => {
    const storedState = localStorage.getItem(extractDomainFromUrl())
    return storedState === 'true'
  }
  const [expanded, setExpanded] = useState(!savedIsCollapsed);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(savedIsCollapsed)
  const [closedItems, setClosedItems] = useState(new Set())
  const pieces = pathname.split("/");
  const prefix = "/" + pieces[1];
  const childPrefix = "/" + pieces[1] + "/" + pieces[2];
  function extractDomainFromUrl() {
    const href = window.location.href
    const parts = href.split("://")
    if (parts.length < 2) {
      return ''
    }
    const domainAndPath = parts[1];
    const domain = domainAndPath.split('/')[0]
    return domain
  }

  useEffect(() => {
    localStorage.setItem(extractDomainFromUrl(), sidebarCollapsed)
  }, [sidebarCollapsed])

  useEffect(() => {
    const handleKeydown = (event) => {
      if (!lockExpanded && event.key === toggleKey) {
        setSidebarCollapsed(!sidebarCollapsed)
        setExpanded(false)
      }
    };
    window.addEventListener('keydown', handleKeydown);
    return () => {
      window.removeEventListener('keydown', handleKeydown);
    };
  }, [sidebarCollapsed, setSidebarCollapsed, toggleKey, setExpanded]);


  return (
    <>
      <aside className={`dark:bg-primary-900 dark:border-gray-600
      ${isMobile ? "z-10 fixed h-screen items-center" : ""} flex border-r bg-white font-subheader text-sm relative`}
      >
        <div className={`flex flex-col justify-between h-full transition-all duration-300 ease-in-out ${sidebarCollapsed ? 'w-16' : 'w-60'}`}>
          <div className="overflow-y-auto">
            {header && <div className="flex justify-between items-center relative pr-2">
              <Header {...header} collapsed={sidebarCollapsed} />
              {!lockExpanded && !sidebarCollapsed && <Icon
                onClick={() => {
                  setSidebarCollapsed(true)
                  setExpanded(false)
                }}
                icon={'keyboard_double_arrow_left'}
                className='text-gray-400 cursor-pointer'
                tooltip={{
                  text: 'Collapse sidebar',
                  direction: 'bottom'
                }} />}
            </div>}
            {secondaryHeader}
            {links
              .filter((link) => link.show !== false)
              .map((link, index) => {
                const isParent = !!link.children;
                const selected = prefix === link.to;
                const isExplicitlyClosed = closedItems.has(link.to);
                const showChildren = link.showChildrenByDefault
                  ? (!!link.children && !isExplicitlyClosed)
                  : (expanded && selected);
                return (
                  <div key={index}>
                    <NavItem
                      small={small}
                      onClick={() => {
                        if (isParent) setSidebarCollapsed(false)
                        if (prefix !== link.to || !isParent) {
                          link.onClick();
                          setExpanded(true);
                        } else {
                          if (link.showChildrenByDefault) {
                            // For showChildrenByDefault items, toggle the closed state
                            setClosedItems(prev => {
                              const newSet = new Set(prev);
                              if (newSet.has(link.to)) {
                                newSet.delete(link.to);
                              } else {
                                newSet.add(link.to);
                              }
                              return newSet;
                            });
                          } else {
                            setExpanded(!expanded);
                          }
                        }
                      }}
                      isParent={isParent}
                      show={true}
                      selected={selected && (!showChildren || !isParent)}
                      showChildren={showChildren}
                      link={link}
                      collapsed={sidebarCollapsed}
                    />
                    {link.children && (
                      <div
                        className={`transition-[max-height] ease-in-out duration-500 overflow-auto ${showChildren ? "max-h-[600px]" : "max-h-0"
                          }`}
                      >
                        {link.children.map((child) => (
                          <NavItem
                            small={small}
                            onClick={child.onClick}
                            key={child.to}
                            selected={childPrefix === child.to}
                            link={child}
                          />
                        ))}
                      </div>
                    )}

                  </div>
                );
              })}

          </div>
          <div className={`${sidebarCollapsed ? "" : "pl-6 pr-4"} py-6`}>
            {footer ? <Footer {...footer} collapsed={sidebarCollapsed} /> : null}
          </div>
        </div>


      </aside>
      {isMobile
        && !lockExpanded
        && !sidebarCollapsed
        && <div className="h-screen w-screen fixed bottom-0 right-0 z-[9] bg-overlay"
          onClick={() => setSidebarCollapsed(true)} >
        </div>}

      {sidebarCollapsed && <div
        className="cursor-pointer flex items-center justify-center rounded shadow-lg px-1 absolute left-16 top-4"
        onClick={() => setSidebarCollapsed(false)}>
        <Icon tooltip={{ text: "Expand sidebar" }} icon="keyboard_double_arrow_right" className="text-gray-400 text-md" />
      </div>}
    </>
  );
};

export default Sidebar
