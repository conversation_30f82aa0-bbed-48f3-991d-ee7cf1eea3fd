import React from 'react';

interface Link {
    text: string
    to: string
}

interface ParentLink extends Link {
    children?: [Link]
    icon: string
    show?: boolean
}

interface Props {
    pathname?: string
    lockExpanded?: boolean
    topMargin?: number
    footer?: {
        name: string
        organization: string
        photoUrl: string | null
        dropdownContent: [{
            text: string
            icon: string
            onClick: () => void
        }]
    }
    header: {
        appName: string
        apps: [{
            name: string
            icon: string 
            description: string
            onClick: () => void
        }]
    }
    links: [ParentLink]
}

declare const SideBar: React.FunctionComponent<Props>

export default SideBar