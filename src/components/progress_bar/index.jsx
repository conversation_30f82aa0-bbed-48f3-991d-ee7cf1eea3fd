import React from 'react'

const ProgressBar = ({ completed, displayPercentage = true }) => 
  <div className='flex items-center w-full h-fit space-x-2 text-primary-500'>
    <div className='bg-gray-200 rounded-full h-1 w-full overflow-hidden'>
      <div className={`h-full bg-primary-500`} style={{ width: `${completed}%`}} />
    </div>
    {(displayPercentage && (completed > 0 && completed < 100))
      ? <span className='text-xs'>{completed.toFixed(2)}%</span>
      : null}
  </div>

export default ProgressBar
