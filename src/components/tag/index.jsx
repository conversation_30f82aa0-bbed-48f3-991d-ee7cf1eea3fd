import React, { useState } from "react"
import uuid from "react-uuid"
import copyToClipboard from 'copy-to-clipboard'
import ReactTooltip from 'react-tooltip'

const dimensionsBySize = {
  small: "text-2xs px-2",
  medium: "text-xs px-2",
  large: "text-sm px-3"
}

const dimensionsBySizeForIcons = {
  small: "text-2xs w-4",
  medium: "text-xs w-5",
  large: "text-sm w-6"
}

const solidColorsByType = {
  primary: "bg-primary-500 text-primary-100",
  neutral: "bg-gray-600 text-gray-100",
  info: "bg-info-500 text-info-100",
  success: "bg-success-500 text-success-100",
  warning: "bg-warning-500 text-warning-100",
  error: "bg-error-500 text-error-100"
}

const outlineColorsByType = {
  primary: "bg-primary-100 text-primary-600",
  neutral: "bg-gray-100 text-gray-600",
  info: "bg-info-100 text-info-600",
  success: "bg-success-100 text-success-600",
  warning: "bg-warning-100 text-warning-600",
  error: "bg-error-100 text-error-600"
}

const iconClassBySize = {
  small: "h-3 w-3",
  medium: "h-4 w-4",
  large: "h-4 w-4",
}

const CopyWrapper = ({
  text = "Copy",
  copy = null,
  direction = "right",
  children,
  includeTooltip = false,
  onClick = null
}) => {
  const [copied, setCopied] = useState(false)
  const value = copy ? copy.value : ""
  const onCopyMessage = copy?.onCopyMessage || "Copied!"
  const uniqueID = uuid()
  return (
    <span
      data-tip
      data-for={uniqueID}
      className='inline-flex'
      onClick={e => {
        if (typeof onClick === "function") {
          e.stopPropagation()
          e.preventDefault()
        }
        if (copy) {
          copyToClipboard(value)
          setCopied(true)
          setTimeout(() => {
            setCopied(false)
          }, 2500)
        }
      }}>
      {children}
      {includeTooltip && <ReactTooltip id={uniqueID} effect='solid' place={direction}>
        <span className='font-content font-normal'>{copied && !value ? "Nothing to copy" : copied ? onCopyMessage : text}</span>
      </ReactTooltip>}
    </span>
  )
}

const Tag = ({
  label = "",
  size = "medium",
  type = "primary",
  outline = false,
  icon = null,
  iconPosition = "left",
  tooltip = null
}) => {
  const Icon = icon
  const pointer = tooltip?.copy ? "cursor-pointer" : ""
  return (
    <CopyWrapper {...tooltip} includeTooltip={tooltip}>
      <div className={`
        ${pointer}
        ${icon && !label ? dimensionsBySizeForIcons[size] : dimensionsBySize[size]}
        ${outline ? outlineColorsByType[type] : solidColorsByType[type]}
        ${iconPosition === 'left' ? '' : 'flex-row-reverse'}
        inline-flex items-center justify-center rounded-xl h-full`}>
        {icon &&
          <Icon className={`${label ? (iconPosition === 'left' ? 'mr-1' : 'ml-1') : ''} ${iconClassBySize[size]}`}/>}
        {label}
      </div>
    </CopyWrapper>
  )
}

export default Tag