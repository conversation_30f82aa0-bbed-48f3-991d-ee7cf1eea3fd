import React from 'react'
import Label from '../input/label'
import { usePaymentInputs } from 'react-payment-inputs'
import images from "react-payment-inputs/images"

const CreditCardInput = ({
  id,
  label = null,
  description = "",
  message = "",
  width = "w-full",
  className = "",
  value,
  onChange = () => {},
  onBlur = () => {},
  disabled = false,
  success = false,
  error = false,
  border = true,
}) => {

  const {
    meta,
    getCardNumberProps,
    getExpiryDateProps,
    getCVCProps,
    getCardImageProps,
  } = usePaymentInputs()

  return (
    <div id={id} className={`${width} ${className} space-y-1 flex flex-col`}>
      {label && <Label label={label}/>}
      {description && <Label label={description} textColor={'text-gray-500'}/>}
      <div className={`
        flex items-center w-full h-10 rounded-lg focus:text-gray-600 ${width}
        ${border ? 'border-solid border border-gray-300 focus:border-blue-500' : ''}
        ${disabled ? 'border-gray-100 text-gray-400 bg-gray-100' : 'hover:border-gray-600'}
        ${success ? 'border-green-500 hover:border-green-600' : ''}
        ${error ? 'border-red-500 hover:border-red-600' : ''}
      `}>
        <svg {...getCardImageProps({ images, className: 'h-10 w-12 px-2 place-self-center' })} />
        <input 
          {...getCardNumberProps({ 
            onChange: (e) => onChange({ ...value, cardNumber: e.target.value }),
            onBlur: (e) => onBlur({ ...value, cardNumber: e.target.value }),
            className: 'border-0 rounded-lg w-full h-full text-sm box-border focus:outline-none focus:text-gray-600' 
          })} 
          value={value.cardNumber} />
        <input 
          {...getExpiryDateProps({
            onChange: (e) => onChange({ ...value, expiry: e.target.value }),
            onBlur: (e) => onBlur({ ...value, expiry: e.target.value }),
            className: 'border-0 rounded-lg w-16 h-full text-sm box-border focus:outline-none focus:text-gray-600' 
          })} 
          value={value.expiry} />
        <input 
          {...getCVCProps({ 
            onChange: (e) => onChange({ ...value, cvc: e.target.value }),
            onBlur: (e) => onBlur({ ...value, cvc: e.target.value }),
            className: 'border-0 rounded-lg w-16 h-full text-sm box-border focus:outline-none focus:text-gray-600' 
          })} 
          value={value.cvc} />
      </div>
      {meta.isTouched && meta.error && <Label label={meta.error} textColor='text-red-500' />}
      {message && <Label label={message} iconPosition="left" textColor={'text-gray-500'}/>}
    </div>
  )
}

export default CreditCardInput
