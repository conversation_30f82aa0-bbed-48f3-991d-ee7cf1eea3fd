import React, { useMemo, useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Legend, Sector } from 'recharts'
import ReactTooltip from 'react-tooltip'
import uuid from 'react-uuid'

const sizeOptions = {
  large: {
    hoverRadiusIncrease: 3,
    height: 400,
    width: 400, 
    outerRadius: 150, 
    innerRadius: 135
  },
  medium: {
    hoverRadiusIncrease: 2,
    height: 250,
    width: 300, 
    outerRadius: 75, 
    innerRadius: 65
  },
  small: {
    hoverRadiusIncrease: 2,
    height: 200,
    width: 200, 
    outerRadius: 40, 
    innerRadius: 32
  }
}

function getSizeConfiguration(size) {
  if (typeof size === 'string') {
    return sizeOptions[size] || sizeOptions.medium
  }
  return size
}

export default function DonutChart({
  data,
  chartHeader,
  onClick,
  size = 'medium',
  className,
  legendHeight = 50,
  hideLegend = false,
  centerText = false,
  chartColor = null,
  tooltip = true
}) {
  const [activeIndex, setActiveIndex] = useState(null)
  const sizeConfig = getSizeConfiguration(size)
  const { innerRadius, outerRadius, width, height, hoverRadiusIncrease } = sizeConfig
  const parentContainerWidth = `min-w-[${width}px]`
  const total = data.reduce((acc, curr) => acc + curr.value, 0)
  const tooltipID = uuid()

  // Deterministically hash obj as hexadecimal color
  const objectToColor = (obj) => {
    const str = JSON.stringify(obj, Object.keys(obj).sort())
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    let color = Math.abs(hash).toString(16).substring(0, 6)
    while (color.length < 6) {
      color += '0'
    }
    return `#${color}`
  }

  const sections = useMemo(() => data.map(item => ({
    ...item,
    color: chartColor|| item.color || objectToColor(item)
  })), [data])

  const onPieEnter = (_, index) => {
    setActiveIndex(index)
  }

  const onPieLeave = () => {
    setActiveIndex(null)
  }

  const renderActiveShape = (props) => {
    const {
      cx, cy, startAngle, endAngle, fill, payload, percent, value
    } = props


    return (
      <g>
        {centerText && <>
          <text x={cx} y={cy - 10} textAnchor="middle" >
            {payload.name}
          </text>
          <text x={cx} y={cy + 20} textAnchor="middle" className='text-xs'>
            {`${value} / ${total} (${(percent * 100).toFixed(1)}%)`}
          </text>
        </>}
        <Sector
          className='cursor-pointer'
          cx={cx}
          cy={cy}
          innerRadius={innerRadius - hoverRadiusIncrease}
          outerRadius={outerRadius + hoverRadiusIncrease}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
      </g>
    )
  }

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const { name, value } = payload[0]
      return (
        <div className="bg-black text-white px-4 py-2 text-center text-sm">
          <p>{name}</p>
          <p>{`${value} / ${total} (${(value / total * 100).toFixed(1)})%`}</p>
        </div>
      )
    }
    return null
  }

  return (
    <>
      <style>
        {`
           .recharts-legend-item {
             cursor: pointer;
           }
           .recharts-layer {
             outline: none !important;
           }
           .recharts-wrapper {
             position: absolute;
           }
           .recharts-legend-wrapper {
            min-height: ${legendHeight}px;
            max-height: ${legendHeight}px; 
            overflow-y: auto; 
          }`}
      </style>
      <div className={`text-center ${parentContainerWidth} ${className}`}>
        {chartHeader}
          <PieChart height={height} width={width}>
            {!hideLegend &&
              <Legend
                onClick={(data) => {
                  setActiveIndex(null)
                  onClick(data.payload)
                }}
                onMouseEnter={onPieEnter}
                onMouseLeave={onPieLeave}
                iconType="circle"
                layout="horizontal"
                verticalAlign="bottom"
                iconSize={10}
                formatter={(value, entry) => {
                  return (<>
                  <span data-tip data-for={tooltipID} className='text-black'>{value}</span>
                  <ReactTooltip id={tooltipID} effect='solid' place={'top'}>
                    <div>
                      <p>{sections[activeIndex]?.name}</p>
                      <p>{`${sections[activeIndex]?.value} / ${total} (${(sections[activeIndex]?.value / total * 100).toFixed(1)}%)`}</p>
                    </div>
                  </ReactTooltip>
                  </>)
                }}
              />
            }
            <Pie
              activeIndex={activeIndex}
              activeShape={renderActiveShape}
              data={sections}
              cx="50%"
              cy="50%"
              innerRadius={innerRadius}
              outerRadius={outerRadius}
              fill="#8884d8"
              dataKey="value"
              onMouseEnter={onPieEnter}
              onMouseLeave={onPieLeave}
              onClick={onClick}
            >
              {sections.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            {tooltip && <Tooltip content={<CustomTooltip />} />}
          </PieChart>
      </div>
    </>
  )
}
