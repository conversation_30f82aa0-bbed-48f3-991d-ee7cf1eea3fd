import React from 'react'
import { Check, Minus } from '../icons'

const Checkbox = ({
  size = "medium", // small, medium, large
  label = null,
  indeterminate = false,
  checked = false,
  disabled = false,
  onChange = () => {},
  className = ""
}) =>
  <label
    className={`${className} inline-flex relative ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'} group items-center`}>
    <input
      type='checkbox'
      checked={checked} 
      disabled={disabled}
      onChange={onChange}
      className={`opacity-0 absolute top-0 left-0 right-0 bottom-0 ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'} `}/>
    <span
      className={`${size === 'small' ? 'w-4 h-4' : size === 'medium' ? 'w-5 h-5' : 'w-6 h-6'} rounded flex justify-center items-center
       ${disabled && (checked || indeterminate)
          ? 'bg-gray-200'
          : disabled
            ? 'border border-gray-200'
            : checked
              ? 'bg-blue-500 group-hover:bg-blue-600 group-active:bg-blue-700' 
              : 'border border-gray-400 group-hover:border-gray-500 group-active:border-gray-600'}`}>
      {checked && !indeterminate
        ? <Check className={`text-white ${size === 'small' ? 'w-3 h-3' : size === 'medium' ? 'w-4 h-4' : 'w-5 h-5'}`}/>
        : checked && indeterminate ? 
          <Minus className={`text-white ${size === 'small' ? 'w-3 h-3' : size === 'medium' ? 'w-4 h-4' : 'w-5 h-5'}`}/>
          : <div className={size === 'small' ? 'w-3 h-3' : size === 'medium' ? 'w-4 h-4' : 'w-5 h-5'}/>}
    </span>
    {label &&
      <span
        className={`
          flex items-center
          ${disabled ? 'text-gray-400' : 'text-gray-700'}
          ${size === 'small'
            ? 'ml-2 text-xs leading-4'
            : size === 'medium'
              ? 'ml-3 text-sm leading-5'
              : 'ml-3 text-base leading-6'}`}>
        {label}
      </span>}
  </label>

export default Checkbox