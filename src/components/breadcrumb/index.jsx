import React, { useState, useEffect, useCallback } from "react"
import PropTypes from "prop-types"
import ButtonDropdown from "../button_dropdown"
import Icon from "../icon"

const Breadcrumb = ({ breadcrumb, onCrumbClick, onAllClick }) => {
  const [width, setWidth] = useState(window.innerWidth)
  const [hidden, setHidden] = useState(false)
  const [transitioning, setTransitioning] = useState(false)
  const expectedHeight = 35

  const handleResize = useCallback(() => {
    setWidth(window.innerWidth)
  }, [])

  useEffect(() => {
    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [handleResize])

  useEffect(() => {
    if (Array.isArray(breadcrumb)) {
      const linkHeights = getLinkHeights()

      if (!linkHeights.every((value) => value === null)) {
        setTransitioning(true)
        setHidden(false)
        setHidden(linkHeights.filter((link) => link > expectedHeight).length > 0)
        setTransitioning(false)
      }
    }
  }, [breadcrumb, width])

  const getLinkHeights = () => {
    return breadcrumb.map((crumb, index) => {
      const el = document.getElementById(`crumb-${index}`)
      return el ? el.clientHeight : null
    })
  }

  const handleCrumbClick = (crumb) => {
    if (onCrumbClick && typeof onCrumbClick === "function") {
      onCrumbClick(crumb)
    }
  }

  const handleAllClick = () => {
    if (breadcrumb.length === 0) {
      return
    }
    if (onAllClick && typeof onAllClick === "function") {
      onAllClick()
    }
  }

  return (
    <div className="flex items-center flex-auto">
      <div
        className={`${breadcrumb.length ? "opacity-50 cursor-pointer hover:bg-gray-300 rounded-full" : ""} px-4 py-0.5`}
        onClick={handleAllClick}>
        All Files
      </div>
      <div className={`flex items-center ${transitioning ? "opacity-0" : ""}`}>
        {hidden && breadcrumb && (
          <div className="flex items-center space-x-2">
            <Icon icon="keyboard_arrow_right" className="opacity-50"/>
            <ButtonDropdown
              button={{
                icon: "more_horiz",
                type: "neutral",
                minimal: true,
                className: "opacity-50 flex items-center justify-center",
                style: { width: "48px", height: "24px" },
              }}
              dropdown={{
                clickCloses: true,
                anchor: "left",
                content: breadcrumb
                  .map((crumb) => ({
                    text: crumb.name,
                    onClick: () => handleCrumbClick(crumb),
                  }))
                  .filter((crumb, index) => index !== breadcrumb.length - 1),
              }}
            />
            <Icon icon="keyboard_arrow_right" className="opacity-50"/>
            {breadcrumb[breadcrumb.length - 1] ? breadcrumb[breadcrumb.length - 1].name : ""}
          </div>
        )}
        {!hidden &&
          breadcrumb.map((crumb, index) => (
            <div className="flex items-center" key={crumb._id}>
              <Icon icon="keyboard_arrow_right" className="opacity-50"/>
              {breadcrumb.length === index + 1 ? (
                <div id={`crumb-${index}`} className='px-4 py-0.5'>{crumb.name}</div>
              ) : (
                <div className="opacity-50 cursor-pointer hover:bg-gray-300 rounded-full px-4 py-0.5" id={`crumb-${index}`} onClick={() => handleCrumbClick(crumb)}>
                  {crumb.name}
                </div>
              )}
            </div>
          ))}
      </div>
    </div>
  )
}

Breadcrumb.propTypes = {
  breadcrumb: PropTypes.array,
  onCrumbClick: PropTypes.func,
  onAllClick: PropTypes.func,
}

export default Breadcrumb
