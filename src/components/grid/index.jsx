import React, { useEffect, useState } from "react";
import FiltersHeader from "../filters_header";
import PaginationFooter from "../pagination_footer";
import Body from "./body";
import Status from "../status";

// Widths as defined by tailwind:
const SM = 640;
const MD = 768;
const LG = 1024;
const XL = 1280;

function getDefaultBreakpoints(isPoster) {
  // These breakpoints need to match the number of items per row in the className definition..
  return isPoster
    ? { 0: 2, [SM]: 3, [MD]: 4, [LG]: 5, [XL]: 6 }
    : { 0: 2, [MD]: 2, [LG]: 3, [XL]: 5 };
}

export default function Grid({ header, status, body, paginate, isPoster = false }) {
  const [breakpoints, setBreakpoints] = useState(
    getDefaultBreakpoints(isPoster)
  );

  useEffect(() => {
    setBreakpoints(getDefaultBreakpoints(isPoster));
  }, [isPoster]);

  return (
    <div className="space-y-4">
      <FiltersHeader isGrid {...header} />
      <Status {...status}>
        <Body body={body} isPoster={isPoster} />
      </Status>
      {paginate && (
        <PaginationFooter {...paginate} isGrid breakpoints={breakpoints} />
      )}
    </div>
  );
}
