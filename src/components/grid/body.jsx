import React from "react";
export default function Body({ body, isPoster = false }) {
  return (
    <div
      className={`grid gap-4
      ${
        isPoster
          ? "xl:grid-cols-6 lg:grid-cols-5 md:grid-cols-4 sm:grid-cols-3 grid-cols-2"
          : "xl:grid-cols-5 lg:grid-cols-3 md:grid-cols-2 grid-cols-2"
      }`}
    >
      {body.data.map((item, index) => body.render(item, index))}
    </div>
  );
}
