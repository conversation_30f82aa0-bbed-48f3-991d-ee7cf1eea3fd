import React, { useState } from 'react'
import Icon from '../icon'
import { Copy, Hidden, Visible } from '../icons'
import { iconClassBySize } from "../icons/icon_size"
import MaskedInput from 'react-text-mask'
import PhoneInputWithCountrySelect from 'react-phone-number-input'
import 'react-phone-number-input/style.css'

const InputField = ({
  inputClassName = "",
  innerRef = null,
  value = "",
  placeholder = "",
  type = "text",
  min = null,
  max = null,
  onChange = () => {},
  onBlur = () => {},
  disabled = false,
  success = false,
  error = false,
  inputWidth = "w-full",
  inputHeight = "h-10",
  minInputHeight = "",
  input = null,
  textarea = false,
  resize = false,
  copyIcon = false,
  mask = null,
  creditCard = false,
  phoneNumber = false,
  hidden = false,
  border = true,
  borderClassName = "",
  ...props
}) => {
  let leftIcon = input?.leftIcon
  let leftIconSize = input?.leftIcon?.size || "large"
  let rightIcon = input?.rightIcon
  let rightIconSize = input?.rightIcon?.size || "large"
  let rightIconCallback = input?.rightIcon?.onClick || (() => {})
  let [hideText, setHideText] = useState(hidden)
  const hiddenIcon = { icon: Visible, tooltip: { text: 'Click to show' }}
  const visibleIcon = { icon: Hidden, tooltip: { text: 'Click to hide' } }

  if (hideText) {
    type = "password"
  }

  if (copyIcon) {
    rightIcon = {
      icon: Copy,
      tooltip: { copy: { } }
    }
  }
  if (leftIcon?.tooltip?.copy) {
    leftIcon.icon = Copy
    if (!leftIcon.tooltip.copy.value) {
      leftIcon.tooltip.copy.value = value
    }
  }
  if (rightIcon?.tooltip?.copy) {
    rightIcon.icon = Copy
    if (!rightIcon.tooltip.copy.value) {
      rightIcon.tooltip.copy.value = value
    }
  }
  const sharedInputClasses = `px-3 border-0 w-full h-full text-sm box-border focus:outline-none focus:text-gray-600 dark:focus:text-gray-200 dark:bg-gray-800
    ${rightIcon && 'pr-2'} ${leftIcon && 'pl-2'} ${inputClassName}`
  return (
    <div className={`
      ${inputClassName ? inputClassName : disabled ? 'bg-gray-100' : 'bg-white dark:bg-gray-800'}
      flex w-full rounded-lg focus:text-gray-600 dark:focus:text-gray-200 overflow-hidden ${inputWidth}
      ${border ? 'border-solid border border-gray-300 focus:border-blue-500' : ''}
      ${borderClassName}
      ${disabled ? 'border-gray-100 text-gray-400 cursor-not-allowed' : 'hover:border-gray-600'}
      ${success ? 'border-green-500 hover:border-green-600' : ''} 
      ${error ? 'border-red-500 hover:border-red-600' : ''}
      ${!resize ? inputHeight : ''}
      ${textarea ? 'items-start py-3 pr-2' : 'items-center'}
      ${rightIcon ? 'pr-2' : ''} ${leftIcon ? 'pl-2' : ''}
    `}>
      {leftIcon && <Icon {...leftIcon} className={`${iconClassBySize[leftIconSize]}`}/>}
      {textarea
        ?
          <textarea
            value={value}
            placeholder={placeholder}
            onChange={e => onChange(e)}
            onBlur={e => onBlur(e)}
            disabled={disabled}
            className={`${minInputHeight} ${sharedInputClasses} ${!resize && 'resize-none'}`}
          />
        : mask
          ?
            <MaskedInput
              value={value}
              placeholder={placeholder}
              type={type}
              min={min}
              max={max}
              onChange={e => onChange(e)}
              onBlur={e => onBlur(e)}
              disabled={disabled}
              mask={mask}
              className={`py-1 ${sharedInputClasses}`}
            />
          : phoneNumber
            ?
              <PhoneInputWithCountrySelect
                value={value}
                placeholder={placeholder}
                type={type}
                onChange={e => onChange(e)}
                onBlur={e => onBlur(e)}
                disabled={disabled}
                className={`py-1 ${sharedInputClasses}`}
                {...props}
              />
            :
              <input
                ref={innerRef}
                value={value}
                placeholder={placeholder}
                type={type}
                min={min}
                max={max}
                onChange={e => onChange(e)}
                onBlur={e => onBlur(e)}
                disabled={disabled}
                className={`py-1 ${sharedInputClasses}`}
                {...props}
              />
      }
     {(rightIcon || hidden) && <div className={`flex flex-row space-x-2 pl-2 ${!copyIcon && !rightIcon && 'pr-2'}`} onClick={() => rightIconCallback()}>
        {hidden && hideText && <Icon {...hiddenIcon} className={`${iconClassBySize[rightIconSize]}`} onClick={() => setHideText(false)}/>}
        {hidden && !hideText && <Icon {...visibleIcon} className={`${iconClassBySize[rightIconSize]}`} onClick={() => setHideText(true)}/>}
        {rightIcon && <Icon {...rightIcon} className={`${iconClassBySize[rightIconSize]}`}/>}
      </div>}
    </div>
  )
}

export default InputField
