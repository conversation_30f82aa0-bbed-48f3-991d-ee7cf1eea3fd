import React from 'react'
import Icon from '../icon'
import { iconClassBySize } from "../icons/icon_size"

const Label = ({
  label = null, // string or { text: "Label", icon: { ... } }
  iconPosition = "right",
  iconSize = "medium"
}) => {
  const labelText = typeof label === "string" ? label : label?.text
  const icon = label?.icon
  const textColor = label?.textColor || "text-primary-900 dark:text-gray-200"
  iconPosition = icon?.position || iconPosition
  iconSize = icon?.size || iconSize
  return (
    <label className={`flex items-center ${textColor}`}>
      <div className={`flex items-start  ${iconPosition === "right" && 'flex-row-reverse'}`}>
        {Array.isArray(icon)
          ? icon.map((i) => <Icon {...i} className={`${iconClassBySize[iconSize]}`}/>)
          : icon && <Icon {...icon} className={`${iconClassBySize[iconSize]}`}/>}
        <span className={`text-xs leading-4
          ${icon ? (iconPosition === "right" ? "pr-1" : "pl-2") : ""}`}>
          {labelText}
        </span>
      </div>
    </label>
  )
}

export default Label
