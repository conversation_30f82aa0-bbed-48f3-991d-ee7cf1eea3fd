import React from 'react'
import Label from './label'
import Input from './input_field'

const InputWrapper = ({
  id,
  label = null,
  description = "",
  message = "",
  width = "w-full",
  className = "",
  ...props
}) => {
  return (
    <div id={id} className={`${width} ${className} space-y-1 flex flex-col`}>
      {label && <Label label={label} {...props}/>}
      {description && <Label label={description} textColor={'text-gray-500 dark:text-gray-200'}/>}
      <Input {...props}/>
      {message && <Label label={message} iconPosition="left" textColor={'text-gray-500 dark:text-gray-200'}/>}
    </div>
  )
}

export default InputWrapper
