@import './styles/fonts/clash-display/css/clash-display.css';
@import './styles/fonts/clash-grotesk/css/clash-grotesk.css';
@import './styles/fonts/inter/inter.css';

@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
    body {
        @apply font-content !important;
    }

    h1 {
        @apply text-[64px] !important;
        @apply leading-[80px] !important;
    }

    h2 {
        @apply text-[48px] !important;
        @apply leading-[64px] !important;
    }

    h3 {
        @apply text-[40px] !important;
        @apply leading-[48px] !important;
    }

    h4 {
        @apply text-[32px] !important;
        @apply leading-[40px] !important;
    }

    h5 {
        @apply text-[18px] !important;
        @apply leading-[24px] !important;
    }

    h6 {
        @apply text-base !important;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        @apply font-header;
        @apply font-semibold !important;
    }

    svg {
        @apply inline-flex;
    }

    .sortable-chosen {
        @apply bg-primary-100 rounded-lg border-4 border-gray-50;
    }

    .react-datepicker {
        @apply font-content !important;
    }

    .react-datepicker__header {
        @apply bg-white !important;
    }

    .react-datepicker__day-name {
        @apply text-gray-600 !important;
    }

    .react-datepicker__day--today {
        @apply bg-gray-100 !important;
    }

    .react-datepicker-time__header {
        @apply text-sm font-semibold !important;
    }

    .react-datepicker__time-container {
        width: 120px !important;
    }

    .react-datepicker__time-box {
        width: 120px !important;
    }

    .react-datepicker__time-list {
        @apply h-[264px] !important;
    }

    .react-datepicker__day-name,
    .react-datepicker__day {
        @apply h-10 w-10 inline-flex items-center justify-center m-0 rounded-none !important;
    }

    .react-datepicker__day--in-range {
        @apply bg-primary-200 !important;
    }

    .react-datepicker__close-icon::after,
    .react-datepicker__day--selected,
    .react-datepicker__time-list-item--selected {
        @apply bg-primary-500 !important;
    }

    .react-datepicker__time-list-item {
        @apply border-r border-gray-400;
    }

    .react-datepicker__day--keyboard-selected {
        @apply bg-primary-200 !important;
    }

    .react-datepicker__triangle {
        @apply invisible !important;
    }

    .tether-element {
        z-index: 10 !important;
    }

    .PhoneInputInput {
        @apply flex-1;
        @apply min-w-0;
        @apply text-sm;
        @apply focus-visible:outline-none;
        @apply focus:text-gray-600;
    }
}
