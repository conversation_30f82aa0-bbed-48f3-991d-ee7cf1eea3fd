import React, { useState, useEffect } from "react";
import Icon from "../icon";
import ButtonDropdown from "../button_dropdown";
import Checkbox from "../checkbox";

function ImageRender({ url, isThumbnail, isPoster, lightBackground, emptyIcon }) {
  const [valid, setValid] = useState(false);
  useEffect(() => {
    var image = new Image();
    image.onload = function () {
      if (this.width > 0) {
        setValid(true);
      }
    };
    image.src = url;
  }, []);

  if (url && valid) {
    return (
      <img
        src={url}
        className={`rounded object-cover ${
          isThumbnail ? "w-full aspect-video" : isPoster ? "h-full" : ""
        }`}
      />
    );
  }

  // Only render placeholder if this is a specific type of image
  if (!isThumbnail && !isPoster) {
    return null;
  }

  return (
    <div
      className={`${
        isThumbnail
          ? "w-full aspect-video"
          : isPoster
          ? "h-full aspect-[2/3]"
          : ""
      } rounded bg-gray-${lightBackground ? '100' : '800'} flex items-center justify-center`}
    >
      <Icon icon={emptyIcon} className={`${lightBackground ? 'text-gray-500' : 'text-white'} opacity-70`} />
    </div>
  );
}

export default function ImageClass({
  url,
  clickFunction,
  clickText,
  title,
  description,
  descriptionIcon,
  buttonDropdownContent = null,
  cover = true,
  selected = false,
  onSelect = null,
  isThumbnail = null,
  isPoster = null,
  lightBackground = false,
  emptyIcon = 'movie'
}) {
  return (
    <div className={`${cover ? "flex flex-col" : null}`}>
      <div
        className={`group flex items-start relative ${
          isThumbnail ? "aspect-video" : isPoster ? "aspect-[2/3]" : null
        }`}
      >
        <ImageRender url={url} isThumbnail={isThumbnail} isPoster={isPoster} lightBackground={lightBackground} emptyIcon={emptyIcon} />
        {title && cover ? (
          <div
            className={`z-20 rounded-b absolute bottom-0 h-10 items-center w-full bg-gradient-to-b from-transparent to-black`}
          >
            <div className="h-full flex items-center px-2 text-xs text-white transition-all">
              <div className="truncate">{title}</div>
            </div>
          </div>
        ) : null}
        {clickFunction || onSelect || buttonDropdownContent ? (
          <div className={`group rounded absolute inset-0`}>
            <div className="w-full h-full invisible group-hover:visible bg-black/50 z-10 rounded flex items-center justify-center cursor-pointer">
              {clickFunction ? (
                <div
                  className="bg-black/30 rounded w-full h-full flex text-center items-center justify-center"
                  onClick={() => clickFunction()}
                >
                  {clickText && (
                    <div className="text-white text-xs">{clickText}</div>
                  )}
                </div>
              ) : null}
            </div>
            {selected ? (
              <div className="absolute inset-0 border-primary-500 border-2 rounded" />
            ) : null}
            {onSelect || selected ? (
              <div
                className={`z-20 absolute top-4 left-4 group-hover:visible ${
                  selected ? "visible" : "invisible"
                }`}
              >
                <Checkbox checked={selected} onChange={onSelect} />
              </div>
            ) : null}
            {buttonDropdownContent ? (
              <div className="absolute top-2 right-2 invisible group-hover:visible ">
                <ButtonDropdown
                  button={{
                    className: "text-white hover:text-black",
                    minimal: true,
                    type: "neutral",
                    icon: "more_vert",
                  }}
                  dropdown={{
                    content: buttonDropdownContent,
                  }}
                />
              </div>
            ) : null}
          </div>
        ) : null}
      </div>
      {title && !cover ? (
        <div className="py-4">
          <div className="text-sm font-medium truncate">{title}</div>
            <div className="opacity-50 flex items-center space-x-2">
              {descriptionIcon && <Icon className='text-xl' icon={descriptionIcon}/>}
              {description ? (
                <div className="text-xs truncate">{description}</div>
              ) : null}
            </div>
        </div>
      ) : null}
    </div>
  );
}
