import * as React from "react";

interface Props {
    className?: string
    header: string
    subheader?: string
    confirmButton?: {
        text: string
        onClick: () => void
    }
    secondaryButton?: {
        text: string
        tertiary: boolean
        onClick: () => void
    }
    onClose?: () => void
    // <p>MODAL CONTENT</p>
}

declare const Modal: React.FunctionComponent<Props>

export default Modal
