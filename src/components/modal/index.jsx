import React, { useState, useEffect } from 'react'
import <PERSON>actD<PERSON> from 'react-dom'
import <PERSON>ton from '../button'
import Status from '../status'
import Icon from '../icon'

const Modal = ({
  header = null,
  subheader = null,
  onClose = null,
  confirmButton = null,
  secondaryButton = null,
  children,
  className = "",
  pending = false,
  minWidth = true
}) => {

  const [closing, setClosing] = useState(false)

  const close = e => {
    setClosing(true)
    setTimeout(() => {
      onClose(e)
    }, 200)
  }

  const onKeyDown = e => {
    if (e.keyCode === 27) {
      close(e)
    }
  }

  useEffect(() => {
    document.addEventListener("keydown", onKeyDown, false)
    return () => {
      document.removeEventListener("keydown", onKeyDown, false)
    }
  })

  const Header = () => (
    <div className={`
      ${subheader ? 'min-h-[96px]' : 'min-h-[72px]'}
      flex items-center px-6 font-header
      border-b text-gray-900 border-gray-200 dark:text-gray-200 dark:border-gray-600
    `}>
      <div className='flex flex-col w-full'>
        <div className='h-6 text-lg font-semibold flex w-full items-center justify-between'>
          {header}
          {typeof onClose === 'function' &&
            <Icon
              icon='close'
              className='text-gray-600 dark:text-gray-200'
              onClick={close}/>}
        </div>
        {subheader && <div className='h-6 text-gray-700 dark:text-gray-200 block'>{subheader}</div>}
      </div>
    </div>
  )

  const Footer = () => {
    if (!confirmButton || typeof confirmButton.onClick !== 'function') {
      return null
    }
    return (
      <div className={`min-h-[88px] min-w-48 px-6 flex items-center justify-between border-t
        border-gray-200 dark:border-gray-600
      `}>
        <Button tertiary type='neutral' disabled={pending} onClick={close}>Cancel</Button>
        <div>
          {secondaryButton && typeof secondaryButton.onClick === 'function' &&
            <Button {...secondaryButton} disabled={pending || secondaryButton.disabled} className='mr-2'>{secondaryButton.text}</Button>}
          {confirmButton.text &&
            <Button {...confirmButton} disabled={pending || confirmButton.disabled}>{confirmButton.text}</Button>}
        </div>
      </div>
    )
  }

  const content = (
    <div
      className={`
        ${closing ? 'animate-fade-out' : 'animate-fade-in'}
        overflow-auto fixed top-0 bottom-0 z-10 h-full w-full bg-overlay dark:bg-black/60 flex justify-center`}
      onClick={close}>
      <div
        className={`
          ${className} ${minWidth ? 'min-w-[550px]' : ''}
          max-w-5xl h-fit my-40 rounded shadow-lg
          bg-white dark:bg-gray-800
        `}
        onClick={e => e.stopPropagation() && e.preventDefault()}>
       <Header/>
        <div className='flex-auto p-6 dark:text-gray-200'>
          <Status pending={pending}>
            {children}
          </Status>
        </div>
        <Footer/>
      </div>
    </div>)
  return ReactDOM.createPortal(content, document.body)
}

export default Modal