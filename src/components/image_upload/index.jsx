import React from 'react'
import FileUpload from '../file_upload'

const uploadDefaults = {
  icon: "image",
  message: "Drag and drop or click here to upload",
  accept: "image/*",
  dropMessage: "Drop image here!",
}

const buttonDefaults = {
  text: "Upload image"
}

const ImageUpload = ({
  upload = null,
  button,
  className = null,
  image = null,
  includeRemoveButton = true
}) =>
  <FileUpload
    upload={{
      ...uploadDefaults,
      ...upload
    }}
    button={{
      ...buttonDefaults,
      ...button
    }}
    backgroundImage={image}
    includeRemoveButton={includeRemoveButton}
    className={className}/>

export default ImageUpload
