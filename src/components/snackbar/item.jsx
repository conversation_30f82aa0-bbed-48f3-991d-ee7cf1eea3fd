import React, { useEffect, useState, useCallback } from 'react'
import { InfoCircle, AlertTriangle, SolidAlertCircle, CheckCircle, Cross } from "../icons"
import Button from '../button'

const classByType = {
  info: "bg-info-100 border-info-500",
  success: "bg-success-100 border-success-500",
  warning: "bg-warning-100 border-warning-500",
  error: "bg-error-100 border-error-500"
}

const classByMessageType = {
  info: "text-info-900",
  success: "text-success-900",
  warning: "text-warning-900",
  error: "text-error-900"
}

const classByDescriptionType = {
  info: "text-info-700",
  success: "text-success-700",
  warning: "text-warning-700",
  error: "text-error-700"
}

const classByIconType = {
  info: "text-info-500",
  success: "text-success-500",
  warning: "text-warning-500",
  error: "text-error-500"
}

const iconByType = {
  info: InfoCircle,
  success: CheckCircle,
  warning: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  error: SolidAlertCircle
}

const SnackbarItem = ({
  _id,
  index,
  message = '',
  description = '',
  type = 'info',
  showIcon = false,
  icon = null,
  timeout = 3000,
  showCloseButton = false,
  onClose = null,
  buttons = null,
  onClick1 = null,
  onClick2 = null
}) => {
  const tickerTotal = timeout - 500
  const [show, setShow] = useState(false)
  const [ticker, setTicker] = useState(tickerTotal)
  const canClose = typeof onClose === 'function' || timeout === null || showCloseButton
  const Icon = iconByType[type]

  const close = useCallback(() => {
  	if (typeof onClose === 'function') {
  		onClose(_id)
  	}
  }, [onClose, _id])

  useEffect(() => {
    let timer = setTimeout(() => timeout && close(), timeout)
    return () => timer && clearTimeout(timer)
  }, [close, timeout])

  useEffect(() => {
    let timer = setTimeout(() => setShow(true), 0)
    return () => timer && clearTimeout(timer)
  }, [])

  useEffect(() => {
    let interval = setInterval(() => setTicker(t => t - 10), 10)
    return () => interval && clearInterval(interval)
  }, [])
  
  return (
    <div className={`w-[488px] flex flex-col ${classByType[type]} border drop-shadow-lg transition-all duration-1000 ${show ? '' : 'translate-y-full'}`}>
      <div className='flex p-4'>
        {showIcon && <div className={`h-6 flex items-center justify-center mr-3 ${classByIconType[type]}`}>
          <Icon className='w-4 h-4'/>
        </div>}
        <div className={`flex flex-col w-full items-center ${classByMessageType[type]}`}>
          <div className={'w-full flex items-center h-6 text-sm font-subheader font-normal'}>
            {message}
          </div>
          <div className={`w-full flex text-xs font-normal ${classByDescriptionType[type]}`}>
            {description}
          </div>
          {buttons && <div className={`w-full mt-2 flex items-center ${classByDescriptionType[type]}`}>
            {buttons.map((buttonText, i) =>
              <Button
                className={'mr-3'}
                size={'small'}
                type={type}
                tertiary={!(i === 0)}
                onClick={i === 0 ? onClick1 : onClick2}>
                {buttonText}
              </Button>
            )}
          </div>}
        </div>
        {canClose &&
          <span
            className={'h-6 cursor-pointer flex items-center justify-center'}
            onClick={close}>
            <Cross className={'h-4 w-4 text-gray-900'}/>
          </span>}
      </div>
      {timeout &&
        <div className={`${classByType[type]} border-b-2`} style={{ width: `${ticker / tickerTotal * 100}%` }}/>
      }
		</div>
	)
}

export default SnackbarItem
