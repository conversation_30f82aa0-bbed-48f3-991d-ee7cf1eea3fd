import React from 'react'
import ReactDOM from 'react-dom'
import SnackbarItem from './item'

const Snackbar = ({
  alignment = 'right',
  items = []
}) => {
    return (
      Array.isArray(items) && items.length
        ? ReactDOM.createPortal(
          <div className={`fixed z-50 bottom-4 ${alignment === 'center' ? 'w-full flex justify-center' : alignment === 'right' ? 'right-4': 'left-4'}`}>
            <div className='flex flex-col space-y-1'>
              {items.map(item =>
                <SnackbarItem
                  key={item._id}
                  {...item}/>
              )}
            </div>
          </div>,
          document.body)
        : null
    )
}

export default Snackbar
