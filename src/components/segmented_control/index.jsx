import React from 'react'
import <PERSON><PERSON> from "../button"

const SegmentedControl = ({
  options,
  value,
  onChange,
  label = null,
  size = 'medium',
  type = 'primary',
  className = "",
  secondary = false,
  disabled = false
}) => {
  return (
    <div className='space-y-1'>
      {label &&
        <label className="text-xs leading-4 text-primary-900 dark:text-gray-200">
          {label}
        </label>
      }
      <div
        className={`${className} ${disabled ? 'cursor-not-allowed' : ''} w-full flex p-1 rounded-lg bg-gray-50 border`}
        onClick={e => {
          if (disabled) {
            e.preventDefault()
            e.stopPropagation()
          }
        }}>
        {options.map((props, index) =>
          <Button
            key={index}
            className="w-1/2"
            size={size}
            type={props.value === value ? type : 'neutral'}
            secondary={secondary}
            minimal={props.value !== value}
            disabled={disabled}
            onClick={() => onChange(props.value)}>
            {props.label}
          </Button>
        )}
      </div>
    </div>
  )
}

export default SegmentedControl
