import React, { useState } from "react"
import PropTypes from "prop-types"
import copyToClipboard from 'copy-to-clipboard'
import ReactTooltip from 'react-tooltip'
import uuid from "react-uuid"

const CopyWrapper = ({
  text = "Copy",
  copy = null,
  direction = "right",
  children,
  includeTooltip = false,
  onClick = null
}) => {
  const [copied, setCopied] = useState(false)
  const value = copy ? copy.value : ""
  const onCopyMessage = copy?.onCopyMessage || "Copied!"
  const uniqueID = uuid()
  return (
    <span
      data-tip
      data-for={uniqueID}
      className='inline-flex'
      onClick={e => {
        if (typeof onClick === "function") {
          e.stopPropagation()
          e.preventDefault()
        }
        if (copy) {
          copyToClipboard(value)
          setCopied(true)
          setTimeout(() => {
            setCopied(false)
          }, 2500)
        }
      }}>
      {children}
      {includeTooltip && <ReactTooltip id={uniqueID} effect='solid' place={direction}>
        <span className='font-content font-normal'>{copied && !value ? "Nothing to copy" : copied ? onCopyMessage : text}</span>
      </ReactTooltip>}
    </span>
  )
}

const InnerIcon = ({ icon, iconClassName, className, outlined = false, style = {}, fontSize, onClick, tooltip = null }) => {
  const pointer = (typeof onClick === "function" || tooltip?.copy) ? "cursor-pointer" : ""
  if (typeof icon === "string") {
    return (
      <div
        style={{
          ...style,
          fontSize,
          fontVariationSettings: outlined ? '' : `'FILL' 1`
        }}
        onClick={onClick}
        className={`${className}
          material-symbols-outlined
          ${pointer}
        `}>
        {icon}
      </div>
    )
  }
  const Icon = icon
  return (
    <Icon
      className={`${className} ${iconClassName} ${pointer} flex items-center`}
      onClick={onClick}/>
  )
}

const Icon = ({
  tooltip = null,
  ...props
}) => {
  return (
    <CopyWrapper {...tooltip} {...props} includeTooltip={tooltip}>
      <InnerIcon {...props} tooltip={tooltip}/>
    </CopyWrapper>
  )
}

Icon.propTypes = {
  tooltip: PropTypes.object,
  icon: PropTypes.oneOfType([PropTypes.object, PropTypes.string, PropTypes.func]),
  className: PropTypes.string,
  fontSize: PropTypes.string,
  onClick: PropTypes.func
}

export default Icon