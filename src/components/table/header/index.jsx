import React from 'react'
import ReactTooltip from 'react-tooltip'
import Checkbox from '../../checkbox'
import Icon from '../../icon'

const Cell = ({ i, width, text, columnSortKey = null, sorting = null }) => {
  let widthStyle = width === 'auto' ? '' : `${width}px`
  let canSort = columnSortKey && sorting && sorting.onSortChange
  let isSorted = sorting && sorting.key === columnSortKey
  let isAscending = sorting && sorting.direction === 'ASC'
  const tip = `sorting-by-${columnSortKey}`
  return (
    <th className={`${width === 'auto' ? 'flex-auto' : 'inline-flex'} font-normal text-xs`} style={{ width: widthStyle }} index={i}>
      <div
        className={`flex items-center w-full h-full ${canSort && 'cursor-pointer'}`}
        onClick={() => canSort && sorting && sorting.onSortChange({
          key: columnSortKey,
          direction: isSorted && isAscending ? 'DESC' : 'ASC'
        })}>
        <div data-tip data-for={tip} className='flex items-center'>
          {canSort ?
            <ReactTooltip id={tip} effect='solid' place='bottom'>
              Click to sort by <span className='lowercase'>{text}</span>
              {isAscending && isSorted ? ' descending' : ' ascending'}
            </ReactTooltip>
          : null}
          <span>{text}</span>
          {canSort &&
            <Icon
              className={`cursor-pointer ml-1
                ${isSorted ? 'text-gray-600' : 'text-gray-300'}
                ${isAscending && isSorted ? 'rotate-180' : ''}`}
              icon={'arrow_drop_down'}/>}
        </div>
      </div>
    </th>
  )
}

const Header = ({ columns = [], widths = [], checkbox = null, sorting = null, draggable = false }) =>
	<thead>
    <tr className='flex px-4 py-2 items-stretch text-gray-500 font-normal'>
      {draggable && <th className='w-6 mr-3'>&nbsp;</th>}
      {checkbox &&
        <th className='flex items-center'>
          <Checkbox
            size="small"
            className="mr-3"
            checked={checkbox.checked}
            indeterminate={checkbox.indeterminate}
            onChange={checkbox.onChange}/>
        </th>}
      {columns.map((cell, i) =>
        <Cell
          {...cell}
          i={i}
          key={i}
          width={widths[i]}
          columnSortKey={cell.key}
          sorting={sorting}/>)}
    </tr>
  </thead>

export default Header
