import React, { useRef, useState, useEffect } from 'react'
import FiltersHeader from '../filters_header'
import Header from './header'
import Body from './body'
import Pagination from '../pagination_footer'
import Status from '../status'

const scaleWidths = (columns, tableWidth, hasCheckboxColumn) => {

  // If there is an auto column present or no table width has been defined, return as is...
  if (columns.includes("auto") || !tableWidth) {
    return columns
  }

  // Actual amount of width available for columns accounting for margins and padding and the checkbox column.
  tableWidth = tableWidth - 34 - (hasCheckboxColumn ? 30 : 0)

  // Currently used width
  const currentWidth = columns.reduce((a, b) => a + b, 0)

  // Amount of width available to distribute
  const widthToShare = tableWidth - currentWidth

  // Identify number of columns that should scale (any column over 80 pixels, otherwise it might be an icon column)
  const growingColumns = columns.filter(column => column > 80)
  const growingColumnsWidth = growingColumns.reduce((a, b) => a + b, 0)

  // Build out the new columns based on percentage of available width.
  const newColumns = columns.map(column => {
    return column <= 80 ? column : column + (column / growingColumnsWidth) * widthToShare
  })

  return newColumns
}

const TableWrapper = ({ status, paginate, initialColumns, customSubheader, ...props }) => {
  const columnCount = props.widths ? props.widths.length : 0
  const tableIdentifier = props.header?.columns ? encodeURI(props.header.columns.map(opt => opt.text).join('-')) : null
  const storedColumns = window.localStorage.getItem(tableIdentifier)
  const configuredColumns = storedColumns ? JSON.parse(storedColumns) : (initialColumns ? initialColumns : [...Array(columnCount).keys()])
  const [activeColumns, setActiveColumns] = useState(configuredColumns)

  const storedColumnOrder = window.localStorage.getItem(tableIdentifier + '-order')
  const configuredColumnOrder = JSON.parse(storedColumnOrder)
  const [orderedColumnWidths, setOrderedColumnWidths] = useState(configuredColumnOrder ? configuredColumnOrder.map((i) => props?.widths[i]) : props?.widths)
  const [orderedColumnsHeader, setOrderedColumnHeader] = useState(configuredColumnOrder ? configuredColumnOrder.map((i) => props?.header?.columns[i]) : props?.header?.columns)
  const [orderedColumnBody, setOrderedColumnBody] = useState(configuredColumnOrder ? configuredColumnOrder.map((i) => props?.body?.row?.render[i]) : props?.body?.row?.render)

  const isCompactIdentifier = tableIdentifier + '-row-compact-setting'
  const storedRowCompactSetting = window.localStorage.getItem(isCompactIdentifier)
  const [isCompact, setIsCompact] = useState(storedRowCompactSetting !== null ? JSON.parse(storedRowCompactSetting) : props.body.row.compact)
  
  const isSpacedIdentifier = tableIdentifier + '-row-spaced-setting'
  const storedRowSpacedSetting = window.localStorage.getItem(isSpacedIdentifier)
  const [isSpaced, setIsSpaced] = useState(storedRowSpacedSetting !== null ? JSON.parse(storedRowSpacedSetting) : props.body.row.spaced)

  const swapElementOrder = (a, b, arr) => {
    [arr[a], arr[b]] = [arr[b], arr[a]]

    return arr
  }

  const replaceElement = (a, b, arr) => {
    let indexA = arr.indexOf(a)
    let indexB = arr.indexOf(b)

    if (indexA >= 0 && indexB < 0) {
      arr[indexA] = b
    } else if (indexA < 0 && indexB >= 0) {
      arr[indexB] = a
    }

    window.localStorage.setItem(tableIdentifier, JSON.stringify(arr))

    return arr
  }

  const swapColumnOrder = (a, b, orderArr) => {
    setOrderedColumnWidths(swapElementOrder(a, b, [...orderedColumnWidths]))
    setOrderedColumnHeader(swapElementOrder(a, b, [...orderedColumnsHeader]))
    setOrderedColumnBody(swapElementOrder(a, b, [...orderedColumnBody]))
    setActiveColumns(replaceElement(a, b, [...activeColumns]))

    window.localStorage.setItem(tableIdentifier + '-order', JSON.stringify(swapElementOrder(a, b, [...orderArr])))
  }

  return (
    <>
      <FiltersHeader
        {...props.header}
        activeColumns={activeColumns}
        setActiveColumns={columns => {
          window.localStorage.setItem(tableIdentifier, JSON.stringify(columns))
          setActiveColumns(columns)
        }}
        initialColumns={initialColumns}
        orderedColumns={orderedColumnsHeader}
        orderArr={configuredColumnOrder || [...Array(columnCount).keys()]}
        swapOrder={swapColumnOrder}
        isCompact={isCompact}
        toggleIsCompact={() => {
          window.localStorage.setItem(isCompactIdentifier, !isCompact)
          setIsCompact(!isCompact)
        }}
        isSpaced={isSpaced}
        toggleIsSpaced={() => {
          window.localStorage.setItem(isSpacedIdentifier, !isSpaced)
          setIsSpaced(!isSpaced)
        }}
        {...props}/>
      <Status {...status}>
        {customSubheader && customSubheader}
        <Table
          {...props}
          header={props.header ? {
            ...props.header,
            columns: props.header && orderedColumnsHeader ?
              orderedColumnsHeader.filter((render, index) => activeColumns.includes(index))
            : []
          } : null}
          body={{
            ...props.body,
            row: props.body && props.body.row ? {
              ...props.body.row,
              compact: isCompact,
              spaced: isSpaced,
              render: orderedColumnBody.filter((render, index) => activeColumns.includes(index))
            } : null
          }}
          widths={props.widths ? orderedColumnWidths.filter((width, index) => activeColumns.includes(index)) : []}/>
      </Status>
      {paginate && <Pagination {...paginate}/>}
    </>
  )
}

const Table = ({
  widths = [],
  header = {},
  body = {},
  className = '',
  draggable = false
}) => {
  const tableRef = useRef(null)
  const [tableWidth, setTableWidth] = useState(0)
  useEffect(() => {
    setTableWidth(tableRef.current?.offsetWidth)
  }, [tableRef])
  widths = scaleWidths(widths, tableWidth, !!header?.checkbox)
  return (
    <table className={`w-full font-content ${className}`} ref={tableRef}>
      {header && header.columns && ((body.data && body.data.length > 0) || !body.empty)
        && <Header {...header}
        widths={widths}
        draggable={draggable}
        hideOnEmpty={body.empty}/>}
      {body && <Body {...body}
        widths={widths}
        draggable={draggable}/>}
    </table>
  )
}

export default TableWrapper
