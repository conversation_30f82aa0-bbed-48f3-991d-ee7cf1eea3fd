import React, { useEffect, useLayoutEffect, useRef } from 'react'
import { useState } from 'react'
import Checkbox from '../../checkbox'
import { Drag<PERSON>andle } from '../../icons'
import Cell from './cell'
import { useNavigate } from 'react-router-dom'
import RowLink from './row_link'

const Row = ({
  tableData,
  data,
  checkbox = null,
  expansion = null,
  onClick = null,
  to = null,
  widths,
  compact = false,
  spaced = false,
  truncate = false,
  index,
  render,
  draggable = false,
  disabled = false,
  baseRowHeight,
  virtualItem = null,
  virtualizer,
  ...rest
}) => {
  let disabledCheckBox = checkbox && typeof checkbox.disabled === 'function' && checkbox.disabled(data, index)
  let disabledRow = disabled && typeof disabled === 'function' && disabled(data, index)

  let expansionEnabled = typeof expansion?.component === 'function' && expansion.component(data, index) !== null
  let expansionVisible = typeof expansion?.visible === 'function' ? expansion.visible(data, index) : false
  let expandableComponent = expansionEnabled && expansionVisible ? expansion.component(data, index) : null

  let clickable = typeof onClick === 'function' || expansionEnabled || to !== null

  const navigate = useNavigate()

  const resize = (height) => {
    if (!virtualItem) return
    height ? virtualizer.resizeItem(virtualItem, baseRowHeight + height) : virtualizer.resizeItem(virtualItem, baseRowHeight)
  }



  const handleRowClick = (event) => {
    if (disabledRow) {
      return false
    }

    if (typeof onClick === 'function') {
      onClick(event, data, index)
    }
  }

  const renderRow = () => <>
    {draggable && (
      <div className={`mr-3 cursor-grab`}>
        <DragHandle className={compact ? 'h-5 w-5' : 'h-6 w-6'} />
      </div>
    )}
    {checkbox && (
      <div onClick={(e) => e.stopPropagation()} className='flex items-center'>
        <Checkbox
          size='small'
          className='mr-3'
          disabled={disabledRow || disabledCheckBox}
          checked={checkbox.checked(data, index)}
          indeterminate={checkbox.indeterminate}
          onChange={(e) => checkbox.onChange(data, index)}
        />
      </div>
    )}
    {render.map((cell, i) => (
      <Cell i={i} key={i} width={widths[i]} truncate={truncate} draggable={draggable} {...rest} >
        {cell(tableData[index], index, { ...rest })}
      </Cell>
    ))}
  </>

  return (
    <tr className={`
      flex flex-col
      ${borderClass(spaced, index)}
      overflow-hidden
    `}>
      <td
        onClick={handleRowClick}
        className={`
          flex text-sm px-4 items-center dark:bg-primary-900 w-full
          overflow-hidden
          ${compact ? 'min-h-10 text-xs' : spaced ? 'min-h-16' : 'min-h-[73px]'}
          ${clickable && !disabledRow ? 'cursor-pointer hover:bg-gray-50' : ''}
          ${disabledRow
            ? 'cursor-not-allowed text-gray-300 dark:text-gray-700'
            : 'text-gray-700 bg-white dark:text-gray-200'}`}
        style={virtualItem && {
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          minHeight: `${virtualItem.size}px`,
          transform: `translateY(${virtualItem.start}px)`,
        }}>
        {to ? 
          <RowLink to={to} data={data}>{renderRow()}</RowLink>
          : renderRow()}
      </td>
      {expansionVisible ? <td className='p-0'>{expandableComponent}</td> : null}
    </tr>
  )
}

const borderClass = (spaced, index) => {
  let classes = [
    'border-b'
  ]
  if (index === 0) {
    classes.push('border-t')
  }
  if (spaced) {
    classes = [...classes, 'mb-2', 'rounded-lg', 'border', 'shadow']
  }
  return classes.join(' ')
}

export default Row
