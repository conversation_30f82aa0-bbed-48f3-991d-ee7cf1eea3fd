import React, { useRef } from "react"
import Empty from "./empty"
import Row from "./row"
import { ReactSortable } from "react-sortablejs"
import { useVirtualizer } from '@tanstack/react-virtual'

const Body = ({ empty, data, row, widths, draggable = false, onDragChange = () => {}, onDragStart = () => {}, onDragEnd = () => {} }) => {
  const baseRowHeight = row.compact ? 40 : row.space ? 64 : 73
  const parentRef = useRef(null)
  const virtualizer = useVirtualizer({
    count: data.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => baseRowHeight,
    overscan: 5,
  })

  if (data.length === 0) {
    return empty && <Empty {...empty} />
  }

  // manually sort the list based on starting and ending index, and return the result. Should remove the need for manual state tracking of list order
  const handleDragEnd = (event) => {
    const { oldIndex, newIndex } = event
    if (oldIndex === newIndex) return
    const updatedData = [...data]
    const [movedItem] = updatedData.splice(oldIndex, 1)
    updatedData.splice(newIndex, 0, movedItem)
    onDragEnd(updatedData)
  }

  const renderRow = (line, index, virtualItem) => (
    <Row
      key={index}
      tableData={data}
      data={line}
      index={index}
      line={line}
      widths={widths}
      draggable={draggable}
      virtualItem={virtualItem}
      virtualizer={virtualizer}
      baseRowHeight={baseRowHeight}
      {...row}
    />
  )

  return draggable ? (
    <ReactSortable
      tag={`tbody`}
      list={data}
      setList={onDragChange}
      onStart={onDragStart}
      onEnd={handleDragEnd}
      handle={`.cursor-grab`}
      chosenClass={`sortable-chosen`}
    >
      {data.map((line, index) => renderRow(line, index))}
    </ReactSortable>
  ) : <tbody>
    {data.map((line, index) => renderRow(line, index))}
  </tbody>
  
  // TODO: fix virtual items array empty in firefox
  // <tbody ref={parentRef} style={{ position: 'relative', height: `${virtualizer.getTotalSize()}px` }}>
  //     {virtualizer.getVirtualItems().map(virtualItem => renderRow(data[virtualItem.index], virtualItem.index, virtualItem))}
  //   </tbody> 
}

export default Body
