import React  from 'react'

const Cell = ({ i, width, children, truncate = false }) => {
  let widthStyle = width === 'auto' ? '' : `${width}px`
  return (
    <div
      index={i}
      className={`pr-4 items-center ${width === 'auto' ? 'flex-auto' : 'inline-flex'}`}
      style={{ minWidth: widthStyle, width: widthStyle }}>
      {truncate ? <div className='truncate h-full items-center'>{children}</div> : children}
    </div>
  )
}

export default Cell
