import * as React from "react";

interface Options {
    key: string
    label: string
}

interface TableHeader {
    columns?: [{
        text: string,
        key?: string
    }]
    sorting?: {
        options: [Options]
        onSortChange: () => void
        direction: string
        key: string
        sortingkey: string
    }
    searching?: {
        search: string
        searchPlaceholder: string
        onSearch: () => void
    }
    filtering?: {
        options: {
            label: string
            key: string
            type: string
            searchable: boolean
            options: [Options]
        }
    }
    checkbox?: {
        checked: boolean
        indeterminate: boolean
        onChange: () => void
    }
}

interface Props {
    className? : string
    widths?: [number]
    header: TableHeader
    initialColumns?: [number]
    body: {
        data: [Record<string, unknown>]
        row: {
            compact: boolean
            checkbox?: {
                checked: boolean
                onChange: () => void
            }
            onClick?: () => void
            render: [() => string | string | JSX.Element]
            empty?: {
                title: string
                text: string
                icon: string
            }
        }
    }
    paginate?: {
        totalRows: number
        currentPage: number
        rowsPerPage: number
        onPageChange: () => void
        onRowsPerPageChange: () => void
    }
}


declare const Table: React.FunctionComponent<Props>

export default Table
