name: CD

on:
  push:
    branches:
      - main

jobs:
  npm-publish:
    env:
      INPUT_TOKEN: ${{ secrets.NPM_TOKEN }}
    runs-on: ubuntu-latest

    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v2
        with:
          persist-credentials: false

      - name: Install dependencies 💿
        run: yarn install

      - name: Run build 🛠️
        run: yarn npm-build

      - name: Publish
        uses: JS-DevTools/npm-publish@v1
        with:
          token: ${{ secrets.NPM_TOKEN }}
          access: "restricted"

      - name: Run build for GH Pages 🛠️
        run: yarn build

      - name: Deploy to GH Pages 🚀
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: dist # The folder the action should deploy.
