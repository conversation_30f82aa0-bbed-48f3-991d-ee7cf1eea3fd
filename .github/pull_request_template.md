**Type of PR:**

<!-- Delete the categories that do not apply -->

&check; Issue

&check; Feature

&check; Fix

&check; Housekeeping

&check; Other:

---

[**Notion Ticket**](insert link here)

---

**Description of PR**

<!-- Include a description of the issue and what you did to resolve it. -->

[insert text here]

---

**Checklist**

<!-- To check an item fill the brackets with the letter `x`, the result should be `[x]`. -->

- [ ] Assign every member of dev team as `Reviewers`
- [ ] Assign yourself under `Assignees`
- [ ] PR is opened against the default / `dev` branch
- [ ] Increment version number (if pushing to main) / N/A
