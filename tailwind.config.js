const defaultTheme = require('tailwindcss/defaultTheme')
import colors from './colors'

module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  darkMode: "class",
  theme: {
    fontFamily: {
      "header": ["ClashDisplay-Variable", ...defaultTheme.fontFamily.sans],
      "subheader": ["ClashGrotesk-Normal", ...defaultTheme.fontFamily.sans],
      "content": ["Inter", ...defaultTheme.fontFamily.sans],
      "sans-serif": "sans-serif"
    },
    screens: {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
    },
    extend: {
      fontSize: {
        "2xs": "0.6rem",
      },
      keyframes: {
        "scale-accordion": {
          '0%': { transform: 'scaleY(0)', opacity: 0 },
          '100%': { transform: 'scaleY(1)', opacity: 1 },
        },
        "fade-in": {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 }
        },
        "fade-out": {
          '0%': { opacity: 1 },
          '100%': { opacity: 0 }
        }
      },
      animation: {
        "scale-accordion": "scale-accordion .2s linear forwards",
        "fade-in": "fade-in .2s ease-in forwards",
        "fade-out": "fade-out .2s ease-in forwards",
        "spin-slow": "spin 2s linear infinite",
      },
      colors
    },
  }
}
