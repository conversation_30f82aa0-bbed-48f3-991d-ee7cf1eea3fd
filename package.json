{"name": "@bitcine/cinesend-theme", "version": "0.3.79", "main": "dist/index.js", "module": "dist/index.js", "homepage": "https://bitcine.github.io/cinesend-theme/", "types": "src/components/types.d.ts", "dependencies": {"@googlemaps/js-api-loader": "^1.14.3", "@tanstack/react-virtual": "^3.2.1", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@uppy/aws-s3": "^3.3.1", "@uppy/core": "^3.3.1", "@uppy/xhr-upload": "^3.3.1", "@vitejs/plugin-react": "^2.1.0", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.7", "file-type": "^19.6.0", "gh-pages": "^3.2.3", "react-datepicker": "^6.9.0", "react-dropzone": "^14.2.3", "react-highlight-words": "^0.18.0", "react-payment-inputs": "^1.1.9", "react-phone-number-input": "^3.2.11", "react-select": "^5.2.2", "react-sortable-hoc": "^2.0.0", "react-sortablejs": "^6.1.1", "react-tether": "^3.0.1", "react-text-mask": "^5.4.3", "react-tooltip": "^4.5.0", "react-uuid": "^1.0.2", "react-window": "^1.8.10", "recharts": "^2.12.5", "sortablejs": "^1.14.0", "styled-components": "^5.3.5", "tailwindcss": "^3.4.13", "text-mask-addons": "^3.8.0", "vite": "5.2.6", "web-vitals": "^2.1.3"}, "scripts": {"dev": "vite", "build": "vite build", "gh-deploy": "npm run build && gh-pages -d dist", "npm-build": "rm -rf dist && mkdir dist && NODE_ENV=production babel src/components -d dist --copy-files"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/cli": "^7.16.7", "autoprefixer": "^10.4.2", "babel-preset-react-app": "^10.0.1", "react": "^18.1.0", "react-dom": "^18.1.0", "react-router-dom": "^6.3.0", "typescript": "^4.8.3"}, "babel": {"presets": ["@babel/react"], "plugins": [["@babel/plugin-proposal-class-properties", {"loose": true}]]}}